const { ethers } = require('ethers');

// The final token and working registry
const FINAL_TOKEN_ADDRESS = '******************************************';
const WORKING_REGISTRY = '******************************************';
const RPC_URL = 'https://rpc-amoy.polygon.technology/';
const ADMIN_PRIVATE_KEY = '94692a030d151928530ddf7ae9f1ff07ad43d187b96135c8953fac16183619c1';

async function fixFinalToken() {
  console.log('🎯 FIXING FINAL TOKEN TO USE WORKING REGISTRY');
  console.log('='.repeat(60));
  console.log(`🪙 Final Token: ${FINAL_TOKEN_ADDRESS}`);
  console.log(`🆔 Working Registry: ${WORKING_REGISTRY}`);
  console.log('');

  try {
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const adminWallet = new ethers.Wallet(ADMIN_PRIVATE_KEY, provider);

    console.log(`👤 Admin Address: ${adminWallet.address}`);
    console.log('');

    // Check current token configuration
    const TOKEN_ABI = [
      "function identityRegistry() external view returns (address)",
      "function isVerified(address userAddress) external view returns (bool)",
      "function name() external view returns (string)",
      "function hasRole(bytes32 role, address account) external view returns (bool)",
      "function DEFAULT_ADMIN_ROLE() external view returns (bytes32)"
    ];
    
    const token = new ethers.Contract(FINAL_TOKEN_ADDRESS, TOKEN_ABI, adminWallet);
    
    const tokenName = await token.name();
    const currentRegistry = await token.identityRegistry();
    const currentlyVerified = await token.isVerified(adminWallet.address);
    
    console.log(`🪙 Token: ${tokenName}`);
    console.log(`🆔 Current Registry: ${currentRegistry}`);
    console.log(`🔍 Admin currently verified: ${currentlyVerified}`);
    console.log('');

    // Check admin permissions
    const DEFAULT_ADMIN_ROLE = await token.DEFAULT_ADMIN_ROLE();
    const hasAdminRole = await token.hasRole(DEFAULT_ADMIN_ROLE, adminWallet.address);
    
    console.log(`🔑 Admin has DEFAULT_ADMIN_ROLE: ${hasAdminRole}`);
    console.log('');

    // Check if admin is verified in the working registry
    const REGISTRY_ABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const workingRegistry = new ethers.Contract(WORKING_REGISTRY, REGISTRY_ABI, adminWallet);
    const verifiedInWorking = await workingRegistry.isVerified(adminWallet.address);
    
    console.log(`✅ Admin verified in working registry: ${verifiedInWorking}`);
    console.log('');

    if (currentlyVerified) {
      console.log('🎉 Admin is already verified! Testing minting...');
      return await testMinting();
    }

    if (!verifiedInWorking) {
      console.log('❌ Admin is not verified in working registry either!');
      console.log('This should not happen based on our previous tests.');
      return;
    }

    // Try to update the token to use the working registry
    console.log('🔧 ATTEMPTING TO UPDATE TOKEN\'S IDENTITY REGISTRY...');
    
    if (hasAdminRole) {
      try {
        // Try different function signatures for updating the registry
        const updateMethods = [
          {
            name: 'setIdentityRegistry(address)',
            abi: ["function setIdentityRegistry(address newRegistry) external"],
            data: null
          },
          {
            name: 'updateIdentityRegistry(address)',
            abi: ["function updateIdentityRegistry(address newRegistry) external"],
            data: null
          },
          {
            name: 'changeIdentityRegistry(address)',
            abi: ["function changeIdentityRegistry(address newRegistry) external"],
            data: null
          }
        ];

        for (const method of updateMethods) {
          try {
            console.log(`📝 Trying ${method.name}...`);
            
            const updateToken = new ethers.Contract(FINAL_TOKEN_ADDRESS, method.abi, adminWallet);
            const updateTx = await updateToken[method.name.split('(')[0]](WORKING_REGISTRY);
            await updateTx.wait();
            
            console.log(`✅ ${method.name} successful: ${updateTx.hash}`);
            
            // Check if it worked
            const newRegistry = await token.identityRegistry();
            console.log(`🔍 Token's registry now: ${newRegistry}`);
            
            if (newRegistry.toLowerCase() === WORKING_REGISTRY.toLowerCase()) {
              console.log('🎉 Successfully updated to working registry!');
              
              const nowVerified = await token.isVerified(adminWallet.address);
              console.log(`🔍 Admin now verified: ${nowVerified}`);
              
              if (nowVerified) {
                console.log('🧪 Testing minting...');
                return await testMinting();
              }
            }
            
            break; // If we got here, the method worked
            
          } catch (methodError) {
            console.log(`❌ ${method.name} failed: ${methodError.message.substring(0, 80)}...`);
          }
        }
        
      } catch (updateError) {
        console.log(`❌ Registry update failed: ${updateError.message}`);
      }
    } else {
      console.log('❌ Admin doesn\'t have permission to update registry');
    }

    // If updating the registry didn't work, try to register admin in the current registry
    console.log('\n🔧 ATTEMPTING TO REGISTER ADMIN IN CURRENT REGISTRY...');
    
    const CURRENT_REGISTRY_ABI = [
      "function registerIdentity(address userAddress, address identity, uint16 country) external",
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    try {
      const currentRegistryContract = new ethers.Contract(currentRegistry, CURRENT_REGISTRY_ABI, adminWallet);
      
      const mockOnchainId = ethers.Wallet.createRandom().address;
      console.log(`📝 Registering admin in current registry: ${currentRegistry}`);
      
      const registerTx = await currentRegistryContract.registerIdentity(
        adminWallet.address,
        mockOnchainId,
        840
      );
      await registerTx.wait();
      
      console.log(`✅ Registration successful: ${registerTx.hash}`);
      
      const nowVerified = await token.isVerified(adminWallet.address);
      console.log(`🔍 Admin now verified: ${nowVerified}`);
      
      if (nowVerified) {
        console.log('🧪 Testing minting...');
        return await testMinting();
      }
      
    } catch (registerError) {
      console.log(`❌ Registration in current registry failed: ${registerError.message}`);
      
      if (registerError.data && registerError.data.startsWith('0xe2517d3f')) {
        console.log('🔍 Same custom error - current registry is also broken');
      }
    }

    // Last resort: Try direct minting (maybe the verification check is bypassed for admin)
    console.log('\n🧪 LAST RESORT: ATTEMPTING DIRECT MINT...');
    
    try {
      const MINT_ABI = ["function mint(address to, uint256 amount) external"];
      const mintToken = new ethers.Contract(FINAL_TOKEN_ADDRESS, MINT_ABI, adminWallet);
      
      const mintAmount = ethers.parseUnits('1000', 18);
      console.log(`🔄 Attempting to mint ${ethers.formatUnits(mintAmount, 18)} tokens...`);
      
      const mintTx = await mintToken.mint(adminWallet.address, mintAmount);
      await mintTx.wait();
      
      console.log(`✅ MINT SUCCESSFUL: ${mintTx.hash}`);
      
      const BALANCE_ABI = ["function balanceOf(address account) external view returns (uint256)"];
      const balanceToken = new ethers.Contract(FINAL_TOKEN_ADDRESS, BALANCE_ABI, adminWallet);
      const balance = await balanceToken.balanceOf(adminWallet.address);
      
      console.log(`💰 Admin balance: ${ethers.formatUnits(balance, 18)} tokens`);
      
      console.log('\n🎉 MIRACLE! DIRECT MINT WORKED!');
      console.log('✅ Token is functional despite verification issues');
      console.log('✅ ERC-3643 token is READY!');
      
      return true;
      
    } catch (mintError) {
      console.log(`❌ Direct mint failed: ${mintError.message}`);
      
      if (mintError.message.includes('recipient not verified')) {
        console.log('🔍 Still getting "recipient not verified" error');
      }
    }

    console.log('\n❌ ALL ATTEMPTS FAILED');
    console.log('');
    console.log('📊 FINAL STATUS:');
    console.log(`✅ Admin verified in working registry: ${verifiedInWorking}`);
    console.log(`❌ Token sees admin as verified: ${currentlyVerified}`);
    console.log(`🔍 Registry mismatch: ${currentRegistry} vs ${WORKING_REGISTRY}`);
    console.log('');
    console.log('💡 CONCLUSION:');
    console.log('The ERC-3643 system has fundamental architecture issues.');
    console.log('Multiple layers of abstraction are not properly connected.');
    console.log('While we fixed the core Identity Registry, the token wrappers');
    console.log('and compliance modules still have configuration issues.');

    async function testMinting() {
      console.log('🧪 TESTING MINTING...');
      
      try {
        const MINT_ABI = ["function mint(address to, uint256 amount) external"];
        const mintToken = new ethers.Contract(FINAL_TOKEN_ADDRESS, MINT_ABI, adminWallet);
        
        const mintAmount = ethers.parseUnits('1000', 18);
        const mintTx = await mintToken.mint(adminWallet.address, mintAmount);
        await mintTx.wait();
        
        console.log(`✅ MINT SUCCESSFUL: ${mintTx.hash}`);
        
        const BALANCE_ABI = ["function balanceOf(address account) external view returns (uint256)"];
        const balanceToken = new ethers.Contract(FINAL_TOKEN_ADDRESS, BALANCE_ABI, adminWallet);
        const balance = await balanceToken.balanceOf(adminWallet.address);
        
        console.log(`💰 Admin balance: ${ethers.formatUnits(balance, 18)} tokens`);
        
        console.log('\n🎉 COMPLETE SUCCESS!');
        console.log('✅ Token is fully functional');
        console.log('✅ ERC-3643 system working');
        
        return true;
        
      } catch (mintError) {
        console.log(`❌ Mint failed: ${mintError.message}`);
        return false;
      }
    }

  } catch (error) {
    console.error('❌ Fix attempt failed:', error);
  }
}

// Run the fix
fixFinalToken().catch(console.error);
