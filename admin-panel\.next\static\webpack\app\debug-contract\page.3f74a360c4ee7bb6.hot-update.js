"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/debug-contract/page",{

/***/ "(app-pages-browser)/./src/app/debug-contract/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/debug-contract/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugContractPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Common ABIs to test\nconst COMMON_FUNCTIONS = [\n    {\n        name: 'name',\n        abi: [\n            'function name() view returns (string)'\n        ]\n    },\n    {\n        name: 'symbol',\n        abi: [\n            'function symbol() view returns (string)'\n        ]\n    },\n    {\n        name: 'decimals',\n        abi: [\n            'function decimals() view returns (uint8)'\n        ]\n    },\n    {\n        name: 'totalSupply',\n        abi: [\n            'function totalSupply() view returns (uint256)'\n        ]\n    },\n    {\n        name: 'paused',\n        abi: [\n            'function paused() view returns (bool)'\n        ]\n    },\n    {\n        name: 'version',\n        abi: [\n            'function version() view returns (string)'\n        ]\n    },\n    {\n        name: 'maxSupply',\n        abi: [\n            'function maxSupply() view returns (uint256)'\n        ]\n    },\n    {\n        name: 'owner',\n        abi: [\n            'function owner() view returns (address)'\n        ]\n    },\n    {\n        name: 'admin',\n        abi: [\n            'function admin() view returns (address)'\n        ]\n    },\n    {\n        name: 'implementation',\n        abi: [\n            'function implementation() view returns (address)'\n        ]\n    },\n    {\n        name: 'proxiableUUID',\n        abi: [\n            'function proxiableUUID() view returns (bytes32)'\n        ]\n    },\n    {\n        name: 'isWhitelisted',\n        abi: [\n            'function isWhitelisted(address) view returns (bool)'\n        ]\n    },\n    {\n        name: 'addToWhitelist',\n        abi: [\n            'function addToWhitelist(address) external'\n        ]\n    },\n    {\n        name: 'removeFromWhitelist',\n        abi: [\n            'function removeFromWhitelist(address) external'\n        ]\n    },\n    {\n        name: 'mint',\n        abi: [\n            'function mint(address to, uint256 amount) external'\n        ]\n    },\n    {\n        name: 'burn',\n        abi: [\n            'function burn(address from, uint256 amount) external'\n        ]\n    },\n    {\n        name: 'identityRegistryAddress',\n        abi: [\n            'function identityRegistryAddress() view returns (address)'\n        ]\n    }\n];\nfunction DebugContractPage() {\n    _s();\n    const [contractAddress, setContractAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0x03112fB317Ac2FFaDF158581896dbB2dC3B9865c');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contractType, setContractType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const analyzeContractType = (testResults)=>{\n        const successfulFunctions = testResults.filter((r)=>r.success).map((r)=>r.function);\n        let type = 'Unknown Contract';\n        if (successfulFunctions.includes('name') && successfulFunctions.includes('symbol') && successfulFunctions.includes('totalSupply')) {\n            if (successfulFunctions.includes('isWhitelisted') && successfulFunctions.includes('paused')) {\n                type = '🛡️ ERC-3643 Security Token (Full Compliance)';\n            } else if (successfulFunctions.includes('paused')) {\n                type = '⏸️ Pausable ERC-20 Token';\n            } else if (successfulFunctions.includes('isWhitelisted')) {\n                type = '📋 Whitelisted ERC-20 Token';\n            } else {\n                type = '🪙 Standard ERC-20 Token';\n            }\n        } else if (successfulFunctions.includes('implementation') || successfulFunctions.includes('proxiableUUID')) {\n            type = '🔄 Proxy Contract';\n        } else if (successfulFunctions.includes('owner') || successfulFunctions.includes('admin')) {\n            type = '👑 Ownable Contract';\n        }\n        setContractType(type);\n    };\n    const testContract = async ()=>{\n        if (!contractAddress) return;\n        setLoading(true);\n        setResults([]);\n        const testResults = [];\n        for (const func of COMMON_FUNCTIONS){\n            try {\n                const result = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_2__.safeContractCall)(contractAddress, func.abi, func.name);\n                testResults.push({\n                    function: func.name,\n                    success: true,\n                    result: (result === null || result === void 0 ? void 0 : result.toString()) || 'null',\n                    error: null\n                });\n            } catch (error) {\n                testResults.push({\n                    function: func.name,\n                    success: false,\n                    result: null,\n                    error: error.message\n                });\n            }\n        }\n        setResults(testResults);\n        setLoading(false);\n        // Analyze contract type based on available functions\n        analyzeContractType(testResults);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-8\",\n                    children: \"\\uD83D\\uDD0D Contract Debugger\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Test Contract Functions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Contract Address\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: contractAddress,\n                                    onChange: (e)=>setContractAddress(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    placeholder: \"0x...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testContract,\n                            disabled: loading || !contractAddress,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50\",\n                            children: loading ? 'Testing...' : 'Test Contract'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Test Results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                contractType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\",\n                                    children: contractType\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-lg border \".concat(result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-3 h-3 rounded-full mr-3 \".concat(result.success ? 'bg-green-500' : 'bg-red-500')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                result.function,\n                                                                \"()\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm \".concat(result.success ? 'text-green-600' : 'text-red-600'),\n                                                    children: result.success ? '✅ Success' : '❌ Failed'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this),\n                                        result.success && result.result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Result:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" \",\n                                                result.result\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 21\n                                        }, this),\n                                        !result.success && result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Error:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" \",\n                                                result.error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-blue-800 mb-2\",\n                                    children: \"Summary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"✅ Successful calls: \",\n                                                results.filter((r)=>r.success).length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"❌ Failed calls: \",\n                                                results.filter((r)=>!r.success).length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"\\uD83D\\uDCCA Total functions tested: \",\n                                                results.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-yellow-800 mb-2\",\n                            children: \"ℹ️ About This Tool\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-700\",\n                            children: \"This tool tests common ERC-20 and ERC-3643 functions on a contract to determine its capabilities. Use this to debug contract compatibility issues and understand what functions are available.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugContractPage, \"/oJdgtYUBjZicyP94VoiHuQmKY0=\");\n_c = DebugContractPage;\nvar _c;\n$RefreshReg$(_c, \"DebugContractPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/debug-contract/page.tsx\n"));

/***/ })

});