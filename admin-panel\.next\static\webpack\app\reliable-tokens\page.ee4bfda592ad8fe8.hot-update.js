"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/app/reliable-tokens/page.tsx":
/*!******************************************!*\
  !*** ./src/app/reliable-tokens/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReliableTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _components_AgentsList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AgentsList */ \"(app-pages-browser)/./src/components/AgentsList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default token address from environment (fallback)\nconst DEFAULT_SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\" || 0;\nfunction ReliableTokensContent() {\n    var _tokenInfo_totalSupply, _tokenInfo_maxSupply, _tokenInfo_metadata_tokenPrice, _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata_tokenPrice1, _tokenInfo_metadata2, _tokenInfo_metadata3;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const urlTokenAddress = searchParams.get('token');\n    // Use URL parameter if provided, otherwise fall back to environment variable\n    const SECURITY_TOKEN_CORE_ADDRESS = urlTokenAddress || DEFAULT_SECURITY_TOKEN_CORE_ADDRESS;\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('management');\n    // Whitelist management\n    const [whitelistedClients, setWhitelistedClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [whitelistLoading, setWhitelistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [whitelistError, setWhitelistError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenFromDb, setTokenFromDb] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ERC-3643 wrapper detection\n    const knownERC3643Wrappers = [\n        '******************************************',\n        '******************************************',\n        '******************************************'\n    ];\n    const knownUnderlyingToken = '******************************************';\n    const isERC3643Wrapper = knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS);\n    const underlyingTokenAddress = isERC3643Wrapper ? knownUnderlyingToken : SECURITY_TOKEN_CORE_ADDRESS;\n    const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n    // Debug logging\n    console.log('🔍 ReliableTokensContent rendered');\n    console.log('🔍 URL token parameter:', urlTokenAddress);\n    console.log('🔍 Using token address:', SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🔍 Default address:', DEFAULT_SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🛡️ Is ERC-3643 wrapper:', isERC3643Wrapper);\n    console.log('🔗 Data address for metadata calls:', dataAddress);\n    // Load token information using fallback-first approach\n    const loadTokenInfo = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🔄 Loading token info with fallback-first approach...');\n            // Check if this is an ERC-3643 wrapper\n            const knownERC3643Wrappers = [\n                '******************************************',\n                '******************************************',\n                '******************************************'\n            ];\n            const knownUnderlyingToken = '******************************************';\n            let isERC3643Wrapper = false;\n            let underlyingTokenAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            if (knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS)) {\n                console.log('🛡️ Detected known ERC-3643 wrapper address');\n                isERC3643Wrapper = true;\n                underlyingTokenAddress = knownUnderlyingToken;\n                console.log('🔗 Using known underlying token for data calls:', underlyingTokenAddress);\n            }\n            // For ERC-3643 wrappers, get basic info from wrapper but maxSupply/metadata from underlying\n            const basicInfoAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n            console.log('📊 Getting basic info from:', basicInfoAddress);\n            console.log('📊 Getting data (maxSupply, metadata) from:', dataAddress);\n            // Use safe contract calls that automatically handle fallbacks\n            let name, symbol, version, totalSupply, decimals, paused;\n            try {\n                [name, symbol, version, totalSupply, decimals] = await Promise.all([\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'name'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'symbol'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'version'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'totalSupply'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'decimals')\n                ]);\n                // Try to get paused status separately with error handling\n                try {\n                    paused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                } catch (pausedError) {\n                    console.log('⚠️ paused() function not available, assuming not paused:', pausedError);\n                    paused = false;\n                }\n            } catch (error) {\n                console.error('❌ Failed to load basic token info:', error);\n                throw new Error(\"Failed to load token information: \".concat(error));\n            }\n            // Get maxSupply and metadata from the appropriate address\n            let maxSupply;\n            let metadata;\n            try {\n                maxSupply = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'maxSupply');\n                console.log('✅ maxSupply loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ maxSupply failed, using 0:', error);\n                maxSupply = BigInt(0);\n            }\n            try {\n                metadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                console.log('✅ metadata loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ metadata failed, using defaults:', error);\n                metadata = {\n                    tokenPrice: '0',\n                    currency: 'USD',\n                    bonusTiers: '',\n                    tokenDetails: ''\n                };\n            }\n            setTokenInfo({\n                name: name || 'Unknown Token',\n                symbol: symbol || 'UNKNOWN',\n                version: version || '1.0',\n                totalSupply: totalSupply || BigInt(0),\n                maxSupply: maxSupply || BigInt(0),\n                decimals: decimals !== null && decimals !== void 0 ? decimals : 0,\n                paused: paused || false,\n                metadata: {\n                    tokenPrice: (metadata === null || metadata === void 0 ? void 0 : metadata.tokenPrice) || '0',\n                    currency: (metadata === null || metadata === void 0 ? void 0 : metadata.currency) || 'USD',\n                    bonusTiers: (metadata === null || metadata === void 0 ? void 0 : metadata.bonusTiers) || ''\n                }\n            });\n            console.log('✅ Token info loaded successfully');\n        } catch (error) {\n            console.error('❌ Failed to load token info:', error);\n            setError(\"Failed to load token information: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Connect wallet\n    const connectWallet = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                await window.ethereum.request({\n                    method: 'eth_requestAccounts'\n                });\n                setWalletConnected(true);\n                setSuccess('Wallet connected successfully');\n            } else {\n                setError('MetaMask not found. Please install MetaMask to use this feature.');\n            }\n        } catch (error) {\n            setError(\"Failed to connect wallet: \".concat(error.message));\n        }\n    };\n    // Debug wallet and transaction setup\n    const debugWalletConnection = async ()=>{\n        try {\n            setError(null);\n            setSuccess(null);\n            console.log('🔍 DEBUGGING WALLET CONNECTION...');\n            if ( false || !window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            // Check accounts\n            const accounts = await window.ethereum.request({\n                method: 'eth_accounts'\n            });\n            console.log('Connected accounts:', accounts);\n            // Check network\n            const chainId = await window.ethereum.request({\n                method: 'eth_chainId'\n            });\n            console.log('Current chain ID:', chainId, '(Expected: 0x13882 for Polygon Amoy)');\n            // Test provider creation\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const signerAddress = await signer.getAddress();\n            console.log('Signer address:', signerAddress);\n            // Test simple contract call (view function)\n            const viewABI = [\n                \"function name() view returns (string)\"\n            ];\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, viewABI, provider);\n            const tokenName = await contract.name();\n            console.log('Token name (view call):', tokenName);\n            // Test contract with signer (but don't execute)\n            const mintABI = [\n                \"function mint(address to, uint256 amount) external\"\n            ];\n            const contractWithSigner = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, mintABI, signer);\n            // Try gas estimation (this is where it might fail)\n            try {\n                const gasEstimate = await contractWithSigner.mint.estimateGas(signerAddress, 1);\n                console.log('✅ Gas estimation successful:', gasEstimate.toString());\n                setSuccess(\"✅ Wallet connection is working! Gas estimate: \".concat(gasEstimate.toString()));\n            } catch (gasError) {\n                console.error('❌ Gas estimation failed:', gasError);\n                setError(\"Gas estimation failed: \".concat(gasError.message));\n            }\n        } catch (error) {\n            console.error('❌ Debug failed:', error);\n            setError(\"Debug failed: \".concat(error.message));\n        }\n    };\n    // Toggle pause state using fallback-first approach\n    const togglePause = async ()=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            const currentPaused = tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused;\n            const isPausing = !currentPaused;\n            const functionName = isPausing ? 'pause' : 'unpause';\n            console.log(\"\\uD83D\\uDD04 \".concat(isPausing ? 'Pausing' : 'Unpausing', \" token...\"));\n            // Get real-time state to ensure accuracy (with error handling)\n            let realTimePaused;\n            try {\n                realTimePaused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                // Validate state change is needed\n                if (isPausing && realTimePaused) {\n                    throw new Error('Token is already paused. Refreshing page data...');\n                }\n                if (!isPausing && !realTimePaused) {\n                    throw new Error('Token is already unpaused. Refreshing page data...');\n                }\n            } catch (pausedError) {\n                console.log('⚠️ Cannot check paused status, proceeding with operation:', pausedError);\n            // Continue with the operation if we can't check the current state\n            }\n            // Check if the contract supports pause/unpause functions\n            try {\n                // Execute the transaction using safe transaction approach\n                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, functionName, []);\n                console.log(\"✅ \".concat(functionName, \" transaction sent: \").concat(tx.hash));\n                // Wait for confirmation\n                const receipt = await tx.wait();\n                console.log(\"✅ \".concat(functionName, \" confirmed in block \").concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n                setSuccess(\"Token \".concat(isPausing ? 'paused' : 'unpaused', \" successfully!\"));\n                // Refresh token info\n                await loadTokenInfo();\n            } catch (pauseError) {\n                console.error(\"❌ \".concat(functionName, \" function not supported:\"), pauseError);\n                throw new Error(\"This token contract does not support \".concat(functionName, \" functionality. Contract may not be pausable.\"));\n            }\n        } catch (error) {\n            var _error_message, _error_message1;\n            console.error(\"❌ Toggle pause failed:\", error);\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('already paused')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('already unpaused'))) {\n                setError(\"\".concat(error.message));\n                // Auto-refresh data after state conflict\n                setTimeout(async ()=>{\n                    await loadTokenInfo();\n                    setError(null);\n                }, 2000);\n            } else {\n                setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n            }\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Mint tokens using fallback-first approach\n    const mintTokens = async (amount, recipient)=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            console.log(\"\\uD83D\\uDD04 Minting \".concat(amount, \" tokens to \").concat(recipient, \"...\"));\n            // 🚨 CRITICAL SECURITY CHECK: Verify recipient is whitelisted before minting\n            console.log('🔒 SECURITY CHECK: Verifying recipient is whitelisted...');\n            try {\n                // First get the identity registry address using minimal ABI\n                const identityRegistryABI = [\n                    \"function identityRegistryAddress() view returns (address)\"\n                ];\n                const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                if (!identityRegistryAddress) {\n                    throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                }\n                // Then check whitelist status on the identity registry\n                const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                    \"function isWhitelisted(address account) external view returns (bool)\"\n                ], 'isWhitelisted', [\n                    recipient\n                ]);\n                if (!isWhitelisted) {\n                    throw new Error(\"SECURITY VIOLATION: Recipient address \".concat(recipient, \" is not whitelisted. Only whitelisted addresses can receive tokens for ERC-3643 compliance.\"));\n                }\n                console.log('✅ SECURITY PASSED: Recipient is whitelisted, proceeding with mint');\n            } catch (whitelistError) {\n                var _whitelistError_message, _whitelistError_message1;\n                console.error('❌ SECURITY CHECK FAILED:', whitelistError);\n                // Check if this is a contract compatibility issue\n                if (((_whitelistError_message = whitelistError.message) === null || _whitelistError_message === void 0 ? void 0 : _whitelistError_message.includes('execution reverted')) || ((_whitelistError_message1 = whitelistError.message) === null || _whitelistError_message1 === void 0 ? void 0 : _whitelistError_message1.includes('no data present'))) {\n                    console.log('⚠️ Contract does not support ERC-3643 identity registry functionality, proceeding without check');\n                    console.log('🚨 WARNING: This token may not be ERC-3643 compliant!');\n                } else {\n                    throw new Error(\"Security validation failed: \".concat(whitelistError.message));\n                }\n            }\n            // Validate and convert amount\n            console.log('🔍 Amount validation:', {\n                rawAmount: amount,\n                amountType: typeof amount,\n                tokenInfoDecimals: tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals,\n                parsedAmount: parseFloat(amount)\n            });\n            const parsedAmount = parseFloat(amount);\n            if (isNaN(parsedAmount) || parsedAmount <= 0) {\n                throw new Error(\"Invalid amount: \".concat(amount, \". Please enter a positive number.\"));\n            }\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            // Convert to BigInt safely\n            let mintAmount;\n            if (decimals > 0) {\n                // For tokens with decimals, multiply by 10^decimals\n                const multiplier = 10n ** BigInt(decimals);\n                mintAmount = BigInt(Math.floor(parsedAmount * Math.pow(10, decimals)));\n            } else {\n                // For tokens without decimals, use whole number\n                mintAmount = BigInt(Math.floor(parsedAmount));\n            }\n            if (mintAmount <= 0n) {\n                throw new Error(\"Calculated mint amount is invalid: \".concat(mintAmount.toString()));\n            }\n            // Execute mint transaction\n            console.log('🔄 Attempting mint with params:', {\n                address: SECURITY_TOKEN_CORE_ADDRESS,\n                function: 'mint',\n                args: [\n                    recipient,\n                    mintAmount.toString()\n                ],\n                recipient: recipient,\n                amount: amount,\n                mintAmount: mintAmount.toString(),\n                decimals: decimals,\n                mintAmountType: typeof mintAmount\n            });\n            // Try with minimal ABI first\n            const mintABI = [\n                \"function mint(address to, uint256 amount) external\"\n            ];\n            const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, mintABI, 'mint', [\n                recipient,\n                mintAmount\n            ]);\n            console.log(\"✅ Mint transaction sent: \".concat(tx.hash));\n            const receipt = await tx.wait();\n            console.log(\"✅ Mint confirmed in block \".concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(recipient, \"!\"));\n            // Refresh token info\n            await loadTokenInfo();\n        } catch (error) {\n            console.error(\"❌ Mint failed:\", error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Load token from database to get the ID for whitelist queries\n    const loadTokenFromDatabase = async ()=>{\n        if (!SECURITY_TOKEN_CORE_ADDRESS) return;\n        try {\n            const response = await fetch('/api/tokens');\n            if (response.ok) {\n                const data = await response.json();\n                // Handle new API response format\n                const tokens = data.success && Array.isArray(data.tokens) ? data.tokens : Array.isArray(data) ? data : [];\n                // Find token by address\n                const token = tokens.find((t)=>t.address.toLowerCase() === SECURITY_TOKEN_CORE_ADDRESS.toLowerCase());\n                if (token) {\n                    setTokenFromDb(token);\n                    console.log('✅ Token found in database:', token);\n                } else {\n                    console.log('⚠️ Token not found in database, checking all available tokens:', tokens.map((t)=>t.address));\n                }\n            }\n        } catch (error) {\n            console.log('⚠️ Error loading token from database:', error);\n            setWhitelistError('Failed to load token from database');\n        }\n    };\n    // Load whitelisted clients for this token\n    const loadWhitelistedClients = async ()=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) {\n            setWhitelistError('Token not found in database');\n            return;\n        }\n        setWhitelistLoading(true);\n        setWhitelistError(null);\n        try {\n            console.log('🔍 Loading whitelisted clients for token ID:', tokenFromDb.id);\n            const response = await fetch(\"/api/tokens?whitelistedInvestors=true&tokenId=\".concat(tokenFromDb.id));\n            console.log('📥 Response status:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('❌ API Error:', errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📊 API Response:', data);\n            if (data.success) {\n                var _data_investors;\n                setWhitelistedClients(data.investors || []);\n                console.log('✅ Loaded whitelisted clients:', ((_data_investors = data.investors) === null || _data_investors === void 0 ? void 0 : _data_investors.length) || 0);\n            } else {\n                throw new Error(data.error || 'Failed to load whitelisted clients');\n            }\n        } catch (err) {\n            console.error('❌ Error loading whitelisted clients:', err);\n            setWhitelistError(err.message || 'Failed to load whitelisted clients');\n            setWhitelistedClients([]); // Clear the list on error\n        } finally{\n            setWhitelistLoading(false);\n        }\n    };\n    // Remove client from whitelist\n    const handleRemoveFromWhitelist = async (clientId)=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) return;\n        if (!confirm('Are you sure you want to remove this client from the whitelist?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/clients/\".concat(clientId, \"/token-approval\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: tokenFromDb.id,\n                    whitelistApproved: false,\n                    approvalStatus: 'REJECTED',\n                    notes: 'Removed from whitelist by admin'\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || 'Failed to remove from whitelist');\n            }\n            // Refresh the clients list\n            loadWhitelistedClients();\n            setSuccess('Client removed from whitelist successfully');\n        } catch (err) {\n            console.error('Error removing from whitelist:', err);\n            setError(\"Error: \".concat(err.message));\n        }\n    };\n    // Load token info on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            loadTokenInfo();\n            loadTokenFromDatabase();\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    // Load whitelist when token from DB is available and whitelist tab is active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if (tokenFromDb && activeTab === 'whitelist') {\n                loadWhitelistedClients();\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], [\n        tokenFromDb,\n        activeTab\n    ]);\n    // Check wallet connection on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if ( true && window.ethereum) {\n                window.ethereum.request({\n                    method: 'eth_accounts'\n                }).then({\n                    \"ReliableTokensContent.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setWalletConnected(true);\n                        }\n                    }\n                }[\"ReliableTokensContent.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    var _tokenInfo_decimals;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Reliable Token Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bulletproof token management with fallback-first architecture - no more network errors!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 624,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"Connection Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-2 \".concat(walletConnected ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Wallet: \",\n                                                            walletConnected ? 'Connected' : 'Not Connected'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Network: Reliable RPC Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\",\n                                        children: \"Connect Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 17\n                                    }, this),\n                                    walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: debugWalletConnection,\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg\",\n                                        children: \"Debug Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 mr-2\",\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"text-red-600 hover:text-red-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 674,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 mr-2\",\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSuccess(null),\n                                className: \"text-green-600 hover:text-green-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 691,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('management'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'management' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"\\uD83D\\uDEE1️ Token Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('whitelist'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'whitelist' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: [\n                                        \"\\uD83D\\uDC65 Whitelisted Clients\",\n                                        whitelistedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full\",\n                                            children: whitelistedClients.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 708,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Token Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: loadTokenInfo,\n                                            disabled: loading,\n                                            className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50\",\n                                            children: loading ? 'Loading...' : 'Refresh'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 11\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Loading token information...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 13\n                                }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.name || 'Unknown'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.symbol || 'N/A'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Version\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: tokenInfo.version || '1.0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Decimals\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: (_tokenInfo_decimals = tokenInfo.decimals) !== null && _tokenInfo_decimals !== void 0 ? _tokenInfo_decimals : 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Total Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_totalSupply = tokenInfo.totalSupply) === null || _tokenInfo_totalSupply === void 0 ? void 0 : _tokenInfo_totalSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_maxSupply = tokenInfo.maxSupply) === null || _tokenInfo_maxSupply === void 0 ? void 0 : _tokenInfo_maxSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                    children: tokenInfo.paused ? '⏸️ Paused' : '▶️ Active'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Token Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice = _tokenInfo_metadata.tokenPrice) === null || _tokenInfo_metadata_tokenPrice === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice.toString()) || '0',\n                                                        \".00 \",\n                                                        ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.currency) || 'USD'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No token information available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Pause Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Token is currently paused. Unpause to allow transfers.' : 'Token is currently active. Pause to stop all transfers.'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePause,\n                                            disabled: actionLoading || !tokenInfo,\n                                            className: \"w-full px-4 py-2 rounded-lg font-medium disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                            children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '▶️ Unpause Token' : '⏸️ Pause Token'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"mintRecipient\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 842,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"mintAmount\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        const recipientInput = document.getElementById('mintRecipient');\n                                                        const amountInput = document.getElementById('mintAmount');\n                                                        const recipient = recipientInput === null || recipientInput === void 0 ? void 0 : recipientInput.value;\n                                                        const amount = amountInput === null || amountInput === void 0 ? void 0 : amountInput.value;\n                                                        console.log('🔍 Form Debug:', {\n                                                            recipientInput: recipientInput,\n                                                            amountInput: amountInput,\n                                                            recipientValue: recipient,\n                                                            amountValue: amount,\n                                                            amountInputValue: amountInput === null || amountInput === void 0 ? void 0 : amountInput.value,\n                                                            amountInputValueNumber: amountInput === null || amountInput === void 0 ? void 0 : amountInput.valueAsNumber\n                                                        });\n                                                        if (recipient && amount) {\n                                                            mintTokens(amount, recipient);\n                                                        } else {\n                                                            setError('Please enter both recipient address and amount');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading || !tokenInfo,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: actionLoading ? 'Processing...' : '🪙 Mint Tokens'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 837,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"ERC-3643 Whitelist Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium\",\n                                                    children: \"\\uD83D\\uDEE1️ Identity Registry Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-600 text-lg mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ERC-3643 Process:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \" Adding to whitelist automatically registers an OnchainID if needed, then adds to whitelist. This ensures full ERC-3643 compliance.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 902,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"whitelistAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Adding to whitelist via API...');\n                                                                        const response = await fetch('/api/admin/add-to-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist add successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist add failed:', error);\n                                                                        setError(\"Failed to add to whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: actionLoading ? 'Processing...' : '✅ Register & Whitelist'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 922,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Removing from whitelist via API...');\n                                                                        const response = await fetch('/api/admin/remove-from-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist remove successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist remove failed:', error);\n                                                                        setError(\"Failed to remove from whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: \"❌ Remove (API)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 921,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                try {\n                                                                    // First get the identity registry address using minimal ABI\n                                                                    const identityRegistryABI = [\n                                                                        \"function identityRegistryAddress() view returns (address)\"\n                                                                    ];\n                                                                    const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                                                                    if (!identityRegistryAddress) {\n                                                                        throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                                                                    }\n                                                                    // Then check whitelist status on the identity registry\n                                                                    const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                                                                        \"function isWhitelisted(address account) external view returns (bool)\"\n                                                                    ], 'isWhitelisted', [\n                                                                        address\n                                                                    ]);\n                                                                    setSuccess(\"Address \".concat(address, \" is \").concat(isWhitelisted ? '✅ whitelisted' : '❌ not whitelisted', \" (via Identity Registry: \").concat(identityRegistryAddress.slice(0, 10), \"...)\"));\n                                                                } catch (whitelistCheckError) {\n                                                                    var _whitelistCheckError_message, _whitelistCheckError_message1;\n                                                                    // Check if this is a contract compatibility issue\n                                                                    if (((_whitelistCheckError_message = whitelistCheckError.message) === null || _whitelistCheckError_message === void 0 ? void 0 : _whitelistCheckError_message.includes('execution reverted')) || ((_whitelistCheckError_message1 = whitelistCheckError.message) === null || _whitelistCheckError_message1 === void 0 ? void 0 : _whitelistCheckError_message1.includes('no data present'))) {\n                                                                        setError('⚠️ This contract does not support ERC-3643 identity registry functionality.');\n                                                                    } else {\n                                                                        throw whitelistCheckError;\n                                                                    }\n                                                                }\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to check whitelist: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD0D Check Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 808,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Balance Checker\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address to Check\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1095,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"balanceAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1094,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('balanceAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                const balance = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'balanceOf', [\n                                                                    address\n                                                                ]);\n                                                                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                const formattedBalance = decimals > 0 ? (Number(balance || 0) / Math.pow(10, decimals)).toString() : (balance === null || balance === void 0 ? void 0 : balance.toString()) || '0';\n                                                                setSuccess(\"Balance: \".concat(formattedBalance, \" \").concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.symbol) || 'tokens'));\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to get balance: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Check Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Burn Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount to Burn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1157,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"burnAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1160,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"⚠️ This will permanently destroy tokens from your wallet. This action cannot be undone.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1167,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const amount = (_document_getElementById = document.getElementById('burnAmount')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (amount) {\n                                                            if (confirm(\"Are you sure you want to burn \".concat(amount, \" tokens? This action cannot be undone.\"))) {\n                                                                try {\n                                                                    setActionLoading(true);\n                                                                    const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                    const burnAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'burn', [\n                                                                        burnAmount\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Successfully burned \".concat(amount, \" tokens\"));\n                                                                    await loadTokenInfo();\n                                                                } catch (error) {\n                                                                    setError(\"Failed to burn tokens: \".concat(error.message));\n                                                                } finally{\n                                                                    setActionLoading(false);\n                                                                }\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an amount to burn');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD25 Burn Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1170,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1153,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Agent Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1212,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Agent Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"agentAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1219,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1215,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully added \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to add agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➕ Add Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1227,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully removed \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to remove agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➖ Remove Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1272,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1226,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1214,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Note: Since AGENT_MANAGER module is not registered, we can't enumerate agents\n                                                                // Instead, we'll show how to check if specific addresses have AGENT_ROLE\n                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                // Check some common addresses for demonstration\n                                                                const addressesToCheck = [\n                                                                    '0x56f3726C92B8B92a6ab71983886F91718540d888',\n                                                                    '0x1f1a17fe870f5A0EEf1274247c080478E3d0840A' // Test address\n                                                                ];\n                                                                const agentResults = [];\n                                                                for (const address of addressesToCheck){\n                                                                    try {\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            AGENT_ROLE,\n                                                                            address\n                                                                        ]);\n                                                                        if (hasRole) {\n                                                                            agentResults.push(\"✅ \".concat(address));\n                                                                        }\n                                                                    } catch (error) {\n                                                                    // Skip addresses that cause errors\n                                                                    }\n                                                                }\n                                                                if (agentResults.length === 0) {\n                                                                    setSuccess('No agents found in checked addresses. Use \"Check Role\" to verify specific addresses.');\n                                                                } else {\n                                                                    setSuccess(\"Agents found: \".concat(agentResults.join(', ')));\n                                                                }\n                                                            } catch (error) {\n                                                                setError(\"Failed to get agents: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        },\n                                                        disabled: actionLoading,\n                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                        children: \"\\uD83D\\uDC65 Check Known Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1319,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-3\",\n                                                            children: \"\\uD83D\\uDCCB Current Agents List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1382,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentsList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1383,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1381,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1213,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1211,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Role Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1394,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"roleAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1397,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1393,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1405,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"roleSelect\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"AGENT_ROLE\",\n                                                                            children: \"AGENT_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1412,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"TRANSFER_MANAGER_ROLE\",\n                                                                            children: \"TRANSFER_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1413,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"MODULE_MANAGER_ROLE\",\n                                                                            children: \"MODULE_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1414,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DEFAULT_ADMIN_ROLE\",\n                                                                            children: \"DEFAULT_ADMIN_ROLE (⚠️ Dangerous)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1415,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1408,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1404,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1392,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully granted \".concat(roleSelect, \" to \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to grant role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"✅ Grant Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1420,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully revoked \".concat(roleSelect, \" from \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to revoke role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"❌ Revoke Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1467,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        setSuccess(\"Address \".concat(address, \" \").concat(hasRole ? '✅ HAS' : '❌ DOES NOT HAVE', \" \").concat(roleSelect));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to check role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDD0D Check Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1514,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1419,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Role Descriptions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1563,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1563,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"AGENT_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1564,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can perform basic token operations\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1564,\n                                                                columnNumber: 84\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"TRANSFER_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1565,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can manage transfers and compliance\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1565,\n                                                                columnNumber: 96\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"MODULE_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can register/unregister modules\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1566,\n                                                                columnNumber: 90\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DEFAULT_ADMIN_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1567,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Full admin access (use with extreme caution)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1562,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1561,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1391,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Token Metadata\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1575,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Token Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1579,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newTokenPrice\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"100.00 USD\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1582,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1578,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Bonus Tiers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1590,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newBonusTiers\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"Early: 20%, Standard: 10%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1593,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1589,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1577,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Details/Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1602,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"newTokenDetails\",\n                                                            rows: 3,\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Token type: Carbon Credits, Debt Securities, Real Estate, etc. Additional details...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1605,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1601,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Image URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1613,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"url\",\n                                                            id: \"newTokenImageUrl\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"https://example.com/token-logo.png\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1616,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1612,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1, _document_getElementById2;\n                                                                const price = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const tiers = (_document_getElementById1 = document.getElementById('newBonusTiers')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                const details = (_document_getElementById2 = document.getElementById('newTokenDetails')) === null || _document_getElementById2 === void 0 ? void 0 : _document_getElementById2.value;\n                                                                if (price || tiers || details) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Get current values if not provided\n                                                                        const currentPrice = price || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenPrice) || '';\n                                                                        const currentTiers = tiers || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.bonusTiers) || '';\n                                                                        const currentDetails = details || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenDetails) || '';\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                            currentPrice,\n                                                                            currentTiers,\n                                                                            currentDetails\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token metadata!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update metadata: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter at least one field to update');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDCDD Update Metadata\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1624,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const imageUrl = (_document_getElementById = document.getElementById('newTokenImageUrl')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (imageUrl) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenImageUrl', [\n                                                                            imageUrl\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token image!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update image: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an image URL');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDDBC️ Update Image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1665,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1623,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1576,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1574,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Quick Price Update\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1705,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Current Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1708,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice1 = _tokenInfo_metadata2.tokenPrice) === null || _tokenInfo_metadata_tokenPrice1 === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice1.toString()) || '0',\n                                                                \".00 \",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.currency) || 'USD'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1711,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1707,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"New Price (USD)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1716,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"newTokenPrice\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"150\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1719,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1715,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const newPrice = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (newPrice) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                console.log('🔄 Updating token price using fallback-first approach...');\n                                                                // Try direct updateTokenPrice first\n                                                                try {\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenPrice', [\n                                                                        newPrice\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD\"));\n                                                                } catch (directError) {\n                                                                    console.log('Direct method failed, trying fallback...');\n                                                                    // Fallback to updateTokenMetadata\n                                                                    const currentMetadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                                                                    const currentBonusTiers = currentMetadata[1];\n                                                                    const currentDetails = currentMetadata[2];\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                        newPrice,\n                                                                        currentBonusTiers,\n                                                                        currentDetails\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD (via fallback method)\"));\n                                                                }\n                                                                // Refresh token info\n                                                                await loadTokenInfo();\n                                                            } catch (error) {\n                                                                setError(\"Failed to update token price: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter a new price');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Update Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1726,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1706,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1704,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"\\uD83D\\uDEE1️ Fallback-First Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1794,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Network Resilience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1797,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Multiple RPC endpoints\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1799,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic failover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1800,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Connection testing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1801,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1798,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1796,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Error Elimination\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1805,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"missing revert data\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1807,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"Internal JSON-RPC\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1808,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Reliable contract calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1809,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1806,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1804,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ User Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1813,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Seamless operation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1815,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Clear error messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1816,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic recovery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1817,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1814,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1812,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1795,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1793,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"\\uD83D\\uDC65 Whitelisted Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1832,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Manage clients who are approved to hold \",\n                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.name) || 'this',\n                                                    \" tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1833,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1831,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: whitelistedClients.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1839,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Whitelisted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1840,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1838,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                                children: \"➕ Add Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1842,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1837,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1830,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1829,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md\",\n                            children: whitelistLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1856,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"Loading whitelisted clients...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1857,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1855,\n                                columnNumber: 17\n                            }, this) : whitelistError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-600 text-xl\",\n                                                children: \"❌\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1863,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-red-800\",\n                                                        children: \"Error Loading Whitelist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1865,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700\",\n                                                        children: whitelistError\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1866,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1864,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1862,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1861,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1860,\n                                columnNumber: 17\n                            }, this) : whitelistedClients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1873,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Whitelisted Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1874,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"No clients have been whitelisted for this token yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1875,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium\",\n                                        children: \"➕ Add First Client\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1878,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1872,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1890,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Wallet Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1893,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"KYC Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1896,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Approved Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1899,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1902,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1889,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1888,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedClients.map((client)=>{\n                                                var _client_tokenApproval;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: [\n                                                                            client.firstName,\n                                                                            \" \",\n                                                                            client.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1912,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: client.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1915,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1911,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1910,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 font-mono\",\n                                                                children: client.walletAddress\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1919,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1918,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.kycStatus === 'APPROVED' ? 'bg-green-100 text-green-800' : client.kycStatus === 'REJECTED' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                children: client.kycStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1924,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1923,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: ((_client_tokenApproval = client.tokenApproval) === null || _client_tokenApproval === void 0 ? void 0 : _client_tokenApproval.approvedAt) ? new Date(client.tokenApproval.approvedAt).toLocaleDateString() : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1934,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveFromWhitelist(client.id),\n                                                                    className: \"text-red-600 hover:text-red-900 mr-4\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1940,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/clients/\".concat(client.id),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1946,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1939,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1909,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1907,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1887,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1886,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1853,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 1827,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 622,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 621,\n        columnNumber: 5\n    }, this);\n}\n_s(ReliableTokensContent, \"mZycVj3G2vFi5jAx3YzfoFENy3g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ReliableTokensContent;\nfunction ReliableTokensPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1971,\n                        columnNumber: 9\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading token management...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1972,\n                        columnNumber: 9\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                lineNumber: 1970,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1969,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReliableTokensContent, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1975,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 1969,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ReliableTokensPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ReliableTokensContent\");\n$RefreshReg$(_c1, \"ReliableTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reliable-tokens/page.tsx\n"));

/***/ })

});