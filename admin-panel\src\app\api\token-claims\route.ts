import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// Available claim types for validation
const VALID_CLAIM_IDS = [1, 2, 3, 4, 5, 6, 7];
const REQUIRED_CLAIM_IDS = [1, 4]; // KYC and Qualification are always required

// GET /api/token-claims - Get claims configuration for a token
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenId = searchParams.get('tokenId');
    const tokenAddress = searchParams.get('tokenAddress');

    if (!tokenId && !tokenAddress) {
      return NextResponse.json(
        { error: 'Either tokenId or tokenAddress is required' },
        { status: 400 }
      );
    }

    // Find token by ID or address
    const whereClause = tokenId 
      ? { id: tokenId }
      : { address: tokenAddress };

    const token = await prisma.token.findUnique({
      where: whereClause,
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        selectedClaims: true,
        network: true,
        tokenType: true
      }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Parse selected claims
    const selectedClaims = token.selectedClaims 
      ? token.selectedClaims.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      : [1, 4]; // Default: KYC + Qualification

    return NextResponse.json({
      success: true,
      token: {
        ...token,
        selectedClaimsArray: selectedClaims
      }
    });

  } catch (error) {
    console.error('Error fetching token claims:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/token-claims - Update claims configuration for a token
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { tokenId, tokenAddress, selectedClaims } = body;

    if (!tokenId && !tokenAddress) {
      return NextResponse.json(
        { error: 'Either tokenId or tokenAddress is required' },
        { status: 400 }
      );
    }

    if (!selectedClaims || !Array.isArray(selectedClaims)) {
      return NextResponse.json(
        { error: 'selectedClaims must be an array of claim IDs' },
        { status: 400 }
      );
    }

    // Validate claim IDs
    const invalidClaims = selectedClaims.filter(id => !VALID_CLAIM_IDS.includes(id));
    if (invalidClaims.length > 0) {
      return NextResponse.json(
        { error: `Invalid claim IDs: ${invalidClaims.join(', ')}` },
        { status: 400 }
      );
    }

    // Ensure required claims are included
    const finalClaims = [...new Set([...REQUIRED_CLAIM_IDS, ...selectedClaims])];
    const claimsString = finalClaims.sort((a, b) => a - b).join(',');

    // Find and update token
    const whereClause = tokenId 
      ? { id: tokenId }
      : { address: tokenAddress };

    const existingToken = await prisma.token.findUnique({
      where: whereClause
    });

    if (!existingToken) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    const updatedToken = await prisma.token.update({
      where: whereClause,
      data: {
        selectedClaims: claimsString,
        deploymentNotes: existingToken.deploymentNotes 
          ? `${existingToken.deploymentNotes.split('Required Claims:')[0]}Required Claims: ${claimsString}`
          : `Required Claims: ${claimsString}`
      },
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        selectedClaims: true,
        network: true,
        tokenType: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Token claims updated successfully',
      token: {
        ...updatedToken,
        selectedClaimsArray: finalClaims
      }
    });

  } catch (error) {
    console.error('Error updating token claims:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/token-claims - Bulk update claims for multiple tokens
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { updates } = body;

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { error: 'updates must be an array of {tokenId, selectedClaims} objects' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    for (const update of updates) {
      try {
        const { tokenId, selectedClaims } = update;

        if (!tokenId || !selectedClaims || !Array.isArray(selectedClaims)) {
          errors.push({ tokenId, error: 'Invalid update format' });
          continue;
        }

        // Validate claim IDs
        const invalidClaims = selectedClaims.filter(id => !VALID_CLAIM_IDS.includes(id));
        if (invalidClaims.length > 0) {
          errors.push({ tokenId, error: `Invalid claim IDs: ${invalidClaims.join(', ')}` });
          continue;
        }

        // Ensure required claims are included
        const finalClaims = [...new Set([...REQUIRED_CLAIM_IDS, ...selectedClaims])];
        const claimsString = finalClaims.sort((a, b) => a - b).join(',');

        const updatedToken = await prisma.token.update({
          where: { id: tokenId },
          data: {
            selectedClaims: claimsString
          },
          select: {
            id: true,
            name: true,
            symbol: true,
            selectedClaims: true
          }
        });

        results.push({
          tokenId,
          success: true,
          token: updatedToken
        });

      } catch (error) {
        errors.push({ 
          tokenId: update.tokenId, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return NextResponse.json({
      success: errors.length === 0,
      message: `Updated ${results.length} tokens, ${errors.length} errors`,
      results,
      errors
    });

  } catch (error) {
    console.error('Error bulk updating token claims:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
