{"_format": "hh-sol-artifact-1", "contractName": "IWorkingIdentityRegistry", "sourceName": "contracts/fresh/FreshERC3643Token.sol", "abi": [{"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}