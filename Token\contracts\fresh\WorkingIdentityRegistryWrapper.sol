// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";

interface IWorkingUnderlyingRegistry {
    function isVerified(address userAddress) external view returns (bool);
    function registerIdentity(address userAddress, uint16 country) external;
}

/**
 * @title WorkingIdentityRegistryWrapper
 * @dev A simplified Identity Registry Wrapper that directly delegates to the working underlying registry
 * This bypasses all the broken wrapper logic and provides a clean interface for ERC-3643 tokens
 */
contract WorkingIdentityRegistryWrapper is 
    Initializable, 
    UUPSUpgradeable, 
    AccessControlUpgradeable 
{
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    IWorkingUnderlyingRegistry public underlyingRegistry;
    
    event IdentityRegistered(address indexed userAddress, address indexed identity, uint16 country);
    event IdentityDeleted(address indexed userAddress);
    event CountryUpdated(address indexed userAddress, uint16 country);
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    /**
     * @dev Initialize the wrapper with the working underlying registry
     * @param _underlyingRegistry Address of the working underlying Identity Registry
     * @param _admin Address of the admin who can manage the wrapper
     */
    function initialize(
        address _underlyingRegistry,
        address _admin
    ) public initializer {
        require(_underlyingRegistry != address(0), "WorkingWrapper: invalid registry address");
        require(_admin != address(0), "WorkingWrapper: invalid admin address");
        
        __AccessControl_init();
        __UUPSUpgradeable_init();
        
        underlyingRegistry = IWorkingUnderlyingRegistry(_underlyingRegistry);
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(AGENT_ROLE, _admin);
    }
    
    /**
     * @dev Check if a user is verified
     * @param userAddress Address of the user to check
     * @return bool True if the user is verified in the underlying registry
     */
    function isVerified(address userAddress) external view returns (bool) {
        require(userAddress != address(0), "WorkingWrapper: invalid user address");
        
        // Directly delegate to the working underlying registry
        return underlyingRegistry.isVerified(userAddress);
    }
    
    /**
     * @dev Register a user's identity (ERC-3643 compatible signature)
     * @param userAddress Address of the user to register
     * @param onchainId OnchainID address (ignored for compatibility with underlying registry)
     * @param country Country code of the user
     */
    function registerIdentity(
        address userAddress,
        address onchainId, // This parameter is ignored for compatibility
        uint16 country
    ) external onlyRole(AGENT_ROLE) {
        require(userAddress != address(0), "WorkingWrapper: invalid user address");
        require(country > 0, "WorkingWrapper: invalid country code");
        
        // Call the underlying registry with the correct signature (2 parameters)
        underlyingRegistry.registerIdentity(userAddress, country);
        
        emit IdentityRegistered(userAddress, onchainId, country);
    }
    
    /**
     * @dev Get the country of a user (compatibility function)
     * @param userAddress Address of the user
     * @return uint16 Country code (returns 840 for all users as default)
     */
    function investorCountry(address userAddress) external pure returns (uint16) {
        require(userAddress != address(0), "WorkingWrapper: invalid user address");
        // Return default country code (USA) for compatibility
        // In a full implementation, this would be stored and retrieved
        return 840;
    }
    
    /**
     * @dev Get the OnchainID of a user (compatibility function)
     * @param userAddress Address of the user
     * @return address OnchainID address (returns zero address as placeholder)
     */
    function identity(address userAddress) external pure returns (address) {
        require(userAddress != address(0), "WorkingWrapper: invalid user address");
        // Return zero address as placeholder for compatibility
        // In a full implementation, this would be stored and retrieved
        return address(0);
    }
    
    /**
     * @dev Delete a user's identity (compatibility function)
     * @param userAddress Address of the user to delete
     */
    function deleteIdentity(address userAddress) external onlyRole(AGENT_ROLE) {
        require(userAddress != address(0), "WorkingWrapper: invalid user address");
        
        // Note: The underlying registry doesn't support deletion
        // This is a compatibility function that emits an event
        emit IdentityDeleted(userAddress);
    }
    
    /**
     * @dev Update a user's country (compatibility function)
     * @param userAddress Address of the user
     * @param country New country code
     */
    function updateCountry(address userAddress, uint16 country) external onlyRole(AGENT_ROLE) {
        require(userAddress != address(0), "WorkingWrapper: invalid user address");
        require(country > 0, "WorkingWrapper: invalid country code");
        
        // Note: The underlying registry doesn't support country updates
        // This is a compatibility function that emits an event
        emit CountryUpdated(userAddress, country);
    }
    
    /**
     * @dev Batch register multiple identities
     * @param userAddresses Array of user addresses
     * @param onchainIds Array of OnchainID addresses (ignored for compatibility)
     * @param countries Array of country codes
     */
    function batchRegisterIdentity(
        address[] calldata userAddresses,
        address[] calldata onchainIds,
        uint16[] calldata countries
    ) external onlyRole(AGENT_ROLE) {
        require(
            userAddresses.length == onchainIds.length &&
            userAddresses.length == countries.length,
            "WorkingWrapper: array length mismatch"
        );

        for (uint256 i = 0; i < userAddresses.length; i++) {
            require(userAddresses[i] != address(0), "WorkingWrapper: invalid user address");
            require(countries[i] > 0, "WorkingWrapper: invalid country code");

            underlyingRegistry.registerIdentity(userAddresses[i], countries[i]);
            emit IdentityRegistered(userAddresses[i], onchainIds[i], countries[i]);
        }
    }
    
    /**
     * @dev Add an agent who can register identities
     * @param agent Address of the new agent
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "WorkingWrapper: invalid agent address");
        _grantRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Remove an agent
     * @param agent Address of the agent to remove
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Check if an address is an agent
     * @param agent Address to check
     * @return bool True if the address is an agent
     */
    function isAgent(address agent) external view returns (bool) {
        return hasRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Get the underlying registry address
     * @return address Address of the underlying registry
     */
    function getUnderlyingRegistry() external view returns (address) {
        return address(underlyingRegistry);
    }
    
    /**
     * @dev Update the underlying registry (admin only)
     * @param newRegistry Address of the new underlying registry
     */
    function updateUnderlyingRegistry(address newRegistry) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(newRegistry != address(0), "WorkingWrapper: invalid registry address");
        underlyingRegistry = IWorkingUnderlyingRegistry(newRegistry);
    }
    
    /**
     * @dev Authorize upgrade (required by UUPSUpgradeable)
     * @param newImplementation Address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation) 
        internal 
        override 
        onlyRole(DEFAULT_ADMIN_ROLE) 
    {
        // Additional upgrade authorization logic can be added here
    }
    
    /**
     * @dev Get the version of this contract
     * @return string Version string
     */
    function version() external pure returns (string memory) {
        return "1.0.0";
    }
}
