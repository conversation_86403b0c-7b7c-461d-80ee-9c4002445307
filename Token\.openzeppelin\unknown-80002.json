{"manifestVersion": "3.2", "proxies": [{"address": "0x7c04b9cB950652a5f4699bC19DA3846f2ae152bB", "txHash": "0x8f49575cdbba0475120d4dbcb28bbac259a9e4c435bec7396bc43e2b25240d6c", "kind": "uups"}, {"address": "0x516238C9f951DC21c8dE2C8FAAe476C265Cd741D", "txHash": "0xb08e5e5be07278d1b2b4c19bd43c885cc97632539847b537ed49860716349bb7", "kind": "uups"}, {"address": "0x29cb357b34831F38Ca8DC1c15A45B36c19dDCc7f", "txHash": "0xcb49127fe7f6e0c17d2e727acb19a37d9158162b8536a3ebd2ef27c1a178a234", "kind": "uups"}, {"address": "0x3B2676F95C045fDE89884A9528f1B1F7BB9F63A6", "txHash": "0x37d4e57bfc98a264745e63cfaac412fe03b595a372bcd5ec7abd76434cc58a3a", "kind": "uups"}, {"address": "0x651BdA6c84743BC6099C16a5D81c039AB02E5682", "txHash": "0xc1805803ed73f6a1fa6254675621046ddcd9505ccf4dd39228b6af0fc3efa08c", "kind": "uups"}, {"address": "0xE14f9Cc205bC45E718DDC0E4A4900b41e66e4041", "txHash": "0x5983f2ad81e1087e9658ad19fd1a0ef1382e2e87dc13943b7bf88f7fabd28675", "kind": "uups"}, {"address": "0xafcdF8d6bcCf5BF5004c633C41a30D202B10b951", "txHash": "0x844cba4122e7c379086104d181d30f00566b1cc794618eb440aaa581146688ac", "kind": "uups"}, {"address": "0x129E04323E4c9bFBD097489473d8523E4015Bfc3", "txHash": "0x0e593b041394b70c611b6ec872d6c911aff132b370adf0943129504b497cabd4", "kind": "uups"}, {"address": "0xA2b73E82712AE920D5CBA00f32496592FcCBD1F2", "txHash": "0x57bcefcac8e383a69c2ab5633a0e7e05c3b730800b6b08997979135c14531262", "kind": "uups"}, {"address": "0xe83dD067Eb1Bb12ee825Adc49e1AD8291007E9E4", "txHash": "0x9a843a1ec9ec5bac355cfb64000f2dbd584227cf37316a9ae1866c88222e5e90", "kind": "uups"}, {"address": "0x7cAcf54F5f2672c9C80ae7FcceF93fA4c89FAb12", "txHash": "0x7e2eeac4704d1d1c4b14331220de3cb4737d0e96ead011d032649efb64b9fffd", "kind": "uups"}, {"address": "0xb7a53dC340C2E5e823002B174629e2a44B8ed815", "txHash": "0xa1e245e491ef5932df8a662ddda7376a0b904e6fb88637f35eada302b73699ca", "kind": "uups"}, {"address": "0x1201Ff51382A5FCE89b1fBbEa970D81C9357509D", "txHash": "0x05c22faac5cba5869574dfea3fd5fd6911ac39334b0f133ab04c81bf9455d1af", "kind": "uups"}, {"address": "0x8A51Dca86b02536206E2fb48674A5834C4b3Fa92", "txHash": "0xbb98599921ec6930b23f6cc87e98b3f884c07734b5fda820bed14ba58de01fc0", "kind": "uups"}, {"address": "0xbC7CfcC7ffB09747a381ed82369589706159a255", "txHash": "0xa820ff0bfdea38724e30545970346a8d2b963c71c5d493127050a3608e2f4eb7", "kind": "uups"}, {"address": "0xF5f155EdA46107A9dADc86804b43f006d7Df67Ff", "kind": "uups"}, {"address": "0x937feAB7Cd3Dc2C26355c2cf3Bb0662244f13f3f", "txHash": "0xe5f70584bf525fc875bd0a5c75e0289eddaad652a9b11a9c404b356594386fe8", "kind": "uups"}, {"address": "0xcF8814F44705c6387393EF0aaE9e0dAf46eE96BE", "txHash": "0x85d4a51f7be83d7f9d80166010e00b921fe67deabbb21adb941933604b7a63e5", "kind": "uups"}, {"address": "0xd7480595E847050e79D5fe20DfFb3E528923938B", "txHash": "0x4804fbe2b53eab3c5c1c743bd747a55c5e9f2d2bdfcabbdc661f6cbd9cb8174d", "kind": "uups"}, {"address": "0x31268482e29C0E11D35c0C985dD82709f2E4a5cf", "txHash": "0x02a005ae07be284ceea9b183d7d53d2d796d0cbad7ffc4d464f6f33e49517708", "kind": "uups"}, {"address": "0x0448C44e27Fe2da1304a20a516b99EEBE896A892", "txHash": "0xb3891c82c1278756d577e6fdcdaf7f6597fa0fbdb52ddd86f4d3e3d673be89a6", "kind": "uups"}, {"address": "0x0Aebe9f451E22f3fD0Bf7bB29087E4C569c06184", "txHash": "0xfceaec0e2f1808c393d8465f9defb4fb18ca8909df8cc0c69072c4274c6ac948", "kind": "uups"}, {"address": "0xFD22E10b0a453aF163CAE77d0667b1EFFF53fE69", "txHash": "0x11f779905c667aa8264ec2b77cc37815ed3ad22e03025507de6a132c2afd3bdd", "kind": "uups"}, {"address": "0x72bb3FdE3363569e1a7A30C03Be7Bbf1d868b24f", "txHash": "0x54cdc93ed4dd51288df887f9d1b5a9dc732af364fa39c2b681cecd8830c62f99", "kind": "uups"}, {"address": "0x65ad387AD6C1406BfEAF470633E6DC3f1e85beA6", "txHash": "0x4c7c3bd86d090c2a001414860985de34a777769ae87b63403eeba6d655b39917", "kind": "uups"}, {"address": "0x18b368D17112eB2d6dEDc5ea4C42490cCf956bbF", "txHash": "0x654c9b05a35110bc4d7bf6f76df6672f2c3e9d6616e49b15b0446f5f07d03588", "kind": "uups"}, {"address": "0x4f0aDcb10F1c87a39cC88A6d33D31FE3575c2663", "txHash": "0xed29981d384c88ed892adbb919804e01789263517b5d8878fe86a8f6ba3cd89b", "kind": "uups"}, {"address": "0x00293A948b8a317A2B99c1ad36bE71Af989EDE84", "txHash": "0x5dfa56fc7252c7c5dd9b2e4fceb4ad93e57cf406b4027f3cb2f1d2a5a7af09d2", "kind": "uups"}, {"address": "0xeB54F0A3d6cB613CD9A188d828A4b581ee851125", "txHash": "0xe3b7cf60124df7e87c43cb6417d345cfb7a6262d127dd0dc9c1c7f2ca172d940", "kind": "uups"}, {"address": "0xFd3F03cD4336A63DC1A19a10fF1F72ba983c9408", "txHash": "0x0bab1f0ccf9442ff9c022eb0f52ea8fe4a48ba47d247cf5f383ad2c8d03b2b1d", "kind": "uups"}, {"address": "0xf88284b7F1329fc93FAA3DA7490e58FAB607Fa48", "txHash": "0xacbadc1377379e13b71dd6d0fd630aaf6a4a4c0f935460d6410089563d43807b", "kind": "uups"}, {"address": "0x3dE1F2f6B60a60A76c6bCf35C9478c790791A4a2", "txHash": "0x80d766db0bce4e9747ffecc6fafb7f472e701605f31759addff2087ae18e7002", "kind": "uups"}, {"address": "0xAFC8C1bd6Ad21E30f7d10aF8Beb7203F10EF5D0e", "txHash": "0x0b5a1e267c491e8ea791377cc7b20cd7fa24fc33a8c5ba7184eaa4e280545696", "kind": "uups"}, {"address": "0x53665E1930EDf9915ece3d844f76811653591711", "txHash": "0xf646a4a0c3fb131c631510ac4b0d2dcb05c908197c562b0ba36c89db97172c11", "kind": "uups"}, {"address": "0x62E848c9D63e3e248096D8A426bFe6A27614Ec18", "txHash": "0x96723e42674d2551fca8b4c9ef2c90dfdbace0a5b91a6403294b3ffd591d64eb", "kind": "uups"}, {"address": "0x13ce95b9C64841e535D99B8c78117E590aA2fc14", "txHash": "0x935b4f5580b0015e51abe48c53ea8a6da45156975d0a3c77202ebaeaf9d7ce91", "kind": "uups"}, {"address": "0x1B7b67597dF3Ac388a71f9C74235Fa04771d77bD", "txHash": "0x60f7234e47eb5b86b5e11ad1084e5ec99c1f165182c27eb4502f1183b2bb9f14", "kind": "uups"}, {"address": "0xF3dA3dCac8F3AcCf3A814c603605739D8F4A96AD", "txHash": "0xc002068bfc6c458051d85dbf2e66fbf9846bccf508a9cd0393975c08f0646b4c", "kind": "uups"}, {"address": "0x45AF99356e4336aF12E77d5eeb1896776412cf13", "txHash": "0x53615ffdb7d00ea25c3363a7d45281b1d54b98631efb03cf80ade91c595a70db", "kind": "uups"}, {"address": "0x7eb7fdb0293Cd008A4014B950A74AC473D7331d4", "txHash": "0xe081fb46de118b733d0ce15ef17a90aabd71d5683fc461efda16121b721fcf3c", "kind": "uups"}, {"address": "0x60863Efa9e1B38ea61c717Eb5DBD15C74D657827", "txHash": "0x1016f0b4f1ac91672f413a70a4fbb601d5aecdc9aeb4f5ff9ef2552f2da6be84", "kind": "uups"}, {"address": "0xB918d40F1c68682F4f0895c6ABD4EF4536C45EdC", "txHash": "0x3a9c37a0891f62a4b91c5855d97710801c103654fa548edb9184126d263c1e26", "kind": "uups"}, {"address": "0x50F5c89C577D97E2809BE4270bE340c146C83D42", "txHash": "0x0b087357542ca166cc09f2d5e876bf572e5bc49adf0cee284f849978267b592e", "kind": "uups"}, {"address": "0xc3359Ca44CD15834fFf950db451B6a2C27cb123C", "txHash": "0xc85c67cdac3d6fb0839a0fa96ea9af18dbb3dbfaf49dac920cd0d4893da15da8", "kind": "uups"}, {"address": "0x126a0fB8875a53cd76b446CD8096873De3F2bbf2", "txHash": "0x15810edc04216a9b3fb6214d645290075a1a6b9c667655375c37da54ba12e60e", "kind": "uups"}, {"address": "0x138985d8098d9a4a93C68Ff1753Fbaf530CB7d9b", "txHash": "0x4a18d35976c976609cc3f5fb07f7db36d50246961c808d289d2f113f9957a835", "kind": "uups"}, {"address": "0xECE4870943391b83819237c8ebdeCC08A826C4D5", "txHash": "0xdb30c83c2bc88198f0bffc46b97b4f12ba2dca9ebdfc20d025929bfd03a51629", "kind": "uups"}, {"address": "0x2dcf5aB324aA040915A534252c41C3A59764C889", "txHash": "0x072065b709de98a2a8d96060f8f14681a0f01460bab7da9d10df9ce684facdb7", "kind": "uups"}, {"address": "0xccF1aD4bd39054FDCF8DAD41545ef219373329FD", "txHash": "0x5a46f5c19f8a094a4f3e59d2902797a85676646d3bb459051b39d96644258306", "kind": "uups"}, {"address": "0x7eAbc8e25a06De422f8Ea2BD2C3b710c41B4c697", "txHash": "0xdefb5ad83ce3c4d575f2438592ba2da5111d09fb4d9f6599584eb7ea40932534", "kind": "uups"}, {"address": "0x90c03aEFbD16385b3cE89afE3Fa4e7108FBA25c8", "txHash": "0x5f7dddb47820ab2a32e33fedbdd707c6f6679412f011cdaa133a087551394b43", "kind": "uups"}, {"address": "0x132d3F31c30f9816824C09F91044A4CBF9c7d76d", "txHash": "0x319b1b62680828a50ca63b20ccc53f83d8526e46edbf5785e88b1a33b2ed5bee", "kind": "uups"}, {"address": "0xC6357207E3bf00b1458dB1c5f539060E77b1D1CC", "txHash": "0xdd7f7d2d0446ab74c1cff530ed065c09c914549a58ae6c37395adbc343e33d4b", "kind": "uups"}, {"address": "0x2EE036202577e25150733B49bD4fa8C186916f24", "txHash": "0x4c6420298c4db57d59006685851e5552b084e0ce1555345f4c29577ce4c8f87d", "kind": "uups"}, {"address": "0x0BB46A12875fA19826a712D83668d2899F566c5D", "txHash": "0x6783355f37d83e486efbcd6a6fab8e1ea57799263d9c1c4ed45a537d37eb76dc", "kind": "uups"}, {"address": "0xbD2A8343be92215bB64cE002e899A80e6B0C6C20", "txHash": "0x46656994c376da91d4d9e33edf4b1c618fdf1ccf7ff44c617056daccae634ac9", "kind": "uups"}, {"address": "0x006Cd2a7d7C2D6fE24E7b0f3DAc13Ecda63be351", "txHash": "0x30678585594bb9687eb2c72d6cf516c334c9dcd63651e48b633ac23f55ff4275", "kind": "uups"}, {"address": "0x735fc2AD0a5C0E1E9e665ECa95bCD5f2E3d59B3F", "txHash": "0x38ff212813dd1f4c3289e5312825158d3a60ee45b9ea7e05b477e48114685bb0", "kind": "uups"}, {"address": "0xAfa73795fe4F80dD647444Ef7889cEf64Cf294EB", "txHash": "0x4a10fb9352803f5e687fcfcda95bd888725b882f0f84b1b5de44846307fcc758", "kind": "uups"}, {"address": "0x983aef02E641cbd1004d9842f60f60E8EA3F9aA1", "txHash": "0xe6a17d6c34c183eb95ecb96d0f94abc3d0a8b0db752ca295d39584fc26eaebb1", "kind": "uups"}, {"address": "0xCaaD1BeF8bEE2D211737e95d1E8AEbCaC42248AA", "txHash": "0xc8833e544c29c396d8deb10c4daab7f0e7ff88a7b8d98bd248217eca1ded6da0", "kind": "uups"}, {"address": "0x7Cf1C59DF83f640b51e63925DB0D6D81E55C9602", "txHash": "0x393de23784f26f203a0034b73e4706adb508133729548a8a6353ff10e897bc89", "kind": "uups"}, {"address": "0xB3aBb2d184F9ee00eDd5D98e24dA6E00adAaD15F", "txHash": "0x95cba375354c5a34ae383af43368b881b38dca1461b947675d9a10ded5595675", "kind": "uups"}, {"address": "0xF3D6EfaFe627EDc1C852a5C0B4faAd8620934F25", "txHash": "0x4077f768255d81584d8024b2437140e63b5c3847e153c994faa8764e6c3ec924", "kind": "uups"}, {"address": "0x6CcaDf8547b53057D034F82576b6e7C72b6C874E", "txHash": "0x5a2acf196d11ca87f9fa38966d08a2478c812d337f5f95c95db3584e24974429", "kind": "uups"}, {"address": "0xCDA4Bb373a560D26EeBE032e8E2839e4d43bba34", "txHash": "0x248b49f1c7da1eff2e2e3b76cb3ff010752c5534f942ccf01b3703f5c993220c", "kind": "uups"}, {"address": "0xEE7b4aaC69e91787d782aa67c2Ed323c0597D22C", "txHash": "0x38b58a84cb2259afb01ffdbfbfdf0618069d6067ec5a721dc2c0b2bf1b129945", "kind": "uups"}, {"address": "0xbBa850Ed7D34ACa00b8889138a312d040f3ef718", "txHash": "0x8fd667cb5a13355b905503dd140880f4dfd72a4e6fb1ec9bb3dcf763e5b154e6", "kind": "uups"}, {"address": "0x0a4e04698B5ED6c7E7935F9dBb408D042c6520B9", "txHash": "0xd1f8fec3c1baff291479486771d23bdc67aabb8a6cd1f958eb1431d92f5f8d88", "kind": "uups"}, {"address": "0xaEcF0D82108981C7f80D5aBf179d54047A243ba1", "txHash": "0x51cdc1b26bc284ab0590b2a7ce0b17235ce9e17166a14a706002bd4379c83aff", "kind": "uups"}, {"address": "0x4538047fB565D809856CBd368521CD90A07b57fB", "kind": "uups"}, {"address": "0xb3631a73Ed3Be2e5DCF860FBBbECc607E8727da6", "txHash": "0x705657b983feafb8833d9cfdef8d2ec4c79f86a7c092dc3e054996479e448dda", "kind": "uups"}, {"address": "0x161119493AEff734B0a89f9d21dfeFfbD1b5D681", "txHash": "0xfa0061a06ea7770c6528f329b482f585bf53a16b9c8edb5461526ee53ac0665a", "kind": "uups"}, {"address": "0xf053B3C0DC43B9DD0B3D25A35175887cAFFe31da", "txHash": "0xe12eb0c8b83b3cb3075de7100f83479fe77215bb3c772d76d0f48f3ca67be548", "kind": "uups"}, {"address": "0x1281a057881CBE94DC2A79561275aa6B35Bf7854", "txHash": "0xb8393ba5eea490323cc56034211d70f7670a29c8387807c9167c5f2a17523375", "kind": "uups"}, {"address": "0x43F8C2736d22DF122c0a3360d640D2E5c9464AF0", "txHash": "0x46cf74be2446664eff3eabe988dabaa10602653a3bafb38a294d5c0408f1c374", "kind": "uups"}, {"address": "0x1926D76Dd8bB97AC6D3344E3944290943Cf10C6f", "txHash": "0x76cbdc1484c8cdba4253227cf1328a357da25f8d6e9a730eae77394616d0466e", "kind": "uups"}, {"address": "0x1E7f798769C6e91Ad57cF08ac5CF6e575Ca6eDEa", "txHash": "0xaadd68e623d78bc44252633febcf392c49b35c6d0c986477c27f62d6777b836f", "kind": "uups"}, {"address": "0xb22229354f8EEf867ee35D36fc2830278394588c", "txHash": "0x3c97d680adf1bde23c673e6badaa0e496975b19c18f58f4642ad13dd8908fb65", "kind": "uups"}, {"address": "0x6CBea7Bc5032949005cA6e7c529Cecdc5e305f5c", "txHash": "0x105130406a6c9eda7b5c09af93500d6b78d69182a276c0f8f5f4f949dd5f1366", "kind": "uups"}, {"address": "0x84c888cC6aACe3BAEaa8d3a09c83AF783B7baB12", "txHash": "0x4ebf821a9e57f6fde038125e57335d39a102bb5e5ff89817b1f224b7851ba426", "kind": "uups"}, {"address": "0xDdCd46Df6e52777b5b97b9055777569B5cAB11Db", "txHash": "0x403cddfde58536e28699644b263c668b5723cabcab215c127c9b2a837dbe7c00", "kind": "uups"}, {"address": "0x74d33Bfa62f999EB2DdF4f89AC1B0D8DC4515dDF", "txHash": "0xeedc0c2270ababea88e202a84f8829365bbb75252315affa390eb231cbc3eb42", "kind": "uups"}, {"address": "0x0653f36797F6664715Fe4f82cACd9f944628c9dE", "txHash": "0x1bc409d18da74025bf972ab72330f7715ce408b1139e3de71ef96afd23ad4e34", "kind": "uups"}, {"address": "0x42420E2302B5d2817C16523dd8b8a548a8B09b0e", "txHash": "0xc0f1e45addd3288f0c79f3245fdae3b0b308764e969525dc2871f86492836ce0", "kind": "uups"}, {"address": "0x15906AbcC4Fbd0F1Ea50FBE8775B40F92828a854", "txHash": "0xaacd0314c46836370da16b8d98d255a9fcbb79cc61a8908ed3573e51f37afd32", "kind": "uups"}, {"address": "0x2b6BEccdebdcb2B7aa413126b1629E841874b462", "txHash": "0x8b1d41c57580eda9e33b350d3b35af84a8badb6e3b7e287dc48fd5307e609e74", "kind": "uups"}, {"address": "0x7D341dd32c06D07eC9fC34D5Ba630B5E5BDdc0C2", "txHash": "0x20190171cb27c93e7e542f6c53195edf695f8c3182a1b751a4e8fe837fab71dc", "kind": "uups"}, {"address": "0x70B28fd44f04D9BdCfA8075c99d9E21b9B242AEF", "txHash": "0x1e89df46631eeaaf095cdd95e628e98de40c1d2b17af17c7f83dfcca84daf1f4", "kind": "uups"}, {"address": "0x24d076a55E5cc24c78a2213018b6d449Fac3e123", "txHash": "0xb15c72b79a85385d1800ea7fc370f79e103a37ec1aa68d72d08676dfc523d8b4", "kind": "uups"}, {"address": "0x62e3C02A53f6089B08E768f0C848c9E36c00d669", "txHash": "0xd4473544737dfd618075f7c4c206553de42a1d7805d1cf38d493280890c05e1e", "kind": "uups"}, {"address": "0x4d94452BC19aBf54494D3199a755343B03707b4c", "txHash": "0x7af9b0962de4ba2882acdc386d1a3c4efa9584d922caa4e34b026e8797f5f38d", "kind": "uups"}, {"address": "0xd418B733Aba8000eC2EAEad050054bBD76405ee5", "txHash": "0x89c8e0acc7df8d4777de72b8edfb671dfa13d2740003d4401c7859ef57b6d225", "kind": "uups"}, {"address": "0x4b5edD2D969678209719730B237cc1852c58c64D", "txHash": "0x4cf7a73064f961ce3cfe76eda73d78983602c84e2fe3a093168a687e459a157e", "kind": "uups"}, {"address": "0x1B56b36561E2C9d84471e138550f7eD3Aef2F510", "txHash": "0x22f556e17332eee05e4e4d447c9bcb42940f785fd0c1a8393e006a926b2b878b", "kind": "uups"}, {"address": "0x036788CBf4bd674eF265bb7c5F66501AAbAE5730", "txHash": "0x1ce158c1c7c93d7a0a24c3d4e9cf8a363283a739843af745799078385c1dd3b6", "kind": "uups"}, {"address": "0x5eE41c51C5Def6740ED0fA1603c445300e12994a", "txHash": "0x12b6588689cfc8234c878ee66a5f70d3fa8d09658cdef2db7a714bf2414c33e2", "kind": "uups"}, {"address": "0x8392796F28e2936121dA822F588e21b40ECFFd31", "txHash": "0xa374794076f14238b12b693735c7a1615ca62644f17fb2ef1c812d6f7389308a", "kind": "uups"}, {"address": "0x41fC579dAa2bE8721095fD6e41d4e1AF158c6A2B", "txHash": "0xd9be1eddef348242f783066cc65418e5ba5bfc71c1145980c71a1d47761ef296", "kind": "uups"}, {"address": "0xc6441B49Ffec3b80F9e3CDC0e8A4F1826Ee5A241", "txHash": "0x122d86adf94130dadb255031edbca816f09019637eb46dffa8b6a4ba6677d5fc", "kind": "uups"}, {"address": "0xf0d09ebc4A92D3D12E0b49B31FB48F8ADBc2EC9c", "txHash": "0x3ada0e9d3554e983d71eaf78e200beabfb3041cf0c6e2c5d1edd8bb477d7aec5", "kind": "uups"}, {"address": "0x59ed2252da69eE58978F81B97E135A4b228d7FAc", "txHash": "0x133b97dac64902296c7e46b9627a6ae39b6acbc9feff315ebb8836f7300e632c", "kind": "uups"}, {"address": "0x6DB181232Dd52faAD98b4973bd72f24263038D01", "txHash": "0xebf3dfce4e0fa8a77511e3d3c4e1e083993bfcb561a065e5c781c3a67754402c", "kind": "uups"}, {"address": "0x7bDA3728aa0479dd1fc05C833220C349258CAF5E", "txHash": "0xbe4f38c1878a818dfc50140bc6898d7ba839a7d39c0c250cd8a11b952745703c", "kind": "uups"}, {"address": "0x5aF0cF73072cEDb05D1195E9388c25ea41CfD24f", "txHash": "0xd78051cec73c444f0e368c8a6aefa48cac95367a30b7fba3db3e4fa441bfed8b", "kind": "uups"}, {"address": "0xF52E82f4eD4827f1B9aff68C1845220C614Fc054", "txHash": "0x02cca5ee438110afb2905c1ca2a3a33c687a2bc75572e71e12ba9948cf41b1f7", "kind": "uups"}, {"address": "0x03112fB317Ac2FFaDF158581896dbB2dC3B9865c", "txHash": "0xd8c50ebd206c18ea2022d7a719786ae4b9a2d0f4122050de36966542c1601fbc", "kind": "uups"}, {"address": "0x4F90B12977Af1BAfF3D659F03F79b6909a589c9C", "txHash": "0x279df8a5a64751ae0043f57142856403710885f580e73d8a33c241cb7a580309", "kind": "uups"}, {"address": "0x25a8397E44bFfBD3e4A862445DBeFd4fC87fAc44", "txHash": "0x3f766b6910e2a1e3448812c6c5571c59f07122a2da34cb519654d309d54bb635", "kind": "uups"}, {"address": "0xD1054f9E66a52094311E574BF72AeB6eB845A9e4", "txHash": "0xa442773fa91299ebc22553018e45a1a1154dfc516bab3d2aa0d92f873a3ff06b", "kind": "uups"}, {"address": "0x27658e881b5E38f2637738e2b7614A562c667f5c", "txHash": "0xa40d128a24168ba77233a56602d0c97ac0f67b62a4fd54f4ea36b87de9227544", "kind": "uups"}, {"address": "0xBC931A42cd033abd009C16BB5F1c54C7585192bb", "txHash": "0x548ffc8806c3f06fc6735f0d3b7f72a6b328cac18b7124b779e39d9e724cb4ee", "kind": "uups"}, {"address": "0xA9d435B7433d6A80C9556B90600339b0A51387a9", "txHash": "0x80ea9394f3aad76fb72fec8d4b99e1bd7e2d6c7b64a6b20644ffb7aa2db7bc26", "kind": "uups"}, {"address": "0x1ADA99eC9ab7906D07Dd3587dE905D2fDcfb5EeE", "txHash": "0x87c41edb0a4a7d62da977926df690a7b5ed63971f226f335541925225458d22b", "kind": "uups"}, {"address": "0x68401BCa532776D8264872Db9d72A375f32359Ba", "txHash": "0xd71b74a11818dadf69d83cd21aacf7d656ee6320dbaeb74ab9ec2e8c149235b3", "kind": "uups"}], "impls": {"495d52da6f87065b02d685ed742c140a03fd1b313d3e253fcb9fb250cc5e3312": {"address": "0x284B53bBD6Be2bF17E42aB97B1b99B249643258D", "txHash": "0xc1398afb0c9dd1d38f3a0431e3d26f1b3a4fdda841339adb93679ead90181618", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "claims", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)1914_storage)))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:35"}, {"label": "claimIds", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:38"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_mapping(t_uint256,t_bool))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:41"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_bytes_storage": {"label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))": {"label": "mapping(address => mapping(uint256 => bytes32[]))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_bool))": {"label": "mapping(address => mapping(uint256 => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)1914_storage)))": {"label": "mapping(address => mapping(uint256 => mapping(bytes32 => struct ClaimRegistry.Claim)))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(Claim)1914_storage)": {"label": "mapping(bytes32 => struct ClaimRegistry.Claim)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_array(t_bytes32)dyn_storage)": {"label": "mapping(uint256 => bytes32[])", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)1914_storage))": {"label": "mapping(uint256 => mapping(bytes32 => struct ClaimRegistry.Claim))", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Claim)1914_storage": {"label": "struct ClaimRegistry.Claim", "members": [{"label": "claimType", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "issuer", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "signature", "type": "t_bytes_storage", "offset": 0, "slot": "2"}, {"label": "data", "type": "t_bytes_storage", "offset": 0, "slot": "3"}, {"label": "uri", "type": "t_string_storage", "offset": 0, "slot": "4"}, {"label": "issuedAt", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "expiresAt", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "revoked", "type": "t_bool", "offset": 0, "slot": "7"}], "numberOfBytes": "256"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "caad62e8ab9e3b9f375ce0c69d31a8c07ff8b227608af6c5d5f3b8883479e859": {"address": "0xf5b9671368f4C0a582105ff020908f387f827a57", "txHash": "0x049de41040337f424b3d5dc578d2776b1089967248cba864eae3746434cee07a", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_identities", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_struct(Identity)5224_storage)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:41"}, {"label": "_countryInvestorCount", "offset": 0, "slot": "1", "type": "t_mapping(t_uint16,t_uint256)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:42"}, {"label": "_restrictedCountries", "offset": 0, "slot": "2", "type": "t_mapping(t_uint16,t_bool)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:43"}, {"label": "_verifiedAddresses", "offset": 0, "slot": "3", "type": "t_array(t_address)dyn_storage", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:46"}, {"label": "_verifiedAddressIndex", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_uint256)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:47"}, {"label": "claimRegistry", "offset": 0, "slot": "5", "type": "t_contract(ClaimRegistry)3841", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:50"}, {"label": "_requiredClaimTopics", "offset": 0, "slot": "6", "type": "t_array(t_uint256)dyn_storage", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:53"}, {"label": "_isRequiredClaimTopic", "offset": 0, "slot": "7", "type": "t_mapping(t_uint256,t_bool)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:54"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_contract(ClaimRegistry)3841": {"label": "contract ClaimRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(Identity)5224_storage)": {"label": "mapping(address => struct IdentityRegistry.Identity)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_bool)": {"label": "mapping(uint16 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_uint256)": {"label": "mapping(uint16 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_struct(Identity)5224_storage": {"label": "struct IdentityRegistry.Identity", "members": [{"label": "isVerified", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "t_bool", "offset": 1, "slot": "0"}, {"label": "isKycApproved", "type": "t_bool", "offset": 2, "slot": "0"}, {"label": "isFrozen", "type": "t_bool", "offset": 3, "slot": "0"}, {"label": "country", "type": "t_uint16", "offset": 4, "slot": "0"}, {"label": "registeredAt", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "updatedAt", "type": "t_uint256", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "119433ed1e3555bdf0ad71e3b3fda8a4589bba3ce27b4ace712bb0d314b8010f": {"address": "0x2E97eFACa4517047B2B91e74D56B1eBe0BEAFD48", "txHash": "0x1cc19117e90d7e19488c1e94b1a220ff00febfd34fbe9277039312c89911ecc7", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IdentityRegistry)6894", "contract": "Compliance", "src": "contracts\\Compliance.sol:44"}, {"label": "tokenAddress", "offset": 0, "slot": "1", "type": "t_address", "contract": "Compliance", "src": "contracts\\Compliance.sol:45"}, {"label": "_complianceRules", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_struct(ComplianceRule)3884_storage)", "contract": "Compliance", "src": "contracts\\Compliance.sol:48"}, {"label": "_activeRuleIds", "offset": 0, "slot": "3", "type": "t_array(t_bytes32)dyn_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:49"}, {"label": "_isActiveRule", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_bool)", "contract": "Compliance", "src": "contracts\\Compliance.sol:50"}, {"label": "_transferData", "offset": 0, "slot": "5", "type": "t_struct(TransferData)3897_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:53"}, {"label": "_holderBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:56"}, {"label": "_holders", "offset": 0, "slot": "10", "type": "t_array(t_address)dyn_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:57"}, {"label": "_holderIndex", "offset": 0, "slot": "11", "type": "t_mapping(t_address,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:58"}, {"label": "_totalHolders", "offset": 0, "slot": "12", "type": "t_uint256", "contract": "Compliance", "src": "contracts\\Compliance.sol:59"}, {"label": "_countryHolderCount", "offset": 0, "slot": "13", "type": "t_mapping(t_uint16,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:62"}, {"label": "_countryHolders", "offset": 0, "slot": "14", "type": "t_mapping(t_uint16,t_array(t_address)dyn_storage)", "contract": "Compliance", "src": "contracts\\Compliance.sol:63"}, {"label": "_countryHolderIndex", "offset": 0, "slot": "15", "type": "t_mapping(t_uint16,t_mapping(t_address,t_uint256))", "contract": "Compliance", "src": "contracts\\Compliance.sol:64"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_contract(IdentityRegistry)6894": {"label": "contract IdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(ComplianceRule)3884_storage)": {"label": "mapping(bytes32 => struct Compliance.ComplianceRule)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_array(t_address)dyn_storage)": {"label": "mapping(uint16 => address[])", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_bool)": {"label": "mapping(uint16 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_mapping(t_address,t_uint256))": {"label": "mapping(uint16 => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_uint256)": {"label": "mapping(uint16 => uint256)", "numberOfBytes": "32"}, "t_struct(ComplianceRule)3884_storage": {"label": "struct Compliance.ComplianceRule", "members": [{"label": "isActive", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "maxHolders", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "maxTokensPerHolder", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "maxTotalSupply", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "countryLimits", "type": "t_mapping(t_uint16,t_uint256)", "offset": 0, "slot": "4"}, {"label": "restrictedCountries", "type": "t_mapping(t_uint16,t_bool)", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_struct(TransferData)3897_storage": {"label": "struct Compliance.TransferData", "members": [{"label": "totalTransfers", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "lastTransferTime", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "transferCount", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "2"}, {"label": "lastTransferTimeByAddress", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "395e6d3666b96aa20d391510de89c302a358f81a4311d80240a8853d82e237f2": {"address": "0xdF80BFB83BA309d03da8317d5Fe8B80a869D7282", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:36"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:39"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:42"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:43"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:44"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:45"}, {"label": "_identityRegistry", "offset": 0, "slot": "6", "type": "t_contract(IdentityRegistry)7962", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:48"}, {"label": "_compliance", "offset": 0, "slot": "7", "type": "t_contract(Compliance)6228", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:49"}, {"label": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_contract(ICompleteWhitelist)15480", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:52"}, {"label": "_forcedTransferInProgress", "offset": 20, "slot": "8", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:55"}, {"label": "_approvedTransferInProgress", "offset": 21, "slot": "8", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:58"}, {"label": "_agentList", "offset": 0, "slot": "9", "type": "t_array(t_address)dyn_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:64"}, {"label": "_agentListIndex", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:66"}, {"label": "_conditionalTransfersEnabled", "offset": 0, "slot": "11", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:78"}, {"label": "_transferApprovals", "offset": 0, "slot": "12", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:79"}, {"label": "_transferNonces", "offset": 0, "slot": "13", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:80"}, {"label": "_transfer<PERSON><PERSON><PERSON>stEnabled", "offset": 0, "slot": "14", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:83"}, {"label": "_transfer<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "15", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:84"}, {"label": "_transferFeesEnabled", "offset": 0, "slot": "16", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:87"}, {"label": "_transferFeePercentage", "offset": 0, "slot": "17", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:88"}, {"label": "_feeCollector", "offset": 0, "slot": "18", "type": "t_address", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:89"}, {"label": "_agreementAcceptances", "offset": 0, "slot": "19", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:92"}, {"label": "_emergencyPaused", "offset": 0, "slot": "20", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:95"}, {"label": "_functionPaused", "offset": 0, "slot": "21", "type": "t_mapping(t_bytes4,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:96"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_bytes4": {"label": "bytes4", "numberOfBytes": "4"}, "t_contract(Compliance)6228": {"label": "contract Compliance", "numberOfBytes": "20"}, "t_contract(ICompleteWhitelist)15480": {"label": "contract ICompleteWhitelist", "numberOfBytes": "20"}, "t_contract(IdentityRegistry)7962": {"label": "contract IdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes4,t_bool)": {"label": "mapping(bytes4 => bool)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "536149335a23efa1cb2e674b19e253d7a0faee1a71aed64d44d6d38dc59e782e": {"address": "0xA384F74a18B92FDEce47C5b23dc1106F9099a42b", "txHash": "0x27fc31f9fbc3ceae5850e34fd4b5d231209bcdbfa258e5c0a38c0deb23fa202a", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:42"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:43"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:46"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:47"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:48"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:49"}, {"label": "_modules", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_address)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:52"}, {"label": "_authorizedModules", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:53"}, {"label": "_forcedTransferInProgress", "offset": 0, "slot": "8", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:56"}, {"label": "_moduleTransferInProgress", "offset": 1, "slot": "8", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "06e0b9fae9918170a2af98b0e8b5664ff76f813f9e65dc31a55a4604ce434cf1": {"address": "0xcdc29d9e18c223f8402813cED1f2B5B63498f146", "txHash": "0x0a42bd89573f93872307f680ad8ac10507a1e2302b0b6e96d7068fbcd8aa4f11", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "pendingUpgrades", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(PendingUpgrade)1922_storage)", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:52"}, {"label": "pendingUpgradeIds", "offset": 0, "slot": "1", "type": "t_array(t_bytes32)dyn_storage", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:53"}, {"label": "emergencyMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:56"}, {"label": "emergencyModeExpiry", "offset": 0, "slot": "3", "type": "t_uint256", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:57"}, {"label": "emergencyModeActivator", "offset": 0, "slot": "4", "type": "t_address", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:58"}, {"label": "moduleProxies", "offset": 0, "slot": "5", "type": "t_mapping(t_bytes32,t_address)", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:61"}, {"label": "proxyToModuleId", "offset": 0, "slot": "6", "type": "t_mapping(t_address,t_bytes32)", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:62"}, {"label": "registeredModules", "offset": 0, "slot": "7", "type": "t_array(t_bytes32)dyn_storage", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:63"}, {"label": "upgradeHistory", "offset": 0, "slot": "8", "type": "t_mapping(t_bytes32,t_array(t_struct(UpgradeRecord)1962_storage)dyn_storage)", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:76"}, {"label": "currentVersionIndex", "offset": 0, "slot": "9", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:79"}, {"label": "currentVersion", "offset": 0, "slot": "10", "type": "t_mapping(t_bytes32,t_string_storage)", "contract": "UpgradeManager", "src": "contracts\\UpgradeManager.sol:80"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)298_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_struct(UpgradeRecord)1962_storage)dyn_storage": {"label": "struct UpgradeManager.UpgradeRecord[]", "numberOfBytes": "32"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_array(t_struct(UpgradeRecord)1962_storage)dyn_storage)": {"label": "mapping(bytes32 => struct UpgradeManager.UpgradeRecord[])", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_string_storage)": {"label": "mapping(bytes32 => string)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(PendingUpgrade)1922_storage)": {"label": "mapping(bytes32 => struct UpgradeManager.PendingUpgrade)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(PendingUpgrade)1922_storage": {"label": "struct UpgradeManager.PendingUpgrade", "members": [{"label": "moduleId", "type": "t_bytes32", "offset": 0, "slot": "0"}, {"label": "proxy", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "newImplementation", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "executeTime", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "executed", "type": "t_bool", "offset": 0, "slot": "4"}, {"label": "cancelled", "type": "t_bool", "offset": 1, "slot": "4"}, {"label": "description", "type": "t_string_storage", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_struct(UpgradeRecord)1962_storage": {"label": "struct UpgradeManager.UpgradeRecord", "members": [{"label": "oldImplementation", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "newImplementation", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "executor", "type": "t_address", "offset": 0, "slot": "3"}, {"label": "version", "type": "t_string_storage", "offset": 0, "slot": "4"}, {"label": "isEmergency", "type": "t_bool", "offset": 0, "slot": "5"}, {"label": "description", "type": "t_string_storage", "offset": 0, "slot": "6"}], "numberOfBytes": "224"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "c215562ca43f4cd997693ce3388d292cb7a49be073ac7bb30af0f7d680b0ae62": {"address": "0x2b20548f3D34F177C14D012dC8DF69793E6A19B3", "txHash": "0xbb2ed98eb8da489953c197dde64d216d240d35d1560022aba4df9a8547121a10", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:42"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:43"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:46"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:47"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:48"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:49"}, {"label": "_modules", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_address)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:52"}, {"label": "_authorizedModules", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:53"}, {"label": "_forcedTransferInProgress", "offset": 0, "slot": "8", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:56"}, {"label": "_moduleTransferInProgress", "offset": 1, "slot": "8", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "7e5f04e06a585ea5094830ad8f5333643d734510083a416ca4d511c9bbc7bc79": {"address": "0xA095764d05ba96DC95Ec25e68FF68E7d9A6f957e", "txHash": "0x0a47c52a6f6b947e71a0bfb42b83e0210347e64c7468244c66f6b6867d5d9c47", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:42"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:43"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:46"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:47"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:48"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:49"}, {"label": "_modules", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_address)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:52"}, {"label": "_authorizedModules", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:53"}, {"label": "_forcedTransferInProgress", "offset": 0, "slot": "8", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:56"}, {"label": "_moduleTransferInProgress", "offset": 1, "slot": "8", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "allAddresses": ["0xA095764d05ba96DC95Ec25e68FF68E7d9A6f957e", "0xA4f70C1953165be63409e1F0Ff11c2D03522E8C2"]}, "adcaeb87f84d6ab3ff62fac4ea1ed4e643f6b3577dbf8751dffef22939fb90a8": {"address": "0x271CA04164b913Bc24204C8150940Fe3088Fa1B0", "txHash": "0xfdd262d3a7534c2651fa992ec038ec07cb7e3d406f7aab1bbadb39a3684217f4", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_nextClaimTypeId", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:18"}, {"label": "_claimNonces", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_uint256)", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:21"}, {"label": "claimTypes", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_struct(ClaimType)3058_storage)", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:33"}, {"label": "creatorClaimTypes", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:36"}, {"label": "claims", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)3085_storage)))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:50"}, {"label": "claimIds", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:53"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "6", "type": "t_mapping(t_address,t_mapping(t_uint256,t_bool))", "contract": "ClaimRegistry", "src": "contracts\\ClaimRegistry.sol:56"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_bytes_storage": {"label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"label": "mapping(address => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))": {"label": "mapping(address => mapping(uint256 => bytes32[]))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_bool))": {"label": "mapping(address => mapping(uint256 => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)3085_storage)))": {"label": "mapping(address => mapping(uint256 => mapping(bytes32 => struct ClaimRegistry.Claim)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(Claim)3085_storage)": {"label": "mapping(bytes32 => struct ClaimRegistry.Claim)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_array(t_bytes32)dyn_storage)": {"label": "mapping(uint256 => bytes32[])", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_bytes32,t_struct(Claim)3085_storage))": {"label": "mapping(uint256 => mapping(bytes32 => struct ClaimRegistry.Claim))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(ClaimType)3058_storage)": {"label": "mapping(uint256 => struct ClaimRegistry.ClaimType)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Claim)3085_storage": {"label": "struct ClaimRegistry.Claim", "members": [{"label": "claimType", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "issuer", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "signature", "type": "t_bytes_storage", "offset": 0, "slot": "2"}, {"label": "data", "type": "t_bytes_storage", "offset": 0, "slot": "3"}, {"label": "uri", "type": "t_string_storage", "offset": 0, "slot": "4"}, {"label": "issuedAt", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "expiresAt", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "revoked", "type": "t_bool", "offset": 0, "slot": "7"}], "numberOfBytes": "256"}, "t_struct(ClaimType)3058_storage": {"label": "struct ClaimRegistry.ClaimType", "members": [{"label": "id", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_string_storage", "offset": 0, "slot": "1"}, {"label": "description", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "creator", "type": "t_address", "offset": 0, "slot": "3"}, {"label": "createdAt", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "active", "type": "t_bool", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "63d9d06fd4e0f24a367d6225f95c71351cb9f7a552f5cf3dc0c945c2cfbfafc6": {"address": "0x5e6Ff2649a8b471EE87FED50484e1a65e1c4fc56", "txHash": "0xbbfbcb4d42b117746c389813ca4551625ea24e684703eaef9509254c65757496", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_identities", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_struct(Identity)5752_storage)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:44"}, {"label": "_countryInvestorCount", "offset": 0, "slot": "1", "type": "t_mapping(t_uint16,t_uint256)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:45"}, {"label": "_restrictedCountries", "offset": 0, "slot": "2", "type": "t_mapping(t_uint16,t_bool)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:46"}, {"label": "_verifiedAddresses", "offset": 0, "slot": "3", "type": "t_array(t_address)dyn_storage", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:49"}, {"label": "_verifiedAddressIndex", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_uint256)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:50"}, {"label": "claimRegistry", "offset": 0, "slot": "5", "type": "t_contract(ClaimRegistry)4284", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:53"}, {"label": "_requiredClaimTopics", "offset": 0, "slot": "6", "type": "t_array(t_uint256)dyn_storage", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:56"}, {"label": "_isRequiredClaimTopic", "offset": 0, "slot": "7", "type": "t_mapping(t_uint256,t_bool)", "contract": "IdentityRegistry", "src": "contracts\\IdentityRegistry.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_contract(ClaimRegistry)4284": {"label": "contract ClaimRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(Identity)5752_storage)": {"label": "mapping(address => struct IdentityRegistry.Identity)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_bool)": {"label": "mapping(uint16 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_uint256)": {"label": "mapping(uint16 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_struct(Identity)5752_storage": {"label": "struct IdentityRegistry.Identity", "members": [{"label": "isVerified", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "t_bool", "offset": 1, "slot": "0"}, {"label": "isKycApproved", "type": "t_bool", "offset": 2, "slot": "0"}, {"label": "isFrozen", "type": "t_bool", "offset": 3, "slot": "0"}, {"label": "country", "type": "t_uint16", "offset": 4, "slot": "0"}, {"label": "registeredAt", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "updatedAt", "type": "t_uint256", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "c0e288495a0da963f5c7ae9f1786bd68e14c38b785f75769acbcbc580b57964f": {"address": "0xC4267cE8202f02Def8f0aCb2d0546f7a87CFCF52", "txHash": "0x42e33525e9c1fc27d58a8c13bafb93ac8d886d5cb0b38af669261b6fcf65ff24", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IdentityRegistry)7436", "contract": "Compliance", "src": "contracts\\Compliance.sol:50"}, {"label": "tokenAddress", "offset": 0, "slot": "1", "type": "t_address", "contract": "Compliance", "src": "contracts\\Compliance.sol:51"}, {"label": "_complianceRules", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_struct(ComplianceRule)4347_storage)", "contract": "Compliance", "src": "contracts\\Compliance.sol:54"}, {"label": "_activeRuleIds", "offset": 0, "slot": "3", "type": "t_array(t_bytes32)dyn_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:55"}, {"label": "_isActiveRule", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_bool)", "contract": "Compliance", "src": "contracts\\Compliance.sol:56"}, {"label": "_transferData", "offset": 0, "slot": "5", "type": "t_struct(TransferData)4360_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:59"}, {"label": "_holderBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:62"}, {"label": "_holders", "offset": 0, "slot": "10", "type": "t_array(t_address)dyn_storage", "contract": "Compliance", "src": "contracts\\Compliance.sol:63"}, {"label": "_holderIndex", "offset": 0, "slot": "11", "type": "t_mapping(t_address,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:64"}, {"label": "_totalHolders", "offset": 0, "slot": "12", "type": "t_uint256", "contract": "Compliance", "src": "contracts\\Compliance.sol:65"}, {"label": "_countryHolderCount", "offset": 0, "slot": "13", "type": "t_mapping(t_uint16,t_uint256)", "contract": "Compliance", "src": "contracts\\Compliance.sol:68"}, {"label": "_countryHolders", "offset": 0, "slot": "14", "type": "t_mapping(t_uint16,t_array(t_address)dyn_storage)", "contract": "Compliance", "src": "contracts\\Compliance.sol:69"}, {"label": "_countryHolderIndex", "offset": 0, "slot": "15", "type": "t_mapping(t_uint16,t_mapping(t_address,t_uint256))", "contract": "Compliance", "src": "contracts\\Compliance.sol:70"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_contract(IdentityRegistry)7436": {"label": "contract IdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(ComplianceRule)4347_storage)": {"label": "mapping(bytes32 => struct Compliance.ComplianceRule)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_array(t_address)dyn_storage)": {"label": "mapping(uint16 => address[])", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_bool)": {"label": "mapping(uint16 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_mapping(t_address,t_uint256))": {"label": "mapping(uint16 => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_uint16,t_uint256)": {"label": "mapping(uint16 => uint256)", "numberOfBytes": "32"}, "t_struct(ComplianceRule)4347_storage": {"label": "struct Compliance.ComplianceRule", "members": [{"label": "isActive", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "maxHolders", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "maxTokensPerHolder", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "maxTotalSupply", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "countryLimits", "type": "t_mapping(t_uint16,t_uint256)", "offset": 0, "slot": "4"}, {"label": "restrictedCountries", "type": "t_mapping(t_uint16,t_bool)", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_struct(TransferData)4360_storage": {"label": "struct Compliance.TransferData", "members": [{"label": "totalTransfers", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "lastTransferTime", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "transferCount", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "2"}, {"label": "lastTransferTimeByAddress", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "e66eec0e47e2f60cdce3f2052592b0098a35993e9e47161b89944332d063dbae": {"address": "0x8dD405Eac1234cedC1291dD7e56028D8D057FA37", "txHash": "0x58ae1710a0f3404b43af0da9578ea1a1312c2aab7473c59b8132d00b3298c986", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_trustedIssuers", "offset": 0, "slot": "0", "type": "t_array(t_address)dyn_storage", "contract": "TrustedIssuersRegistry", "src": "contracts\\erc3643\\TrustedIssuersRegistry.sol:28"}, {"label": "_isTrusted<PERSON><PERSON>uer", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_bool)", "contract": "TrustedIssuersRegistry", "src": "contracts\\erc3643\\TrustedIssuersRegistry.sol:31"}, {"label": "_issuerClaimTopics", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "TrustedIssuersRegistry", "src": "contracts\\erc3643\\TrustedIssuersRegistry.sol:34"}, {"label": "_issuerTopicAuthorization", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_mapping(t_uint256,t_bool))", "contract": "TrustedIssuersRegistry", "src": "contracts\\erc3643\\TrustedIssuersRegistry.sol:37"}, {"label": "_topicTo<PERSON>ssuers", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_array(t_address)dyn_storage)", "contract": "TrustedIssuersRegistry", "src": "contracts\\erc3643\\TrustedIssuersRegistry.sol:40"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"label": "mapping(address => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_bool))": {"label": "mapping(address => mapping(uint256 => bool))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_array(t_address)dyn_storage)": {"label": "mapping(uint256 => address[])", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "b8f8a992c7f4ab0049aa2e9181665f8bafa9a89d4c012deb534d9634bd8b12a3": {"address": "0x0F1c3373A271120FA7411E5570b5E378112F45bd", "txHash": "0x6f9df2320ef3f1ed2b1482d3db06155ffe7994d6b151e376512650ead17fc759", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_claimTopics", "offset": 0, "slot": "0", "type": "t_array(t_uint256)dyn_storage", "contract": "ClaimTopicsRegistry", "src": "contracts\\erc3643\\ClaimTopicsRegistry.sol:28"}, {"label": "_isRequiredTopic", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_bool)", "contract": "ClaimTopicsRegistry", "src": "contracts\\erc3643\\ClaimTopicsRegistry.sol:31"}, {"label": "_topicIndex", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_uint256)", "contract": "ClaimTopicsRegistry", "src": "contracts\\erc3643\\ClaimTopicsRegistry.sol:34"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint256)": {"label": "mapping(uint256 => uint256)", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "9ec1cbd4dd10416874b35a5af47e0c9a7151e9de87940cc96388ffcd4441ce69": {"address": "******************************************", "txHash": "0x6518b9b82ebfb7941a283e2667b8068834477e0901382127fbc6dfbb8533bb0a", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_identityImplementation", "offset": 0, "slot": "0", "type": "t_address", "contract": "IdentityContractFactory", "src": "contracts\\erc3643\\IdentityContractFactory.sol:27"}, {"label": "_walletToIdentity", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_address)", "contract": "IdentityContractFactory", "src": "contracts\\erc3643\\IdentityContractFactory.sol:30"}, {"label": "_identityToWallet", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_address)", "contract": "IdentityContractFactory", "src": "contracts\\erc3643\\IdentityContractFactory.sol:33"}, {"label": "_allIdentities", "offset": 0, "slot": "3", "type": "t_array(t_address)dyn_storage", "contract": "IdentityContractFactory", "src": "contracts\\erc3643\\IdentityContractFactory.sol:36"}, {"label": "_identityCounter", "offset": 0, "slot": "4", "type": "t_uint256", "contract": "IdentityContractFactory", "src": "contracts\\erc3643\\IdentityContractFactory.sol:39"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_mapping(t_address,t_address)": {"label": "mapping(address => address)", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "0a7c947add8084baaa5b83221debd96fd86628b0152b5c67b140da8e1b43ba1d": {"address": "0x877211F1A038702Ea6077b935f0A31C348c913e4", "txHash": "0xbd42473f275ba1af5e3b6b9a97a29317e59d722958d881ceb6e0eeb4f6e07622", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_legacyCompliance", "offset": 0, "slot": "0", "type": "t_address", "contract": "ERC3643ComplianceWrapper", "src": "contracts\\erc3643\\ERC3643ComplianceWrapper.sol:42"}, {"label": "_boundToken", "offset": 0, "slot": "1", "type": "t_address", "contract": "ERC3643ComplianceWrapper", "src": "contracts\\erc3643\\ERC3643ComplianceWrapper.sol:45"}, {"label": "_boundTokens", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_bool)", "contract": "ERC3643ComplianceWrapper", "src": "contracts\\erc3643\\ERC3643ComplianceWrapper.sol:48"}, {"label": "_allBoundTokens", "offset": 0, "slot": "3", "type": "t_array(t_address)dyn_storage", "contract": "ERC3643ComplianceWrapper", "src": "contracts\\erc3643\\ERC3643ComplianceWrapper.sol:51"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "ddb8b8a86caeb4df3fee23dc6541aa221edc5c93a85f000e5173f2c417cc2cc3": {"address": "0x769D6F0065cCf6a8B37B734E4396E549Cb543238", "txHash": "0x323cb67065f4fe22bb99e93ca0c660e19d7a089e1a60d5015ac4bd99955a4b0f", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_legacyIdentityRegistry", "offset": 0, "slot": "0", "type": "t_address", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:46"}, {"label": "_identityStorage", "offset": 0, "slot": "1", "type": "t_address", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:49"}, {"label": "_trustedIssuersRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:52"}, {"label": "_claimTopicsRegistry", "offset": 0, "slot": "3", "type": "t_address", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:55"}, {"label": "_identityFactory", "offset": 0, "slot": "4", "type": "t_contract(IdentityContractFactory)11314", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:58"}, {"label": "_walletToIdentity", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_contract(IIdentity)12929)", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:61"}, {"label": "_identityToWallet", "offset": 0, "slot": "6", "type": "t_mapping(t_address,t_address)", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:64"}, {"label": "_verificationCache", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:67"}, {"label": "_verificationCacheTimestamp", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC3643IdentityRegistryWrapper", "src": "contracts\\erc3643\\ERC3643IdentityRegistryWrapper.sol:68"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IIdentity)12929": {"label": "contract IIdentity", "numberOfBytes": "20"}, "t_contract(IdentityContractFactory)11314": {"label": "contract IdentityContractFactory", "numberOfBytes": "20"}, "t_mapping(t_address,t_address)": {"label": "mapping(address => address)", "numberOfBytes": "32"}, "t_mapping(t_address,t_contract(IIdentity)12929)": {"label": "mapping(address => contract IIdentity)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "94c4b98e6ab33a50691a4becf8e7d2be29ff1cd52e585f339160738cafdc1f85": {"address": "0x1bfFf7fFFB303B2de423D3EA442E5F155e8BDFfB", "txHash": "0x08c6702b83222b7f0167c7940984758d52fc6f8873786c1eaab7523e722c64f2", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_underlyingToken", "offset": 0, "slot": "0", "type": "t_address", "contract": "ERC3643TokenWrapper", "src": "contracts\\erc3643\\ERC3643TokenWrapper.sol:34"}, {"label": "_erc3643IdentityRegistry", "offset": 0, "slot": "1", "type": "t_address", "contract": "ERC3643TokenWrapper", "src": "contracts\\erc3643\\ERC3643TokenWrapper.sol:37"}, {"label": "_erc3643Compliance", "offset": 0, "slot": "2", "type": "t_address", "contract": "ERC3643TokenWrapper", "src": "contracts\\erc3643\\ERC3643TokenWrapper.sol:40"}, {"label": "_onchainID", "offset": 0, "slot": "3", "type": "t_address", "contract": "ERC3643TokenWrapper", "src": "contracts\\erc3643\\ERC3643TokenWrapper.sol:43"}, {"label": "_version", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC3643TokenWrapper", "src": "contracts\\erc3643\\ERC3643TokenWrapper.sol:46"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "fc90b5f9ae0c83816e84f3fd1928f5a343eadf6601515d2fc9dcd660bde820b4": {"address": "0xc9d514Aa6D88d96Cbe9Ffb8fF6571bDC4c1AF5c3", "txHash": "0x352e7c084dc41f41394cd89952c62b9008d8c9709ecde26671c50a107eb798ca", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:42"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:43"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:46"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:47"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:48"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:49"}, {"label": "_modules", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_address)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:52"}, {"label": "_authorizedModules", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:53"}, {"label": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:56"}, {"label": "_directVerified", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:57"}, {"label": "_forcedTransferInProgress", "offset": 0, "slot": "10", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:60"}, {"label": "_moduleTransferInProgress", "offset": 1, "slot": "10", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:61"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "edf596980ea0d74829858dae95fcfbe00fd114acabfe3e7ed7b4f7204716a9e8": {"address": "0xc9d514Aa6D88d96Cbe9Ffb8fF6571bDC4c1AF5c3", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:38"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:41"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:44"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:45"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:46"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:47"}, {"label": "_identityRegistry", "offset": 0, "slot": "6", "type": "t_contract(IdentityRegistry)7436", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:50"}, {"label": "_compliance", "offset": 0, "slot": "7", "type": "t_contract(Compliance)5702", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:51"}, {"label": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_contract(ICompleteWhitelist)13464", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:54"}, {"label": "_forcedTransferInProgress", "offset": 20, "slot": "8", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:57"}, {"label": "_approvedTransferInProgress", "offset": 21, "slot": "8", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:60"}, {"label": "_agentList", "offset": 0, "slot": "9", "type": "t_array(t_address)dyn_storage", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:66"}, {"label": "_agentListIndex", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:68"}, {"label": "_conditionalTransfersEnabled", "offset": 0, "slot": "11", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:80"}, {"label": "_transferApprovals", "offset": 0, "slot": "12", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:81"}, {"label": "_transferNonces", "offset": 0, "slot": "13", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:82"}, {"label": "_transfer<PERSON><PERSON><PERSON>stEnabled", "offset": 0, "slot": "14", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:85"}, {"label": "_transfer<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "15", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:86"}, {"label": "_transferFeesEnabled", "offset": 0, "slot": "16", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:89"}, {"label": "_transferFeePercentage", "offset": 0, "slot": "17", "type": "t_uint256", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:90"}, {"label": "_feeCollector", "offset": 0, "slot": "18", "type": "t_address", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:91"}, {"label": "_agreementAcceptances", "offset": 0, "slot": "19", "type": "t_mapping(t_address,t_uint256)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:94"}, {"label": "_emergencyPaused", "offset": 0, "slot": "20", "type": "t_bool", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:97"}, {"label": "_functionPaused", "offset": 0, "slot": "21", "type": "t_mapping(t_bytes4,t_bool)", "contract": "SecurityToken", "src": "contracts\\SecurityToken.sol:98"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_bytes4": {"label": "bytes4", "numberOfBytes": "4"}, "t_contract(Compliance)5702": {"label": "contract Compliance", "numberOfBytes": "20"}, "t_contract(ICompleteWhitelist)13464": {"label": "contract ICompleteWhitelist", "numberOfBytes": "20"}, "t_contract(IdentityRegistry)7436": {"label": "contract IdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes4,t_bool)": {"label": "mapping(bytes4 => bool)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "778eab746f6ccb2027580219d5448ffd3691cad192c25d18a0734c4141679277": {"address": "0x2cB76BFB785B9af8Bb3C62A48550666873A28B7f", "txHash": "0xb1d04254f7741de8e8226d5b6ce55868a4441de2569c37e1c61c3a39de13b055", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:42"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:43"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:46"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:47"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:48"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:49"}, {"label": "_modules", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_address)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:52"}, {"label": "_authorizedModules", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:53"}, {"label": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:56"}, {"label": "_directVerified", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:57"}, {"label": "_forcedTransferInProgress", "offset": 0, "slot": "10", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:60"}, {"label": "_moduleTransferInProgress", "offset": 1, "slot": "10", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:61"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "8a7b36717548dafd2bf53efc6f608476eb46db715bf38a6bd3d2fb04ba100ea8": {"address": "0x5C4eb39C3fFaD4019Baf73F31DA55B09A8f224bD", "txHash": "0x2a00a0649db7e6ff9585f00b55a9d15d03c8be21c51734cfc5d4957577f2e656", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_maxSupply", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:42"}, {"label": "_decimals", "offset": 0, "slot": "1", "type": "t_uint8", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:43"}, {"label": "_tokenPrice", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:46"}, {"label": "_bonusTiers", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:47"}, {"label": "_tokenDetails", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:48"}, {"label": "_tokenImageUrl", "offset": 0, "slot": "5", "type": "t_string_storage", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:49"}, {"label": "_modules", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_address)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:52"}, {"label": "_authorizedModules", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:53"}, {"label": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:56"}, {"label": "_directVerified", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_bool)", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:57"}, {"label": "_forcedTransferInProgress", "offset": 0, "slot": "10", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:60"}, {"label": "_moduleTransferInProgress", "offset": 1, "slot": "10", "type": "t_bool", "contract": "SecurityTokenCore", "src": "contracts\\SecurityTokenCore.sol:61"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)294_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)145_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)522_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)586_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_bytes32,t_address)": {"label": "mapping(bytes32 => address)", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "e5e4a39ba4df7398edca2d501c94aad58b75b8ae9a7a6da0615f18dc66296f71": {"address": "0x74aC46eF6Ba20E55782219A6c79AeE3e62C99eA0", "txHash": "0xe37ffe73bd40a2f031af740a386b4b6b4565b918205846e437977bf5932371cd", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "0", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:38"}, {"label": "complianceModule", "offset": 0, "slot": "1", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:39"}, {"label": "tokenVersion", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:40"}, {"label": "maxSupply", "offset": 0, "slot": "3", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:41"}, {"label": "upgradesRenounced", "offset": 0, "slot": "4", "type": "t_bool", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:47"}, {"label": "upgradeTimelock", "offset": 0, "slot": "5", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:48"}, {"label": "pendingUpgradeTimestamp", "offset": 0, "slot": "6", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:49"}, {"label": "pendingImplementation", "offset": 0, "slot": "7", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:50"}, {"label": "frozen", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:56"}, {"label": "frozenBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "ef5b3882722d3bcf0ec4bcd0db2a56f5dfabe1c5e60c0a0f70322619997a7147": {"address": "0xad9C54d23b169eEF92a815771263B2027193FF12", "txHash": "0x7a11ffa226c5ece8c72493ea48a565eb572027b83e2dd8e0ef57a6aec4703325", "layout": {"solcVersion": "0.8.22", "storage": [{"label": "_version", "offset": 0, "slot": "0", "type": "t_string_storage", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:25"}, {"label": "_identityRegistry", "offset": 0, "slot": "1", "type": "t_address", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:26"}, {"label": "_complianceModule", "offset": 0, "slot": "2", "type": "t_address", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:27"}, {"label": "upgradesRenounced", "offset": 20, "slot": "2", "type": "t_bool", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:28"}, {"label": "_paused", "offset": 21, "slot": "2", "type": "t_bool", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:29"}, {"label": "_whitelist", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_bool)", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:32"}, {"label": "_identityVerified", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_bool)", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:33"}, {"label": "_frozenTokens", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_uint256)", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:34"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}