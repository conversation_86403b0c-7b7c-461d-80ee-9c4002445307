{"_format": "hh-sol-artifact-1", "contractName": "UpgradeableERC3643Token", "sourceName": "contracts/fresh/UpgradeableERC3643Token.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AddressFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AddressUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldCompliance", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newCompliance", "type": "address"}], "name": "ComplianceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "ForcedTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldRegistry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newRegistry", "type": "address"}], "name": "IdentityRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newTimelock", "type": "uint256"}], "name": "TimelockUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "TokensFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "TokensUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newImplementation", "type": "address"}], "name": "UpgradeExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newImplementation", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "executeAfter", "type": "uint256"}], "name": "UpgradeProposed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UpgradesRenounced", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COMPLIANCE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "availableBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "cancelUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "complianceModule", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "executeUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "freeze", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "freezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "frozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "frozenBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "frozenBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newTimelock", "type": "uint256"}], "name": "increaseTimelock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "admin_", "type": "address"}, {"internalType": "address", "name": "identityRegistry_", "type": "address"}, {"internalType": "address", "name": "compliance_", "type": "address"}, {"internalType": "string", "name": "version_", "type": "string"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isFrozen", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pendingImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pendingUpgradeTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "proposeUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceUpgrades", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newCompliance", "type": "address"}], "name": "setCompliance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newRegistry", "type": "address"}], "name": "setIdentityRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "unfreeze", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unfreezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "upgradeTimelock", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "upgradesRenounced", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}