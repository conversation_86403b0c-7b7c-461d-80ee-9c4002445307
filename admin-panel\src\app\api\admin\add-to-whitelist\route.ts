import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function addToWhitelist(address account) external",
  "function isWhitelisted(address account) external view returns (bool)",
  "function identityRegistry() external view returns (address)"
];

const IDENTITY_REGISTRY_ABI = [
  "function registerIdentity(address userAddress, address onchainId, uint16 country) external",
  "function addToWhitelist(address account) external",
  "function isVerified(address account) external view returns (bool)",
  "function isWhitelisted(address account) external view returns (bool)",
  "function createIdentityContract(address userAddress) external returns (address)"
];

const ERC3643_IDENTITY_FACTORY_ABI = [
  "function createIdentity(address wallet) external returns (address)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, userAddress, country = 'US' } = await request.json();

    if (!tokenAddress || !userAddress) {
      return NextResponse.json(
        { error: 'Token address and user address are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(userAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
    const identityRegistryAddress = process.env.AMOY_ERC3643_IDENTITY_REGISTRY_WRAPPER_ADDRESS;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    console.log('🔄 Starting ERC-3643 whitelist process...');
    console.log('Token:', tokenAddress);
    console.log('User:', userAddress);
    console.log('Identity Registry Wrapper:', identityRegistryAddress);

    const results = {
      identityCreated: false,
      identityRegistered: false,
      whitelisted: false,
      txHashes: [] as string[],
      errors: [] as string[]
    };

    // Get identity registry contract
    if (!identityRegistryAddress) {
      return NextResponse.json(
        { error: 'Identity registry not configured' },
        { status: 500 }
      );
    }

    const identityRegistry = new ethers.Contract(identityRegistryAddress, IDENTITY_REGISTRY_ABI, signer);

    // Get the underlying registry address
    const WRAPPER_ABI = [
      "function underlyingRegistry() external view returns (address)",
      "function isVerified(address account) external view returns (bool)",
      "function isWhitelisted(address account) external view returns (bool)"
    ];

    const wrapper = new ethers.Contract(identityRegistryAddress, WRAPPER_ABI, signer);
    let underlyingRegistryAddress: string;

    try {
      underlyingRegistryAddress = await wrapper.underlyingRegistry();
      console.log('🔗 Underlying Registry:', underlyingRegistryAddress);
    } catch (error) {
      return NextResponse.json(
        { error: 'Could not get underlying registry address' },
        { status: 500 }
      );
    }

    // Create contract for underlying registry
    const UNDERLYING_REGISTRY_ABI = [
      "function registerIdentity(address userAddress, uint16 country) external",
      "function addToWhitelist(address account) external",
      "function isVerified(address account) external view returns (bool)",
      "function isWhitelisted(address account) external view returns (bool)"
    ];

    const underlyingRegistry = new ethers.Contract(underlyingRegistryAddress, UNDERLYING_REGISTRY_ABI, signer);

    // Check current status using underlying registry (more reliable)
    let isVerified = false;
    let isWhitelisted = false;

    try {
      [isVerified, isWhitelisted] = await Promise.all([
        underlyingRegistry.isVerified(userAddress),
        underlyingRegistry.isWhitelisted(userAddress)
      ]);

      console.log('📊 Underlying registry status:', { isVerified, isWhitelisted });
    } catch (error) {
      console.warn('Could not check underlying registry status:', error.message);
    }

    if (isWhitelisted) {
      return NextResponse.json({
        success: true,
        message: 'User is already whitelisted',
        results: { ...results, whitelisted: true },
        userAddress,
        tokenAddress
      });
    }

    // Step 1: Register identity if not verified (using underlying registry)
    if (!isVerified) {
      try {
        console.log('📝 Registering identity with underlying registry...');
        const countryCode = getCountryCode(country);
        const tx1 = await underlyingRegistry.registerIdentity(userAddress, countryCode);
        await tx1.wait();
        results.identityRegistered = true;
        results.txHashes.push(tx1.hash);
        console.log('✅ Identity registered:', tx1.hash);
      } catch (error: any) {
        console.error('❌ Failed to register identity:', error);
        results.errors.push(`Identity registration failed: ${error.message}`);
      }
    } else {
      console.log('✅ Identity already registered');
      results.identityRegistered = true;
    }

    // Step 2: Add to whitelist (using underlying registry)
    try {
      console.log('📋 Adding to whitelist with underlying registry...');
      const tx2 = await underlyingRegistry.addToWhitelist(userAddress);
      await tx2.wait();
      results.whitelisted = true;
      results.txHashes.push(tx2.hash);
      console.log('✅ Added to whitelist:', tx2.hash);
    } catch (error: any) {
      console.error('❌ Failed to add to whitelist:', error);
      results.errors.push(`Whitelist addition failed: ${error.message}`);
    }

    // Final verification using underlying registry
    try {
      const finalWhitelisted = await underlyingRegistry.isWhitelisted(userAddress);
      results.whitelisted = finalWhitelisted;
      console.log('🔍 Final whitelist status:', finalWhitelisted);
    } catch (error) {
      console.warn('Could not verify final whitelist status:', error.message);
    }

    const success = results.whitelisted && results.errors.length === 0;
    const message = success
      ? 'User successfully added to whitelist with ERC-3643 compliance'
      : `Whitelist process completed with ${results.errors.length} errors`;

    return NextResponse.json({
      success,
      message,
      results,
      userAddress,
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error in whitelist process:', error);
    return NextResponse.json(
      { error: `Failed to process whitelist request: ${error.message}` },
      { status: 500 }
    );
  }
}

// Helper function to convert country names to ISO codes
function getCountryCode(country: string): number {
  const countryCodes: { [key: string]: number } = {
    'US': 840, 'USA': 840, 'United States': 840,
    'CA': 124, 'Canada': 124,
    'GB': 826, 'UK': 826, 'United Kingdom': 826,
    'DE': 276, 'Germany': 276,
    'FR': 250, 'France': 250,
    'JP': 392, 'Japan': 392,
    'AU': 36, 'Australia': 36,
    'SG': 702, 'Singapore': 702,
    'CH': 756, 'Switzerland': 756,
    'NL': 528, 'Netherlands': 528,
    'SE': 752, 'Sweden': 752,
    'NO': 578, 'Norway': 578,
    'DK': 208, 'Denmark': 208,
    'FI': 246, 'Finland': 246,
    'IT': 380, 'Italy': 380,
    'ES': 724, 'Spain': 724,
    'BE': 56, 'Belgium': 56,
    'AT': 40, 'Austria': 40,
    'LU': 442, 'Luxembourg': 442,
    'IE': 372, 'Ireland': 372,
    'PT': 620, 'Portugal': 620,
    'GR': 300, 'Greece': 300,
    'CY': 196, 'Cyprus': 196,
    'MT': 470, 'Malta': 470,
    'SI': 705, 'Slovenia': 705,
    'SK': 703, 'Slovakia': 703,
    'CZ': 203, 'Czech Republic': 203,
    'PL': 616, 'Poland': 616,
    'HU': 348, 'Hungary': 348,
    'EE': 233, 'Estonia': 233,
    'LV': 428, 'Latvia': 428,
    'LT': 440, 'Lithuania': 440,
    'HR': 191, 'Croatia': 191,
    'BG': 100, 'Bulgaria': 100,
    'RO': 642, 'Romania': 642
  };

  return countryCodes[country.toUpperCase()] || 840; // Default to USA
}
