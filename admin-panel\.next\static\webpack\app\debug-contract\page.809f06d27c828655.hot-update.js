"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/debug-contract/page",{

/***/ "(app-pages-browser)/./src/app/debug-contract/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/debug-contract/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugContractPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Common ABIs to test\nconst COMMON_FUNCTIONS = [\n    {\n        name: 'name',\n        abi: [\n            'function name() view returns (string)'\n        ]\n    },\n    {\n        name: 'symbol',\n        abi: [\n            'function symbol() view returns (string)'\n        ]\n    },\n    {\n        name: 'decimals',\n        abi: [\n            'function decimals() view returns (uint8)'\n        ]\n    },\n    {\n        name: 'totalSupply',\n        abi: [\n            'function totalSupply() view returns (uint256)'\n        ]\n    },\n    {\n        name: 'paused',\n        abi: [\n            'function paused() view returns (bool)'\n        ]\n    },\n    {\n        name: 'version',\n        abi: [\n            'function version() view returns (string)'\n        ]\n    },\n    {\n        name: 'maxSupply',\n        abi: [\n            'function maxSupply() view returns (uint256)'\n        ]\n    },\n    {\n        name: 'owner',\n        abi: [\n            'function owner() view returns (address)'\n        ]\n    },\n    {\n        name: 'admin',\n        abi: [\n            'function admin() view returns (address)'\n        ]\n    },\n    {\n        name: 'implementation',\n        abi: [\n            'function implementation() view returns (address)'\n        ]\n    },\n    {\n        name: 'proxiableUUID',\n        abi: [\n            'function proxiableUUID() view returns (bytes32)'\n        ]\n    },\n    {\n        name: 'isWhitelisted',\n        abi: [\n            'function isWhitelisted(address) view returns (bool)'\n        ]\n    },\n    {\n        name: 'addToWhitelist',\n        abi: [\n            'function addToWhitelist(address) external'\n        ]\n    },\n    {\n        name: 'removeFromWhitelist',\n        abi: [\n            'function removeFromWhitelist(address) external'\n        ]\n    }\n];\nfunction DebugContractPage() {\n    _s();\n    const [contractAddress, setContractAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0x03112fB317Ac2FFaDF158581896dbB2dC3B9865c');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contractType, setContractType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const analyzeContractType = (testResults)=>{\n        const successfulFunctions = testResults.filter((r)=>r.success).map((r)=>r.function);\n        let type = 'Unknown Contract';\n        if (successfulFunctions.includes('name') && successfulFunctions.includes('symbol') && successfulFunctions.includes('totalSupply')) {\n            if (successfulFunctions.includes('isWhitelisted') && successfulFunctions.includes('paused')) {\n                type = '🛡️ ERC-3643 Security Token (Full Compliance)';\n            } else if (successfulFunctions.includes('paused')) {\n                type = '⏸️ Pausable ERC-20 Token';\n            } else if (successfulFunctions.includes('isWhitelisted')) {\n                type = '📋 Whitelisted ERC-20 Token';\n            } else {\n                type = '🪙 Standard ERC-20 Token';\n            }\n        } else if (successfulFunctions.includes('implementation') || successfulFunctions.includes('proxiableUUID')) {\n            type = '🔄 Proxy Contract';\n        } else if (successfulFunctions.includes('owner') || successfulFunctions.includes('admin')) {\n            type = '👑 Ownable Contract';\n        }\n        setContractType(type);\n    };\n    const testContract = async ()=>{\n        if (!contractAddress) return;\n        setLoading(true);\n        setResults([]);\n        const testResults = [];\n        for (const func of COMMON_FUNCTIONS){\n            try {\n                const result = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_2__.safeContractCall)(contractAddress, func.abi, func.name);\n                testResults.push({\n                    function: func.name,\n                    success: true,\n                    result: (result === null || result === void 0 ? void 0 : result.toString()) || 'null',\n                    error: null\n                });\n            } catch (error) {\n                testResults.push({\n                    function: func.name,\n                    success: false,\n                    result: null,\n                    error: error.message\n                });\n            }\n        }\n        setResults(testResults);\n        setLoading(false);\n        // Analyze contract type based on available functions\n        analyzeContractType(testResults);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-8\",\n                    children: \"\\uD83D\\uDD0D Contract Debugger\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Test Contract Functions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Contract Address\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: contractAddress,\n                                    onChange: (e)=>setContractAddress(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    placeholder: \"0x...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testContract,\n                            disabled: loading || !contractAddress,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50\",\n                            children: loading ? 'Testing...' : 'Test Contract'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Test Results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                contractType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\",\n                                    children: contractType\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-lg border \".concat(result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-3 h-3 rounded-full mr-3 \".concat(result.success ? 'bg-green-500' : 'bg-red-500')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                result.function,\n                                                                \"()\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm \".concat(result.success ? 'text-green-600' : 'text-red-600'),\n                                                    children: result.success ? '✅ Success' : '❌ Failed'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this),\n                                        result.success && result.result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Result:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" \",\n                                                result.result\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 21\n                                        }, this),\n                                        !result.success && result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Error:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \" \",\n                                                result.error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-blue-800 mb-2\",\n                                    children: \"Summary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"✅ Successful calls: \",\n                                                results.filter((r)=>r.success).length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"❌ Failed calls: \",\n                                                results.filter((r)=>!r.success).length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"\\uD83D\\uDCCA Total functions tested: \",\n                                                results.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-yellow-800 mb-2\",\n                            children: \"ℹ️ About This Tool\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-700\",\n                            children: \"This tool tests common ERC-20 and ERC-3643 functions on a contract to determine its capabilities. Use this to debug contract compatibility issues and understand what functions are available.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\debug-contract\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugContractPage, \"/oJdgtYUBjZicyP94VoiHuQmKY0=\");\n_c = DebugContractPage;\nvar _c;\n$RefreshReg$(_c, \"DebugContractPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/debug-contract/page.tsx\n"));

/***/ })

});