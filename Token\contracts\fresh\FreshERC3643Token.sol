// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";

interface IWorkingIdentityRegistry {
    function isVerified(address userAddress) external view returns (bool);
}

/**
 * @title FreshERC3643Token
 * @dev A clean, working implementation of ERC-3643 compliant security token
 * This token uses the working Identity Registry directly and provides all
 * institutional custody features required for security tokens
 */
contract FreshERC3643Token is 
    Initializable,
    ERC20Upgradeable,
    UUPSUpgradeable,
    AccessControlUpgradeable,
    PausableUpgradeable
{
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant COMPLIANCE_ROLE = keccak256("COMPLIANCE_ROLE");
    
    IWorkingIdentityRegistry public identityRegistry;
    uint8 private _decimals;
    uint256 public maxSupply;
    
    // Frozen balances for partial freezing functionality
    mapping(address => uint256) private _frozenBalances;
    
    event Mint(address indexed to, uint256 amount);
    event Burn(address indexed from, uint256 amount);
    event ForcedTransfer(address indexed from, address indexed to, uint256 amount);
    event IdentityRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event TokensFrozen(address indexed account, uint256 amount);
    event TokensUnfrozen(address indexed account, uint256 amount);
    
    modifier onlyVerified(address user) {
        require(isVerified(user), "FreshERC3643: user not verified");
        _;
    }
    
    modifier notExceedsMaxSupply(uint256 amount) {
        require(totalSupply() + amount <= maxSupply, "FreshERC3643: exceeds max supply");
        _;
    }
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    /**
     * @dev Initialize the token
     * @param name_ Token name
     * @param symbol_ Token symbol
     * @param decimals_ Token decimals
     * @param maxSupply_ Maximum token supply
     * @param _identityRegistry Address of the working Identity Registry
     * @param _admin Address of the admin
     */
    function initialize(
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        uint256 maxSupply_,
        address _identityRegistry,
        address _admin
    ) public initializer {
        require(_identityRegistry != address(0), "FreshERC3643: invalid registry address");
        require(_admin != address(0), "FreshERC3643: invalid admin address");
        require(maxSupply_ > 0, "FreshERC3643: invalid max supply");
        
        __ERC20_init(name_, symbol_);
        __AccessControl_init();
        __UUPSUpgradeable_init();
        __Pausable_init();
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        identityRegistry = IWorkingIdentityRegistry(_identityRegistry);
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(AGENT_ROLE, _admin);
        _grantRole(COMPLIANCE_ROLE, _admin);
    }
    
    /**
     * @dev Returns the number of decimals used to get its user representation
     */
    function decimals() public view override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev Check if a user is verified through the Identity Registry
     * @param user Address to check
     * @return bool True if the user is verified
     */
    function isVerified(address user) public view returns (bool) {
        return identityRegistry.isVerified(user);
    }
    
    /**
     * @dev Get available balance (total balance minus frozen balance)
     * @param account Address to check
     * @return uint256 Available balance
     */
    function availableBalanceOf(address account) public view returns (uint256) {
        uint256 totalBalance = balanceOf(account);
        uint256 frozenBalance = _frozenBalances[account];
        return totalBalance > frozenBalance ? totalBalance - frozenBalance : 0;
    }
    
    /**
     * @dev Get frozen balance
     * @param account Address to check
     * @return uint256 Frozen balance
     */
    function frozenBalanceOf(address account) public view returns (uint256) {
        return _frozenBalances[account];
    }
    
    /**
     * @dev Mint tokens to a verified address
     * @param to Address to mint to
     * @param amount Amount to mint
     */
    function mint(address to, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
        onlyVerified(to) 
        notExceedsMaxSupply(amount)
        whenNotPaused
    {
        _mint(to, amount);
        emit Mint(to, amount);
    }
    
    /**
     * @dev Burn tokens from an address
     * @param from Address to burn from
     * @param amount Amount to burn
     */
    function burn(address from, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
        whenNotPaused
    {
        require(availableBalanceOf(from) >= amount, "FreshERC3643: insufficient available balance");
        _burn(from, amount);
        emit Burn(from, amount);
    }
    
    /**
     * @dev Force transfer tokens (custody operation)
     * @param from Address to transfer from
     * @param to Address to transfer to
     * @param amount Amount to transfer
     * @return bool True if successful
     */
    function forcedTransfer(address from, address to, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
        onlyVerified(to)
        whenNotPaused
        returns (bool) 
    {
        require(availableBalanceOf(from) >= amount, "FreshERC3643: insufficient available balance");
        _transfer(from, to, amount);
        emit ForcedTransfer(from, to, amount);
        return true;
    }
    
    /**
     * @dev Freeze tokens in an account
     * @param account Address to freeze tokens for
     * @param amount Amount to freeze
     */
    function freezeTokens(address account, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
    {
        require(balanceOf(account) >= _frozenBalances[account] + amount, "FreshERC3643: insufficient balance to freeze");
        
        _frozenBalances[account] += amount;
        emit TokensFrozen(account, amount);
    }
    
    /**
     * @dev Unfreeze tokens in an account
     * @param account Address to unfreeze tokens for
     * @param amount Amount to unfreeze
     */
    function unfreezeTokens(address account, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
    {
        require(_frozenBalances[account] >= amount, "FreshERC3643: insufficient frozen balance");
        
        _frozenBalances[account] -= amount;
        emit TokensUnfrozen(account, amount);
    }
    
    /**
     * @dev Transfer tokens between verified addresses
     * @param to Address to transfer to
     * @param amount Amount to transfer
     * @return bool True if successful
     */
    function transfer(address to, uint256 amount) 
        public 
        override 
        whenNotPaused 
        onlyVerified(msg.sender) 
        onlyVerified(to) 
        returns (bool) 
    {
        require(availableBalanceOf(msg.sender) >= amount, "FreshERC3643: insufficient available balance");
        return super.transfer(to, amount);
    }
    
    /**
     * @dev Transfer tokens from one verified address to another
     * @param from Address to transfer from
     * @param to Address to transfer to
     * @param amount Amount to transfer
     * @return bool True if successful
     */
    function transferFrom(address from, address to, uint256 amount) 
        public 
        override 
        whenNotPaused 
        onlyVerified(from) 
        onlyVerified(to) 
        returns (bool) 
    {
        require(availableBalanceOf(from) >= amount, "FreshERC3643: insufficient available balance");
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev Pause all token operations
     */
    function pause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _pause();
    }
    
    /**
     * @dev Unpause all token operations
     */
    function unpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _unpause();
    }
    
    /**
     * @dev Update the Identity Registry
     * @param newRegistry Address of the new Identity Registry
     */
    function setIdentityRegistry(address newRegistry) 
        external 
        onlyRole(DEFAULT_ADMIN_ROLE) 
    {
        require(newRegistry != address(0), "FreshERC3643: invalid registry address");
        
        address oldRegistry = address(identityRegistry);
        identityRegistry = IWorkingIdentityRegistry(newRegistry);
        
        emit IdentityRegistryUpdated(oldRegistry, newRegistry);
    }
    
    /**
     * @dev Batch mint tokens to multiple verified addresses
     * @param recipients Array of recipient addresses
     * @param amounts Array of amounts to mint
     */
    function batchMint(address[] calldata recipients, uint256[] calldata amounts) 
        external 
        onlyRole(AGENT_ROLE) 
        whenNotPaused
    {
        require(recipients.length == amounts.length, "FreshERC3643: array length mismatch");
        
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            totalAmount += amounts[i];
        }
        require(totalSupply() + totalAmount <= maxSupply, "FreshERC3643: exceeds max supply");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            require(isVerified(recipients[i]), "FreshERC3643: recipient not verified");
            _mint(recipients[i], amounts[i]);
            emit Mint(recipients[i], amounts[i]);
        }
    }
    
    /**
     * @dev Add an agent who can perform token operations
     * @param agent Address of the new agent
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "FreshERC3643: invalid agent address");
        _grantRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Remove an agent
     * @param agent Address of the agent to remove
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Check if an address is an agent
     * @param agent Address to check
     * @return bool True if the address is an agent
     */
    function isAgent(address agent) external view returns (bool) {
        return hasRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Authorize upgrade (required by UUPSUpgradeable)
     * @param newImplementation Address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation) 
        internal 
        override 
        onlyRole(DEFAULT_ADMIN_ROLE) 
    {
        // Additional upgrade authorization logic can be added here
    }
    
    // ERC-3643 compatibility functions
    
    /**
     * @dev Get the compliance module address (simplified implementation)
     * @return address Zero address (no separate compliance module)
     */
    function compliance() external pure returns (address) {
        return address(0); // Simplified - compliance is built into the token
    }
    
    /**
     * @dev Get the version of this contract
     * @return string Version string
     */
    function version() external pure returns (string memory) {
        return "1.0.0";
    }
    
    /**
     * @dev Get token information
     * @return name_ Token name
     * @return symbol_ Token symbol
     * @return decimals_ Token decimals
     * @return totalSupply_ Total supply
     * @return maxSupply_ Maximum supply
     */
    function getTokenInfo() external view returns (
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        uint256 totalSupply_,
        uint256 maxSupply_
    ) {
        return (name(), symbol(), decimals(), totalSupply(), maxSupply);
    }
}
