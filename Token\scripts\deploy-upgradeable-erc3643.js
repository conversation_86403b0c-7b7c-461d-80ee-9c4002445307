const { ethers, upgrades } = require('hardhat');

// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL ✅)
const WORKING_ERC3643_CONTRACTS = {
  workingUnderlyingRegistry: "******************************************",
  workingWrapper: "******************************************",
  workingTokenExample: "******************************************"
};

async function deployUpgradeableERC3643() {
  console.log('🚀 DEPLOYING UPGRADEABLE ERC-3643 TOKEN');
  console.log('='.repeat(80));

  try {
    const [deployer] = await ethers.getSigners();
    console.log('👤 Deployer:', deployer.address);
    console.log('💰 Balance:', ethers.formatEther(await deployer.provider.getBalance(deployer.address)), 'MATIC');
    console.log('');

    // Token configuration
    const tokenConfig = {
      name: process.env.TOKEN_NAME || "Upgradeable Security Token",
      symbol: process.env.TOKEN_SYMBOL || "UPGSEC",
      decimals: parseInt(process.env.TOKEN_DECIMALS || "18"),
      maxSupply: ethers.parseUnits(process.env.TOKEN_MAX_SUPPLY || "1000000", 18),
      admin: process.env.ADMIN_ADDRESS || deployer.address,
      identityRegistry: WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry,
      compliance: ethers.ZeroAddress, // Simplified compliance for now
      version: "1.0.0"
    };

    console.log('📋 TOKEN CONFIGURATION:');
    console.log(`   Name: ${tokenConfig.name}`);
    console.log(`   Symbol: ${tokenConfig.symbol}`);
    console.log(`   Decimals: ${tokenConfig.decimals}`);
    console.log(`   Max Supply: ${ethers.formatUnits(tokenConfig.maxSupply, tokenConfig.decimals)}`);
    console.log(`   Admin: ${tokenConfig.admin}`);
    console.log(`   Identity Registry: ${tokenConfig.identityRegistry}`);
    console.log(`   Compliance: ${tokenConfig.compliance}`);
    console.log(`   Version: ${tokenConfig.version}`);
    console.log('');

    // Deploy the upgradeable contract
    console.log('🔄 DEPLOYING UPGRADEABLE CONTRACT...');
    console.log('-'.repeat(60));

    const UpgradeableERC3643Token = await ethers.getContractFactory('UpgradeableERC3643Token');
    
    console.log('📦 Deploying proxy and implementation...');
    const token = await upgrades.deployProxy(
      UpgradeableERC3643Token,
      [
        tokenConfig.name,
        tokenConfig.symbol,
        tokenConfig.decimals,
        tokenConfig.maxSupply,
        tokenConfig.admin,
        tokenConfig.identityRegistry,
        tokenConfig.compliance,
        tokenConfig.version
      ],
      {
        initializer: 'initialize',
        kind: 'uups'
      }
    );

    await token.waitForDeployment();
    const tokenAddress = await token.getAddress();

    console.log('✅ Proxy deployed to:', tokenAddress);

    // Get implementation address
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(tokenAddress);
    console.log('✅ Implementation deployed to:', implementationAddress);

    // Get admin address (for UUPS, this should be zero as admin is in the implementation)
    try {
      const adminAddress = await upgrades.erc1967.getAdminAddress(tokenAddress);
      console.log('✅ Proxy admin:', adminAddress);
    } catch (e) {
      console.log('ℹ️ No proxy admin (UUPS pattern)');
    }

    console.log('');
    console.log('🔍 VERIFYING DEPLOYMENT...');
    console.log('-'.repeat(60));

    // Verify basic functionality
    const name = await token.name();
    const symbol = await token.symbol();
    const decimals = await token.decimals();
    const maxSupply = await token.maxSupply();
    const identityRegistry = await token.identityRegistry();
    const version = await token.version();
    const upgradesRenounced = await token.upgradesRenounced();
    const upgradeTimelock = await token.upgradeTimelock();

    console.log('✅ Token verification:');
    console.log(`   Name: ${name}`);
    console.log(`   Symbol: ${symbol}`);
    console.log(`   Decimals: ${decimals}`);
    console.log(`   Max Supply: ${ethers.formatUnits(maxSupply, decimals)}`);
    console.log(`   Identity Registry: ${identityRegistry}`);
    console.log(`   Version: ${version}`);
    console.log(`   Upgrades Renounced: ${upgradesRenounced}`);
    console.log(`   Upgrade Timelock: ${Number(upgradeTimelock) / 86400} days`);

    // Check admin verification
    console.log('');
    console.log('🔍 CHECKING ADMIN VERIFICATION...');
    try {
      const isAdminVerified = await token.isVerified(tokenConfig.admin);
      console.log(`✅ Admin verified in identity registry: ${isAdminVerified}`);
    } catch (e) {
      console.log('⚠️ Could not check admin verification:', e.message);
    }

    // Check roles
    console.log('');
    console.log('🔍 CHECKING ROLE ASSIGNMENTS...');
    const DEFAULT_ADMIN_ROLE = await token.DEFAULT_ADMIN_ROLE();
    const AGENT_ROLE = await token.AGENT_ROLE();
    const COMPLIANCE_ROLE = await token.COMPLIANCE_ROLE();
    const UPGRADER_ROLE = await token.UPGRADER_ROLE();

    const hasDefaultAdmin = await token.hasRole(DEFAULT_ADMIN_ROLE, tokenConfig.admin);
    const hasAgentRole = await token.hasRole(AGENT_ROLE, tokenConfig.admin);
    const hasComplianceRole = await token.hasRole(COMPLIANCE_ROLE, tokenConfig.admin);
    const hasUpgraderRole = await token.hasRole(UPGRADER_ROLE, tokenConfig.admin);

    console.log(`✅ Admin has DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ Admin has AGENT_ROLE: ${hasAgentRole}`);
    console.log(`✅ Admin has COMPLIANCE_ROLE: ${hasComplianceRole}`);
    console.log(`✅ Admin has UPGRADER_ROLE: ${hasUpgraderRole}`);

    console.log('');
    console.log('🎉 DEPLOYMENT SUCCESSFUL!');
    console.log('='.repeat(80));

    const deploymentInfo = {
      success: true,
      network: (await deployer.provider.getNetwork()).name,
      chainId: (await deployer.provider.getNetwork()).chainId.toString(),
      deployer: deployer.address,
      contracts: {
        proxy: tokenAddress,
        implementation: implementationAddress,
        identityRegistry: tokenConfig.identityRegistry,
        compliance: tokenConfig.compliance
      },
      tokenInfo: {
        name: tokenConfig.name,
        symbol: tokenConfig.symbol,
        decimals: tokenConfig.decimals,
        maxSupply: ethers.formatUnits(tokenConfig.maxSupply, tokenConfig.decimals),
        admin: tokenConfig.admin,
        version: tokenConfig.version
      },
      upgradeInfo: {
        upgradesRenounced: upgradesRenounced,
        upgradeTimelock: `${Number(upgradeTimelock) / 86400} days`,
        pendingUpgrade: false
      },
      features: [
        '✅ ERC-3643 Compliant',
        '✅ Upgradeable (UUPS Pattern)',
        '✅ Optional Upgrade Renunciation',
        '✅ Timelock Protection (30 days)',
        '✅ Force Transfer (Custody)',
        '✅ Account Freezing',
        '✅ Partial Token Freezing',
        '✅ Role-Based Access Control',
        '✅ Pausable',
        '✅ Identity Registry Integration',
        '✅ Institutional Grade Security'
      ],
      explorerUrls: {
        proxy: `https://amoy.polygonscan.com/address/${tokenAddress}`,
        implementation: `https://amoy.polygonscan.com/address/${implementationAddress}`,
        identityRegistry: `https://amoy.polygonscan.com/address/${tokenConfig.identityRegistry}`
      },
      nextSteps: [
        '1. Verify contracts on block explorer',
        '2. Test basic functionality (mint, transfer, freeze)',
        '3. Test upgrade functionality',
        '4. Set up governance for upgrade decisions',
        '5. Consider renouncing upgrades when ready for production'
      ]
    };

    console.log('📊 DEPLOYMENT SUMMARY:');
    console.log(`   Proxy Address: ${tokenAddress}`);
    console.log(`   Implementation: ${implementationAddress}`);
    console.log(`   Identity Registry: ${tokenConfig.identityRegistry}`);
    console.log(`   Admin: ${tokenConfig.admin}`);
    console.log(`   Upgradeable: Yes (can be renounced)`);
    console.log(`   Timelock: ${Number(upgradeTimelock) / 86400} days`);

    console.log('');
    console.log('🔗 EXPLORER LINKS:');
    console.log(`   Proxy: https://amoy.polygonscan.com/address/${tokenAddress}`);
    console.log(`   Implementation: https://amoy.polygonscan.com/address/${implementationAddress}`);

    console.log('');
    console.log('📋 NEXT STEPS:');
    deploymentInfo.nextSteps.forEach((step, index) => {
      console.log(`   ${step}`);
    });

    // Save deployment info to file
    const fs = require('fs');
    const deploymentFile = `deployment-${tokenConfig.symbol}-${Date.now()}.json`;
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    console.log('');
    console.log(`💾 Deployment info saved to: ${deploymentFile}`);

    return deploymentInfo;

  } catch (error) {
    console.error('❌ Deployment failed:', error);
    throw error;
  }
}

// Run deployment if called directly
if (require.main === module) {
  deployUpgradeableERC3643()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { deployUpgradeableERC3643 };
