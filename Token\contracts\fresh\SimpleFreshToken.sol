// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

interface IWorkingIdentityRegistry {
    function isVerified(address userAddress) external view returns (bool);
}

/**
 * @title SimpleFreshToken
 * @dev A simple, non-upgradeable ERC-3643 compliant security token
 */
contract SimpleFreshToken is ERC20, AccessControl, Pausable {
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    IWorkingIdentityRegistry public identityRegistry;
    uint8 private _decimals;
    uint256 public maxSupply;
    
    // Frozen balances for partial freezing functionality
    mapping(address => uint256) private _frozenBalances;
    
    event Mint(address indexed to, uint256 amount);
    event Burn(address indexed from, uint256 amount);
    event ForcedTransfer(address indexed from, address indexed to, uint256 amount);
    event TokensFrozen(address indexed account, uint256 amount);
    event TokensUnfrozen(address indexed account, uint256 amount);
    
    modifier onlyVerified(address user) {
        require(isVerified(user), "SimpleFreshToken: user not verified");
        _;
    }
    
    modifier notExceedsMaxSupply(uint256 amount) {
        require(totalSupply() + amount <= maxSupply, "SimpleFreshToken: exceeds max supply");
        _;
    }
    
    constructor(
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        uint256 maxSupply_,
        address _identityRegistry,
        address _admin
    ) ERC20(name_, symbol_) {
        require(_identityRegistry != address(0), "SimpleFreshToken: invalid registry address");
        require(_admin != address(0), "SimpleFreshToken: invalid admin address");
        require(maxSupply_ > 0, "SimpleFreshToken: invalid max supply");
        
        _decimals = decimals_;
        maxSupply = maxSupply_;
        identityRegistry = IWorkingIdentityRegistry(_identityRegistry);
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(AGENT_ROLE, _admin);
    }
    
    /**
     * @dev Returns the number of decimals
     */
    function decimals() public view override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev Check if a user is verified
     */
    function isVerified(address user) public view returns (bool) {
        return identityRegistry.isVerified(user);
    }
    
    /**
     * @dev Get available balance (total balance minus frozen balance)
     */
    function availableBalanceOf(address account) public view returns (uint256) {
        uint256 totalBalance = balanceOf(account);
        uint256 frozenBalance = _frozenBalances[account];
        return totalBalance > frozenBalance ? totalBalance - frozenBalance : 0;
    }
    
    /**
     * @dev Get frozen balance
     */
    function frozenBalanceOf(address account) public view returns (uint256) {
        return _frozenBalances[account];
    }
    
    /**
     * @dev Mint tokens to a verified address
     */
    function mint(address to, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
        onlyVerified(to) 
        notExceedsMaxSupply(amount)
        whenNotPaused
    {
        _mint(to, amount);
        emit Mint(to, amount);
    }
    
    /**
     * @dev Burn tokens from an address
     */
    function burn(address from, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
        whenNotPaused
    {
        require(availableBalanceOf(from) >= amount, "SimpleFreshToken: insufficient available balance");
        _burn(from, amount);
        emit Burn(from, amount);
    }
    
    /**
     * @dev Force transfer tokens (custody operation)
     */
    function forcedTransfer(address from, address to, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
        onlyVerified(to)
        whenNotPaused
        returns (bool) 
    {
        require(availableBalanceOf(from) >= amount, "SimpleFreshToken: insufficient available balance");
        _transfer(from, to, amount);
        emit ForcedTransfer(from, to, amount);
        return true;
    }
    
    /**
     * @dev Freeze tokens in an account
     */
    function freezeTokens(address account, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
    {
        require(balanceOf(account) >= _frozenBalances[account] + amount, "SimpleFreshToken: insufficient balance to freeze");
        
        _frozenBalances[account] += amount;
        emit TokensFrozen(account, amount);
    }
    
    /**
     * @dev Unfreeze tokens in an account
     */
    function unfreezeTokens(address account, uint256 amount) 
        external 
        onlyRole(AGENT_ROLE) 
    {
        require(_frozenBalances[account] >= amount, "SimpleFreshToken: insufficient frozen balance");
        
        _frozenBalances[account] -= amount;
        emit TokensUnfrozen(account, amount);
    }
    
    /**
     * @dev Transfer tokens between verified addresses
     */
    function transfer(address to, uint256 amount) 
        public 
        override 
        whenNotPaused 
        onlyVerified(msg.sender) 
        onlyVerified(to) 
        returns (bool) 
    {
        require(availableBalanceOf(msg.sender) >= amount, "SimpleFreshToken: insufficient available balance");
        return super.transfer(to, amount);
    }
    
    /**
     * @dev Transfer tokens from one verified address to another
     */
    function transferFrom(address from, address to, uint256 amount) 
        public 
        override 
        whenNotPaused 
        onlyVerified(from) 
        onlyVerified(to) 
        returns (bool) 
    {
        require(availableBalanceOf(from) >= amount, "SimpleFreshToken: insufficient available balance");
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev Pause all token operations
     */
    function pause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _pause();
    }
    
    /**
     * @dev Unpause all token operations
     */
    function unpause() external onlyRole(DEFAULT_ADMIN_ROLE) {
        _unpause();
    }
    
    /**
     * @dev Add an agent
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "SimpleFreshToken: invalid agent address");
        _grantRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Remove an agent
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Check if an address is an agent
     */
    function isAgent(address agent) external view returns (bool) {
        return hasRole(AGENT_ROLE, agent);
    }
    
    // ERC-3643 compatibility functions
    
    /**
     * @dev Get the compliance module address (simplified implementation)
     */
    function compliance() external pure returns (address) {
        return address(0); // Simplified - compliance is built into the token
    }
    
    /**
     * @dev Get the version of this contract
     */
    function version() external pure returns (string memory) {
        return "1.0.0";
    }
}
