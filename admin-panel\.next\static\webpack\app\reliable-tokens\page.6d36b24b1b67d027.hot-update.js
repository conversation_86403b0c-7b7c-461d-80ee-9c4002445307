"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/utils/fallbackProvider.ts":
/*!***************************************!*\
  !*** ./src/utils/fallbackProvider.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FallbackProvider: () => (/* binding */ FallbackProvider),\n/* harmony export */   safeContractCall: () => (/* binding */ safeContractCall),\n/* harmony export */   safeContractTransaction: () => (/* binding */ safeContractTransaction),\n/* harmony export */   safeOptionalContractCall: () => (/* binding */ safeOptionalContractCall)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n/**\n * Fallback-first provider that bypasses browser provider issues\n * Uses reliable RPC endpoints as primary, browser provider as secondary\n */ class FallbackProvider {\n    static async initialize() {\n        if (this.initialized) return;\n        // Initialize reliable RPC providers\n        const rpcUrls = [\n            'https://rpc-amoy.polygon.technology/',\n            'https://polygon-amoy.drpc.org',\n            'https://polygon-amoy-bor-rpc.publicnode.com'\n        ];\n        for (const url of rpcUrls){\n            try {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_0__.JsonRpcProvider(url);\n                await provider.getBlockNumber(); // Test connectivity\n                this.reliableProviders.push(provider);\n                console.log(\"✅ Reliable provider added: \".concat(url));\n            } catch (error) {\n                console.warn(\"❌ RPC failed: \".concat(url));\n            }\n        }\n        // Initialize browser provider if available\n        if ( true && window.ethereum) {\n            try {\n                this.browserProvider = new ethers__WEBPACK_IMPORTED_MODULE_1__.BrowserProvider(window.ethereum);\n                console.log('✅ Browser provider initialized');\n            } catch (error) {\n                console.warn('❌ Browser provider failed to initialize');\n            }\n        }\n        this.initialized = true;\n        console.log(\"\\uD83D\\uDD27 FallbackProvider initialized with \".concat(this.reliableProviders.length, \" reliable providers\"));\n    }\n    /**\n   * Get a provider for read-only operations (view calls)\n   * Always uses reliable RPC providers to avoid \"missing revert data\" errors\n   */ static async getReadProvider() {\n        await this.initialize();\n        if (this.reliableProviders.length === 0) {\n            throw new Error('No reliable providers available');\n        }\n        // Always use the first working reliable provider for reads\n        for (const provider of this.reliableProviders){\n            try {\n                await provider.getBlockNumber(); // Quick connectivity test\n                return provider;\n            } catch (error) {\n                console.warn('Reliable provider failed, trying next...');\n            }\n        }\n        throw new Error('All reliable providers failed');\n    }\n    /**\n   * Get a provider for write operations (transactions)\n   * Uses browser provider for signing, but with fallback options\n   */ static async getWriteProvider() {\n        await this.initialize();\n        if (!this.browserProvider) {\n            throw new Error('Browser provider not available. Please connect your wallet.');\n        }\n        return this.browserProvider;\n    }\n    /**\n   * Perform a contract call with automatic fallback\n   * Tries browser provider first, falls back to reliable provider\n   */ static async safeCall(contractAddress, abi, functionName) {\n        let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        await this.initialize();\n        // Strategy 1: Try browser provider first (for consistency with wallet)\n        if (this.browserProvider) {\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, this.browserProvider);\n                const result = await contract[functionName](...args);\n                console.log(\"✅ Browser provider call successful: \".concat(functionName));\n                return result;\n            } catch (error) {\n                var _error_message;\n                console.warn(\"❌ Browser provider failed for \".concat(functionName, \":\"), error.message);\n                // Don't retry browser provider for \"missing revert data\" errors\n                if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('missing revert data')) {\n                    console.log('🔄 Switching to reliable provider due to missing revert data');\n                }\n            }\n        }\n        // Strategy 2: Use reliable provider as fallback\n        const readProvider = await this.getReadProvider();\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, readProvider);\n        const result = await contract[functionName](...args);\n        console.log(\"✅ Reliable provider call successful: \".concat(functionName));\n        return result;\n    }\n    /**\n   * Perform a transaction with enhanced error handling\n   */ static async safeTransaction(contractAddress, abi, functionName) {\n        let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [], overrides = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n        const writeProvider = await this.getWriteProvider();\n        const signer = await writeProvider.getSigner();\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, signer);\n        // Get current gas pricing from reliable provider\n        const readProvider = await this.getReadProvider();\n        const feeData = await readProvider.getFeeData();\n        const txOverrides = {\n            gasLimit: 150000,\n            gasPrice: feeData.gasPrice ? feeData.gasPrice * 120n / 100n : ethers__WEBPACK_IMPORTED_MODULE_3__.parseUnits('50', 'gwei'),\n            ...overrides\n        };\n        console.log(\"\\uD83D\\uDD04 Sending transaction: \".concat(functionName, \" with gas price \").concat(ethers__WEBPACK_IMPORTED_MODULE_3__.formatUnits(txOverrides.gasPrice, 'gwei'), \" gwei\"));\n        const tx = await contract[functionName](...args, txOverrides);\n        return tx;\n    }\n    /**\n   * Reset all providers (useful for troubleshooting)\n   */ static reset() {\n        this.reliableProviders = [];\n        this.browserProvider = null;\n        this.initialized = false;\n        console.log('🔄 FallbackProvider reset');\n    }\n}\nFallbackProvider.reliableProviders = [];\nFallbackProvider.browserProvider = null;\nFallbackProvider.initialized = false;\n/**\n * Convenience function for safe contract calls\n */ async function safeContractCall(contractAddress, abi, functionName) {\n    let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n    return FallbackProvider.safeCall(contractAddress, abi, functionName, args);\n}\n/**\n * Safe contract call with optional fallback value\n * Returns the fallback value if the function doesn't exist or fails\n */ async function safeOptionalContractCall(contractAddress, abi, functionName, fallbackValue) {\n    let args = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : [];\n    try {\n        return await FallbackProvider.safeCall(contractAddress, abi, functionName, args);\n    } catch (error) {\n        console.log(\"⚠️ Optional function \".concat(functionName, \" not available, using fallback:\"), fallbackValue);\n        return fallbackValue;\n    }\n}\n/**\n * Convenience function for safe transactions\n */ async function safeContractTransaction(contractAddress, abi, functionName) {\n    let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [], overrides = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n    return FallbackProvider.safeTransaction(contractAddress, abi, functionName, args, overrides);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/fallbackProvider.ts\n"));

/***/ })

});