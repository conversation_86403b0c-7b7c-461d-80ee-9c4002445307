import { ethers } from 'ethers';

/**
 * Fallback-first provider that bypasses browser provider issues
 * Uses reliable RPC endpoints as primary, browser provider as secondary
 */
export class FallbackProvider {
  private static reliableProviders: ethers.JsonRpcProvider[] = [];
  private static browserProvider: ethers.BrowserProvider | null = null;
  private static initialized = false;

  static async initialize() {
    if (this.initialized) return;

    // Initialize reliable RPC providers
    const rpcUrls = [
      'https://rpc-amoy.polygon.technology/',
      'https://polygon-amoy.drpc.org',
      'https://polygon-amoy-bor-rpc.publicnode.com'
    ];

    for (const url of rpcUrls) {
      try {
        const provider = new ethers.JsonRpcProvider(url);
        await provider.getBlockNumber(); // Test connectivity
        this.reliableProviders.push(provider);
        console.log(`✅ Reliable provider added: ${url}`);
      } catch (error) {
        console.warn(`❌ RPC failed: ${url}`);
      }
    }

    // Initialize browser provider if available
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        this.browserProvider = new ethers.BrowserProvider(window.ethereum);
        console.log('✅ Browser provider initialized');
      } catch (error) {
        console.warn('❌ Browser provider failed to initialize');
      }
    }

    this.initialized = true;
    console.log(`🔧 FallbackProvider initialized with ${this.reliableProviders.length} reliable providers`);
  }

  /**
   * Get a provider for read-only operations (view calls)
   * Always uses reliable RPC providers to avoid "missing revert data" errors
   */
  static async getReadProvider(): Promise<ethers.JsonRpcProvider> {
    await this.initialize();

    if (this.reliableProviders.length === 0) {
      throw new Error('No reliable providers available');
    }

    // Always use the first working reliable provider for reads
    for (const provider of this.reliableProviders) {
      try {
        await provider.getBlockNumber(); // Quick connectivity test
        return provider;
      } catch (error) {
        console.warn('Reliable provider failed, trying next...');
      }
    }

    throw new Error('All reliable providers failed');
  }

  /**
   * Get a provider for write operations (transactions)
   * Uses browser provider for signing, but with fallback options
   */
  static async getWriteProvider(): Promise<ethers.BrowserProvider> {
    await this.initialize();

    if (!this.browserProvider) {
      throw new Error('Browser provider not available. Please connect your wallet.');
    }

    return this.browserProvider;
  }

  /**
   * Perform a contract call with automatic fallback
   * Tries browser provider first, falls back to reliable provider
   */
  static async safeCall<T>(
    contractAddress: string,
    abi: any[],
    functionName: string,
    args: any[] = []
  ): Promise<T> {
    await this.initialize();

    // Strategy 1: Try browser provider first (for consistency with wallet)
    if (this.browserProvider) {
      try {
        const contract = new ethers.Contract(contractAddress, abi, this.browserProvider);
        const result = await contract[functionName](...args);
        console.log(`✅ Browser provider call successful: ${functionName}`);
        return result;
      } catch (error: any) {
        console.warn(`❌ Browser provider failed for ${functionName}:`, error.message);
        
        // Don't retry browser provider for "missing revert data" errors
        if (error.message?.includes('missing revert data')) {
          console.log('🔄 Switching to reliable provider due to missing revert data');
        }
      }
    }

    // Strategy 2: Use reliable provider as fallback
    const readProvider = await this.getReadProvider();
    const contract = new ethers.Contract(contractAddress, abi, readProvider);
    const result = await contract[functionName](...args);
    console.log(`✅ Reliable provider call successful: ${functionName}`);
    return result;
  }

  /**
   * Perform a transaction with enhanced error handling
   */
  static async safeTransaction(
    contractAddress: string,
    abi: any[],
    functionName: string,
    args: any[] = [],
    overrides: any = {}
  ): Promise<ethers.ContractTransactionResponse> {
    const writeProvider = await this.getWriteProvider();
    const signer = await writeProvider.getSigner();
    const contract = new ethers.Contract(contractAddress, abi, signer);

    // Get current gas pricing from reliable provider
    const readProvider = await this.getReadProvider();
    const feeData = await readProvider.getFeeData();
    
    const txOverrides = {
      gasLimit: 150000,
      gasPrice: feeData.gasPrice ? feeData.gasPrice * 120n / 100n : ethers.parseUnits('50', 'gwei'),
      ...overrides
    };

    console.log(`🔄 Sending transaction: ${functionName} with gas price ${ethers.formatUnits(txOverrides.gasPrice, 'gwei')} gwei`);
    
    const tx = await contract[functionName](...args, txOverrides);
    return tx;
  }

  /**
   * Reset all providers (useful for troubleshooting)
   */
  static reset() {
    this.reliableProviders = [];
    this.browserProvider = null;
    this.initialized = false;
    console.log('🔄 FallbackProvider reset');
  }
}

/**
 * Convenience function for safe contract calls
 */
export async function safeContractCall<T>(
  contractAddress: string,
  abi: any[],
  functionName: string,
  args: any[] = []
): Promise<T> {
  return FallbackProvider.safeCall<T>(contractAddress, abi, functionName, args);
}

/**
 * Safe contract call with optional fallback value
 * Returns the fallback value if the function doesn't exist or fails
 */
export async function safeOptionalContractCall<T>(
  contractAddress: string,
  abi: any[],
  functionName: string,
  fallbackValue: T,
  args: any[] = []
): Promise<T> {
  try {
    return await FallbackProvider.safeCall<T>(contractAddress, abi, functionName, args);
  } catch (error) {
    console.log(`⚠️ Optional function ${functionName} not available, using fallback:`, fallbackValue);
    return fallbackValue;
  }
}

/**
 * Convenience function for safe transactions
 */
export async function safeContractTransaction(
  contractAddress: string,
  abi: any[],
  functionName: string,
  args: any[] = [],
  overrides: any = {}
): Promise<ethers.ContractTransactionResponse> {
  return FallbackProvider.safeTransaction(contractAddress, abi, functionName, args, overrides);
}
