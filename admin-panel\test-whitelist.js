const fetch = require('node-fetch');

async function testWhitelist() {
  const testAddress = '******************************************'; // Fresh generated address
  const tokenAddress = '******************************************';
  
  console.log('🧪 Testing ERC-3643 Whitelist API');
  console.log('Test Address:', testAddress);
  console.log('Token Address:', tokenAddress);
  console.log('');

  try {
    const response = await fetch('http://localhost:6677/api/admin/add-to-whitelist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        userAddress: testAddress,
        country: 'US'
      }),
    });

    const result = await response.json();
    
    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Body:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Test PASSED');
      if (result.results) {
        console.log('📈 Process Results:');
        console.log('  - Identity Created:', result.results.identityCreated);
        console.log('  - Identity Registered:', result.results.identityRegistered);
        console.log('  - Whitelisted:', result.results.whitelisted);
        console.log('  - Transaction Hashes:', result.results.txHashes);
        console.log('  - Errors:', result.results.errors);
      }
    } else {
      console.log('❌ Test FAILED');
      console.log('Error:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Test ERROR:', error.message);
  }
}

testWhitelist();
