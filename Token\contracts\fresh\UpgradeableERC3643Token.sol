// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

/**
 * @title UpgradeableERC3643Token
 * @dev ERC-3643 compliant security token with optional upgradeability
 * 
 * Features:
 * - Full ERC-3643 compliance with Identity Registry and Compliance
 * - Optional upgradeability with timelock protection
 * - Ability to permanently renounce upgrades for maximum trust
 * - Institutional-grade security and governance
 * - Force transfer, freezing, and custody features
 */
contract UpgradeableERC3643Token is
    Initializable,
    ERC20Upgradeable,
    AccessControlUpgradeable,
    UUPSUpgradeable
{
    // =============================================================================
    // ROLES
    // =============================================================================
    
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant COMPLIANCE_ROLE = keccak256("COMPLIANCE_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");

    // =============================================================================
    // ERC-3643 CORE COMPONENTS
    // =============================================================================
    
    address public identityRegistryAddress;
    address public complianceModule;
    string public tokenVersion;
    uint256 public maxSupply;

    // =============================================================================
    // UPGRADE CONTROL STATE
    // =============================================================================
    
    bool public upgradesRenounced;
    uint256 public upgradeTimelock;
    uint256 public pendingUpgradeTimestamp;
    address public pendingImplementation;
    
    // =============================================================================
    // SECURITY FEATURES STATE
    // =============================================================================
    
    mapping(address => bool) public frozen;
    mapping(address => uint256) public frozenBalances;
    
    // =============================================================================
    // EVENTS
    // =============================================================================
    
    // Upgrade Events
    event UpgradeProposed(address indexed newImplementation, uint256 executeAfter);
    event UpgradeExecuted(address indexed newImplementation);
    event UpgradesRenounced(uint256 timestamp);
    event TimelockUpdated(uint256 newTimelock);
    
    // ERC-3643 Events
    event IdentityRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event ComplianceUpdated(address indexed oldCompliance, address indexed newCompliance);
    
    // Security Events
    event ForcedTransfer(address indexed from, address indexed to, uint256 amount, address indexed agent);
    event AddressFrozen(address indexed account, address indexed agent);
    event AddressUnfrozen(address indexed account, address indexed agent);
    event TokensFrozen(address indexed account, uint256 amount, address indexed agent);
    event TokensUnfrozen(address indexed account, uint256 amount, address indexed agent);

    // =============================================================================
    // MODIFIERS
    // =============================================================================
    
    modifier onlyIfUpgradesAllowed() {
        require(!upgradesRenounced, "Upgrades permanently disabled");
        _;
    }
    
    modifier onlyAgent() {
        require(hasRole(AGENT_ROLE, msg.sender), "Caller is not an agent");
        _;
    }
    
    modifier onlyCompliance() {
        require(hasRole(COMPLIANCE_ROLE, msg.sender), "Caller is not compliance");
        _;
    }

    modifier notFrozen(address account) {
        require(!frozen[account], "Account is frozen");
        _;
    }

    // =============================================================================
    // INITIALIZATION
    // =============================================================================
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        uint256 maxSupply_,
        address admin_,
        address identityRegistry_,
        address compliance_,
        string memory version_
    ) public initializer {
        __ERC20_init(name_, symbol_);
        __AccessControl_init();
        __UUPSUpgradeable_init();

        // Set decimals (ERC20Upgradeable doesn't have _setupDecimals)
        // We'll override decimals() function instead
        
        maxSupply = maxSupply_;
        identityRegistryAddress = identityRegistry_;
        complianceModule = compliance_;
        tokenVersion = version_;
        
        // Setup roles
        _grantRole(DEFAULT_ADMIN_ROLE, admin_);
        _grantRole(AGENT_ROLE, admin_);
        _grantRole(COMPLIANCE_ROLE, admin_);
        _grantRole(UPGRADER_ROLE, admin_);
        
        // Initialize upgrade settings
        upgradeTimelock = 30 days; // 30-day timelock by default
        upgradesRenounced = false;
    }

    // =============================================================================
    // ERC-3643 COMPLIANCE FUNCTIONS
    // =============================================================================
    
    /**
     * @dev Returns the identity registry address
     */
    function identityRegistry() external view returns (address) {
        return identityRegistryAddress;
    }
    
    /**
     * @dev Returns the compliance module address
     */
    function compliance() external view returns (address) {
        return complianceModule;
    }
    
    /**
     * @dev Returns the version of the token
     */
    function version() external view returns (string memory) {
        return tokenVersion;
    }
    
    /**
     * @dev Checks if an address is verified in the identity registry
     */
    function isVerified(address user) public view returns (bool) {
        if (identityRegistryAddress == address(0)) return true; // If no registry, assume verified

        // Call the identity registry to check verification
        (bool success, bytes memory data) = identityRegistryAddress.staticcall(
            abi.encodeWithSignature("isVerified(address)", user)
        );
        
        if (success && data.length >= 32) {
            return abi.decode(data, (bool));
        }
        
        return false; // Default to not verified if call fails
    }

    // =============================================================================
    // UPGRADE CONTROL FUNCTIONS
    // =============================================================================
    
    /**
     * @dev Propose an upgrade with timelock protection
     */
    function proposeUpgrade(address newImplementation) 
        external 
        onlyRole(UPGRADER_ROLE) 
        onlyIfUpgradesAllowed 
    {
        require(newImplementation != address(0), "Invalid implementation");
        require(pendingImplementation == address(0), "Upgrade already pending");
        
        pendingImplementation = newImplementation;
        pendingUpgradeTimestamp = block.timestamp + upgradeTimelock;
        
        emit UpgradeProposed(newImplementation, pendingUpgradeTimestamp);
    }
    
    /**
     * @dev Execute a pending upgrade after timelock expires
     */
    function executeUpgrade() external onlyRole(UPGRADER_ROLE) onlyIfUpgradesAllowed {
        require(pendingImplementation != address(0), "No pending upgrade");
        require(block.timestamp >= pendingUpgradeTimestamp, "Timelock not expired");
        
        address impl = pendingImplementation;
        pendingImplementation = address(0);
        pendingUpgradeTimestamp = 0;
        
        upgradeToAndCall(impl, "");
        emit UpgradeExecuted(impl);
    }
    
    /**
     * @dev Cancel a pending upgrade
     */
    function cancelUpgrade() external onlyRole(UPGRADER_ROLE) {
        require(pendingImplementation != address(0), "No pending upgrade");
        
        pendingImplementation = address(0);
        pendingUpgradeTimestamp = 0;
    }
    
    /**
     * @dev Permanently disable all future upgrades (IRREVERSIBLE)
     */
    function renounceUpgrades() external onlyRole(DEFAULT_ADMIN_ROLE) {
        upgradesRenounced = true;
        pendingImplementation = address(0);
        pendingUpgradeTimestamp = 0;
        
        emit UpgradesRenounced(block.timestamp);
    }
    
    /**
     * @dev Increase the upgrade timelock (can only increase for security)
     */
    function increaseTimelock(uint256 newTimelock) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(newTimelock > upgradeTimelock, "Can only increase timelock");
        upgradeTimelock = newTimelock;
        emit TimelockUpdated(newTimelock);
    }

    // =============================================================================
    // SECURITY & CUSTODY FUNCTIONS
    // =============================================================================
    
    /**
     * @dev Force transfer tokens (custody feature)
     */
    function forcedTransfer(
        address from,
        address to,
        uint256 amount
    ) external onlyAgent returns (bool) {
        require(from != address(0), "Transfer from zero address");
        require(to != address(0), "Transfer to zero address");
        require(balanceOf(from) >= amount, "Insufficient balance");
        
        // Force transfer bypasses normal transfer restrictions
        _transfer(from, to, amount);
        
        emit ForcedTransfer(from, to, amount, msg.sender);
        return true;
    }
    
    /**
     * @dev Freeze an entire account
     */
    function freeze(address account) external onlyAgent {
        require(account != address(0), "Cannot freeze zero address");
        frozen[account] = true;
        emit AddressFrozen(account, msg.sender);
    }
    
    /**
     * @dev Unfreeze an account
     */
    function unfreeze(address account) external onlyAgent {
        require(account != address(0), "Cannot unfreeze zero address");
        frozen[account] = false;
        emit AddressUnfrozen(account, msg.sender);
    }
    
    /**
     * @dev Freeze a specific amount of tokens for an account
     */
    function freezeTokens(address account, uint256 amount) external onlyAgent {
        require(account != address(0), "Cannot freeze tokens for zero address");
        require(amount > 0, "Amount must be greater than 0");
        require(balanceOf(account) >= frozenBalances[account] + amount, "Insufficient unfrozen balance");
        
        frozenBalances[account] += amount;
        emit TokensFrozen(account, amount, msg.sender);
    }
    
    /**
     * @dev Unfreeze a specific amount of tokens for an account
     */
    function unfreezeTokens(address account, uint256 amount) external onlyAgent {
        require(account != address(0), "Cannot unfreeze tokens for zero address");
        require(amount > 0, "Amount must be greater than 0");
        require(frozenBalances[account] >= amount, "Insufficient frozen balance");
        
        frozenBalances[account] -= amount;
        emit TokensUnfrozen(account, amount, msg.sender);
    }

    // =============================================================================
    // VIEW FUNCTIONS
    // =============================================================================
    
    /**
     * @dev Check if an account is frozen
     */
    function isFrozen(address account) external view returns (bool) {
        return frozen[account];
    }
    
    /**
     * @dev Get the frozen balance of an account
     */
    function frozenBalanceOf(address account) external view returns (uint256) {
        return frozenBalances[account];
    }
    
    /**
     * @dev Get the available (unfrozen) balance of an account
     */
    function availableBalanceOf(address account) external view returns (uint256) {
        uint256 totalBalance = balanceOf(account);
        uint256 frozenAmount = frozenBalances[account];
        return totalBalance > frozenAmount ? totalBalance - frozenAmount : 0;
    }

    // =============================================================================
    // OVERRIDES
    // =============================================================================
    
    /**
     * @dev Override transfer to include compliance and freezing checks
     */
    function _update(
        address from,
        address to,
        uint256 amount
    ) internal override notFrozen(from) notFrozen(to) {
        // Check compliance if not minting/burning
        if (from != address(0) && to != address(0)) {
            require(isVerified(from), "Sender not verified");
            require(isVerified(to), "Recipient not verified");

            // Check available balance (not frozen)
            if (from != address(0)) {
                uint256 availableBalance = balanceOf(from) - frozenBalances[from];
                require(availableBalance >= amount, "Insufficient available balance");
            }
        }

        super._update(from, to, amount);
    }
    
    /**
     * @dev Override decimals to return custom value
     */
    function decimals() public pure override returns (uint8) {
        return 18; // Can be made configurable if needed
    }
    
    /**
     * @dev Required by UUPSUpgradeable
     */
    function _authorizeUpgrade(address newImplementation) 
        internal 
        override 
        onlyRole(UPGRADER_ROLE) 
        onlyIfUpgradesAllowed 
    {
        // Authorization is handled in proposeUpgrade/executeUpgrade pattern
        // This function is called by the UUPS proxy during upgrade
    }

    // =============================================================================
    // ADMIN FUNCTIONS
    // =============================================================================
    
    /**
     * @dev Mint tokens (only admin)
     */
    function mint(address to, uint256 amount) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(totalSupply() + amount <= maxSupply, "Exceeds max supply");
        require(isVerified(to), "Recipient not verified");
        _mint(to, amount);
    }
    
    /**
     * @dev Burn tokens (only admin)
     */
    function burn(address from, uint256 amount) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _burn(from, amount);
    }
    
    /**
     * @dev Update identity registry (only admin)
     */
    function setIdentityRegistry(address newRegistry) external onlyRole(DEFAULT_ADMIN_ROLE) {
        address oldRegistry = identityRegistryAddress;
        identityRegistryAddress = newRegistry;
        emit IdentityRegistryUpdated(oldRegistry, newRegistry);
    }
    
    /**
     * @dev Update compliance module (only admin)
     */
    function setCompliance(address newCompliance) external onlyRole(DEFAULT_ADMIN_ROLE) {
        address oldCompliance = complianceModule;
        complianceModule = newCompliance;
        emit ComplianceUpdated(oldCompliance, newCompliance);
    }
    
    // Pause functionality removed for simplicity
}
