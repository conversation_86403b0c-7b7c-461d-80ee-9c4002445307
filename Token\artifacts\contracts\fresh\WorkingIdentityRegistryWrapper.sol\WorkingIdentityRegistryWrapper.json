{"_format": "hh-sol-artifact-1", "contractName": "WorkingIdentityRegistryWrapper", "sourceName": "contracts/fresh/WorkingIdentityRegistryWrapper.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": false, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "CountryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}], "name": "IdentityDeleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "identity", "type": "address"}, {"indexed": false, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "IdentityRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "userAddresses", "type": "address[]"}, {"internalType": "address[]", "name": "onchainIds", "type": "address[]"}, {"internalType": "uint16[]", "name": "countries", "type": "uint16[]"}], "name": "batchRegisterIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "deleteIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnderlyingRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "identity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_underlyingRegistry", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "investorCountry", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "address", "name": "onchainId", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "registerIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "underlyingRegistry", "outputs": [{"internalType": "contract IWorkingUnderlyingRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "updateCountry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newRegistry", "type": "address"}], "name": "updateUnderlyingRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}