// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/AccessControl.sol";

interface IWorkingUnderlyingRegistry {
    function isVerified(address userAddress) external view returns (bool);
    function registerIdentity(address userAddress, uint16 country) external;
}

/**
 * @title SimpleWorkingWrapper
 * @dev A simple, non-upgradeable Identity Registry Wrapper that works
 */
contract SimpleWorkingWrapper is AccessControl {
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    IWorkingUnderlyingRegistry public underlyingRegistry;
    
    event IdentityRegistered(address indexed userAddress, address indexed identity, uint16 country);
    
    constructor(address _underlyingRegistry, address _admin) {
        require(_underlyingRegistry != address(0), "SimpleWrapper: invalid registry address");
        require(_admin != address(0), "SimpleWrapper: invalid admin address");
        
        underlyingRegistry = IWorkingUnderlyingRegistry(_underlyingRegistry);
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(AGENT_ROLE, _admin);
    }
    
    /**
     * @dev Check if a user is verified
     */
    function isVerified(address userAddress) external view returns (bool) {
        require(userAddress != address(0), "SimpleWrapper: invalid user address");
        return underlyingRegistry.isVerified(userAddress);
    }
    
    /**
     * @dev Register a user's identity (ERC-3643 compatible signature)
     */
    function registerIdentity(
        address userAddress,
        address onchainId, // Ignored for compatibility
        uint16 country
    ) external onlyRole(AGENT_ROLE) {
        require(userAddress != address(0), "SimpleWrapper: invalid user address");
        require(country > 0, "SimpleWrapper: invalid country code");
        
        underlyingRegistry.registerIdentity(userAddress, country);
        emit IdentityRegistered(userAddress, onchainId, country);
    }
    
    /**
     * @dev Get the country of a user (compatibility function)
     */
    function investorCountry(address userAddress) external pure returns (uint16) {
        require(userAddress != address(0), "SimpleWrapper: invalid user address");
        return 840; // Default USA
    }
    
    /**
     * @dev Get the OnchainID of a user (compatibility function)
     */
    function identity(address userAddress) external pure returns (address) {
        require(userAddress != address(0), "SimpleWrapper: invalid user address");
        return address(0); // Placeholder
    }
    
    /**
     * @dev Add an agent
     */
    function addAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(agent != address(0), "SimpleWrapper: invalid agent address");
        _grantRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Remove an agent
     */
    function removeAgent(address agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        _revokeRole(AGENT_ROLE, agent);
    }
    
    /**
     * @dev Check if an address is an agent
     */
    function isAgent(address agent) external view returns (bool) {
        return hasRole(AGENT_ROLE, agent);
    }
}
