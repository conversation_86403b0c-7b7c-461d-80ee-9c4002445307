/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/deploy-proper-token/route";
exports.ids = ["app/api/deploy-proper-token/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-proper-token%2Froute&page=%2Fapi%2Fdeploy-proper-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-proper-token%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-proper-token%2Froute&page=%2Fapi%2Fdeploy-proper-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-proper-token%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_deploy_proper_token_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/deploy-proper-token/route.ts */ \"(rsc)/./src/app/api/deploy-proper-token/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/deploy-proper-token/route\",\n        pathname: \"/api/deploy-proper-token\",\n        filename: \"route\",\n        bundlePath: \"app/api/deploy-proper-token/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\deploy-proper-token\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_deploy_proper_token_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-proper-token%2Froute&page=%2Fapi%2Fdeploy-proper-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-proper-token%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/deploy-proper-token/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/deploy-proper-token/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// ProperERC3643Token ABI - only the functions we need\nconst PROPER_ERC3643_TOKEN_ABI = [\n    \"function initialize(string memory name, string memory symbol, address admin, string memory version_) public\",\n    \"function name() external view returns (string memory)\",\n    \"function symbol() external view returns (string memory)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function version() external view returns (string memory)\",\n    \"function identityRegistryAddress() external view returns (address)\",\n    \"function complianceAddress() external view returns (address)\",\n    \"function hasRole(bytes32 role, address account) external view returns (bool)\",\n    \"function mint(address to, uint256 amount) external\",\n    \"function isWhitelisted(address account) external view returns (bool)\",\n    \"function isVerified(address account) external view returns (bool)\",\n    \"function registerAndWhitelist(address account, uint16 country) external\",\n    \"function getTokenInfo() external view returns (string memory, string memory, uint8, uint256, string memory, bool)\",\n    \"function getAccountInfo(address account) external view returns (uint256, uint256, uint256, bool, bool)\"\n];\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_3__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_2__.exec);\n// Path to the Token project\nconst TOKEN_PROJECT_PATH = path__WEBPACK_IMPORTED_MODULE_4___default().join(process.cwd(), '..', 'Token');\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log('🚀 Deploying Proper ERC-3643 Token with data:', body);\n        // Validate required fields\n        const requiredFields = [\n            'name',\n            'symbol',\n            'adminAddress'\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Create a deployment script with the parameters\n        const deploymentScript = `\nconst { ethers, upgrades } = require(\"hardhat\");\n\nasync function main() {\n  try {\n    console.log(\"🚀 Deploying ProperERC3643Token via API...\");\n\n    const [deployer] = await ethers.getSigners();\n    console.log(\"Deploying with account:\", deployer.address);\n\n    // Check balance\n    const balance = await deployer.provider.getBalance(deployer.address);\n    console.log(\"Deployer balance:\", ethers.formatEther(balance), \"MATIC\");\n\n    console.log(\"Getting contract factory...\");\n    const ProperERC3643Token = await ethers.getContractFactory(\"ProperERC3643Token\");\n    console.log(\"Contract factory created successfully\");\n\n    console.log(\"Deploying proxy with parameters:\");\n    console.log(\"- Name: ${body.name}\");\n    console.log(\"- Symbol: ${body.symbol}\");\n    console.log(\"- Admin: ${body.adminAddress}\");\n    console.log(\"- Version: ${body.version || '1.0.0'}\");\n\n    const token = await upgrades.deployProxy(\n      ProperERC3643Token,\n      [\"${body.name}\", \"${body.symbol}\", \"${body.adminAddress}\", \"${body.version || '1.0.0'}\"],\n      {\n        kind: 'uups',\n        initializer: 'initialize'\n      }\n    );\n\n    console.log(\"Proxy deployed, waiting for deployment...\");\n    await token.waitForDeployment();\n\n    const tokenAddress = await token.getAddress();\n    console.log(\"Token deployed at:\", tokenAddress);\n\n    let implementationAddress;\n    try {\n      implementationAddress = await upgrades.erc1967.getImplementationAddress(tokenAddress);\n      console.log(\"Implementation address:\", implementationAddress);\n    } catch (implError) {\n      console.log(\"Warning: Could not get implementation address:\", implError.message);\n      implementationAddress = \"Unknown\";\n    }\n\n    // Verify deployment with basic calls\n    console.log(\"Verifying deployment...\");\n    const [name, symbol, version] = await Promise.all([\n      token.name(),\n      token.symbol(),\n      token.version()\n    ]);\n\n    console.log(\"Verification successful:\");\n    console.log(\"- Name:\", name);\n    console.log(\"- Symbol:\", symbol);\n    console.log(\"- Version:\", version);\n\n    const result = {\n      success: true,\n      tokenAddress: tokenAddress,\n      implementationAddress: implementationAddress,\n      name: name,\n      symbol: symbol,\n      version: version,\n      deployer: deployer.address\n    };\n\n    console.log(\"DEPLOYMENT_RESULT:\", JSON.stringify(result));\n    return result;\n\n  } catch (error) {\n    console.error(\"Deployment error:\", error.message);\n    console.error(\"Stack:\", error.stack);\n    const errorResult = {\n      success: false,\n      error: error.message,\n      stack: error.stack\n    };\n    console.log(\"DEPLOYMENT_RESULT:\", JSON.stringify(errorResult));\n    throw error;\n  }\n}\n\nmain()\n  .then(() => process.exit(0))\n  .catch((error) => {\n    console.error(\"Script failed:\", error.message);\n    process.exit(1);\n  });\n`;\n        // Write the deployment script to a temporary file\n        const fs = __webpack_require__(/*! fs */ \"fs\");\n        const tempScriptPath = path__WEBPACK_IMPORTED_MODULE_4___default().join(TOKEN_PROJECT_PATH, 'scripts', 'temp-deploy-api.js');\n        fs.writeFileSync(tempScriptPath, deploymentScript);\n        console.log('📝 Created temporary deployment script');\n        // Execute the deployment script\n        console.log('🚀 Executing deployment...');\n        const { stdout, stderr } = await execAsync(`npx hardhat run scripts/temp-deploy-api.js --network amoy --config hardhat.config.fresh.js`, {\n            cwd: TOKEN_PROJECT_PATH,\n            timeout: 120000 // 2 minutes timeout\n        });\n        console.log('Deployment stdout:', stdout);\n        if (stderr) {\n            console.log('Deployment stderr:', stderr);\n        }\n        // Clean up the temporary script\n        fs.unlinkSync(tempScriptPath);\n        // Parse the result from the deployment script output\n        const lines = stdout.split('\\n');\n        let deploymentResult = null;\n        for (const line of lines){\n            try {\n                if (line.includes('DEPLOYMENT_RESULT:')) {\n                    const jsonPart = line.substring(line.indexOf('DEPLOYMENT_RESULT:') + 'DEPLOYMENT_RESULT:'.length).trim();\n                    const parsed = JSON.parse(jsonPart);\n                    if (parsed.success && parsed.tokenAddress) {\n                        deploymentResult = parsed;\n                        break;\n                    } else if (!parsed.success) {\n                        throw new Error(`Deployment failed: ${parsed.error}`);\n                    }\n                }\n            } catch (e) {\n                if (line.includes('DEPLOYMENT_RESULT:')) {\n                    console.error('Failed to parse deployment result:', e.message);\n                    throw new Error(`Failed to parse deployment result: ${e.message}`);\n                }\n            // Not our result line, continue\n            }\n        }\n        if (!deploymentResult) {\n            console.error('Full deployment output:', stdout);\n            throw new Error('No deployment result found in script output');\n        }\n        console.log('✅ Deployment successful:', deploymentResult);\n        // Save to database\n        const tokenRecord = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.create({\n            data: {\n                address: deploymentResult.tokenAddress,\n                transactionHash: null,\n                blockNumber: null,\n                network: 'amoy',\n                name: body.name,\n                symbol: body.symbol,\n                decimals: 18,\n                maxSupply: body.maxSupply || '1000000',\n                totalSupply: '0',\n                tokenType: 'Proper ERC-3643 Security Token',\n                tokenPrice: body.tokenPrice || '1.00',\n                currency: body.currency || 'USD',\n                adminAddress: body.adminAddress,\n                hasKYC: true,\n                isActive: true,\n                selectedClaims: Array.isArray(body.selectedClaims) ? body.selectedClaims.join(',') : body.selectedClaims || null,\n                deployedBy: deploymentResult.deployer,\n                deploymentNotes: `Proper ERC-3643 token deployed with implementation ${deploymentResult.implementationAddress}`\n            }\n        });\n        console.log('✅ Token record saved to database');\n        const response = {\n            success: true,\n            message: 'Proper ERC-3643 token deployed successfully',\n            token: {\n                address: deploymentResult.tokenAddress,\n                name: deploymentResult.name,\n                symbol: deploymentResult.symbol,\n                version: deploymentResult.version,\n                implementationAddress: deploymentResult.implementationAddress,\n                adminAddress: body.adminAddress,\n                network: 'amoy',\n                tokenType: 'Proper ERC-3643 Security Token',\n                features: {\n                    erc3643Compliant: true,\n                    upgradeable: true,\n                    hasWhitelist: true,\n                    hasIdentityRegistry: true,\n                    hasComplianceModule: true,\n                    hasRoleBasedAccess: true,\n                    hasForcedTransfer: true,\n                    hasTokenFreezing: true,\n                    hasPauseFunction: true\n                }\n            },\n            databaseRecord: tokenRecord,\n            adminPanelUrl: `http://localhost:6677/reliable-tokens?token=${deploymentResult.tokenAddress}`,\n            polygonScanUrl: `https://amoy.polygonscan.com/address/${deploymentResult.tokenAddress}`\n        };\n        console.log('🎉 Deployment complete!');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('❌ Token deployment failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Token deployment failed',\n            details: error?.message || 'Unknown error',\n            stack:  true ? error?.stack : 0\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/deploy-proper-token/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-proper-token%2Froute&page=%2Fapi%2Fdeploy-proper-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-proper-token%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();