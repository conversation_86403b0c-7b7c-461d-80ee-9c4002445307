const { ethers } = require('hardhat');

// Configuration
const WORKING_UNDERLYING_REGISTRY = '******************************************';
const ADMIN_ADDRESS = '******************************************';

async function deploySimpleFresh() {
  console.log('🚀 DEPLOYING SIMPLE FRESH ERC-3643 SYSTEM');
  console.log('='.repeat(80));
  console.log('');

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Deployer Balance: ${ethers.formatEther(await ethers.provider.getBalance(deployer.address))} MATIC`);
    console.log(`🆔 Working Underlying Registry: ${WORKING_UNDERLYING_REGISTRY}`);
    console.log(`👑 Admin Address: ${ADMIN_ADDRESS}`);
    console.log('');

    // STEP 1: Verify admin is verified in the working registry
    console.log('🔍 STEP 1: VERIFYING ADMIN IN WORKING REGISTRY');
    console.log('-'.repeat(60));
    
    const workingRegistryABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const workingRegistry = new ethers.Contract(
      WORKING_UNDERLYING_REGISTRY, 
      workingRegistryABI, 
      deployer
    );
    
    const adminVerified = await workingRegistry.isVerified(ADMIN_ADDRESS);
    console.log(`✅ Admin verified in working registry: ${adminVerified}`);
    
    if (!adminVerified) {
      console.log('❌ DEPLOYMENT STOPPED: Admin is not verified in the working registry');
      console.log('💡 Please register the admin in the Identity Registry first');
      return;
    }
    console.log('');

    // STEP 2: Deploy Simple Working Wrapper
    console.log('🔧 STEP 2: DEPLOYING SIMPLE WORKING WRAPPER');
    console.log('-'.repeat(60));
    
    const SimpleWrapper = await ethers.getContractFactory('SimpleWorkingWrapper');
    
    console.log('📝 Deploying simple wrapper...');
    const wrapper = await SimpleWrapper.deploy(WORKING_UNDERLYING_REGISTRY, ADMIN_ADDRESS);
    await wrapper.waitForDeployment();
    
    const wrapperAddress = await wrapper.getAddress();
    console.log(`✅ Simple Wrapper deployed: ${wrapperAddress}`);
    
    // Verify wrapper sees admin as verified
    const wrapperVerified = await wrapper.isVerified(ADMIN_ADDRESS);
    console.log(`🔍 Wrapper sees admin as verified: ${wrapperVerified}`);
    
    if (!wrapperVerified) {
      console.log('❌ CRITICAL: Wrapper doesn\'t see admin as verified');
      console.log('This will cause token functionality issues');
      return;
    }
    console.log('');

    // STEP 3: Deploy Simple Fresh Token
    console.log('🪙 STEP 3: DEPLOYING SIMPLE FRESH TOKEN');
    console.log('-'.repeat(60));
    
    const SimpleFreshToken = await ethers.getContractFactory('SimpleFreshToken');
    
    console.log('📝 Deploying simple token...');
    const token = await SimpleFreshToken.deploy(
      'Simple Fresh ERC3643 Token',
      'SIMPLE3643',
      18,
      ethers.parseUnits('1000000', 18), // 1 million max supply
      wrapperAddress,
      ADMIN_ADDRESS
    );
    await token.waitForDeployment();
    
    const tokenAddress = await token.getAddress();
    console.log(`✅ Simple Fresh Token deployed: ${tokenAddress}`);
    console.log('');

    // STEP 4: Verify Token Functionality
    console.log('🧪 STEP 4: VERIFYING TOKEN FUNCTIONALITY');
    console.log('-'.repeat(60));
    
    // Check token info
    const tokenName = await token.name();
    const tokenSymbol = await token.symbol();
    const tokenDecimals = await token.decimals();
    const tokenMaxSupply = await token.maxSupply();
    const tokenIdentityRegistry = await token.identityRegistry();
    
    console.log(`📋 Token Name: ${tokenName}`);
    console.log(`🏷️ Token Symbol: ${tokenSymbol}`);
    console.log(`🔢 Token Decimals: ${tokenDecimals}`);
    console.log(`📊 Max Supply: ${ethers.formatUnits(tokenMaxSupply, tokenDecimals)}`);
    console.log(`🆔 Identity Registry: ${tokenIdentityRegistry}`);
    
    // Check if admin is verified via token
    const tokenVerified = await token.isVerified(ADMIN_ADDRESS);
    console.log(`✅ Token sees admin as verified: ${tokenVerified}`);
    
    if (!tokenVerified) {
      console.log('❌ CRITICAL: Token doesn\'t see admin as verified');
      console.log('This will prevent minting and other operations');
      return;
    }
    console.log('');

    // STEP 5: Test Minting (as deployer who has admin role)
    console.log('💰 STEP 5: TESTING TOKEN MINTING');
    console.log('-'.repeat(60));
    
    // Check if deployer has admin role
    const deployerHasAdminRole = await token.hasRole(await token.DEFAULT_ADMIN_ROLE(), deployer.address);
    const deployerHasAgentRole = await token.hasRole(await token.AGENT_ROLE(), deployer.address);
    
    console.log(`🔍 Deployer has admin role: ${deployerHasAdminRole}`);
    console.log(`🔍 Deployer has agent role: ${deployerHasAgentRole}`);
    
    if (!deployerHasAgentRole) {
      console.log('❌ Deployer doesn\'t have agent role, cannot mint');
      console.log('💡 The admin role was granted to a different address');
      return;
    }
    
    const mintAmount = ethers.parseUnits('1000', 18);
    console.log(`🔄 Attempting to mint ${ethers.formatUnits(mintAmount, 18)} tokens to admin...`);
    
    try {
      const mintTx = await token.mint(ADMIN_ADDRESS, mintAmount);
      console.log(`⏳ Mint transaction submitted: ${mintTx.hash}`);
      
      await mintTx.wait();
      console.log(`✅ Mint transaction confirmed!`);
      
      const balance = await token.balanceOf(ADMIN_ADDRESS);
      console.log(`💰 Admin balance: ${ethers.formatUnits(balance, 18)} ${tokenSymbol}`);
      console.log('');

      // STEP 6: Test Additional Features
      console.log('🔧 STEP 6: TESTING ADDITIONAL FEATURES');
      console.log('-'.repeat(60));
      
      // Test available balance
      const availableBalance = await token.availableBalanceOf(ADMIN_ADDRESS);
      console.log(`💳 Available balance: ${ethers.formatUnits(availableBalance, 18)} ${tokenSymbol}`);
      
      const frozenBalance = await token.frozenBalanceOf(ADMIN_ADDRESS);
      console.log(`🧊 Frozen balance: ${ethers.formatUnits(frozenBalance, 18)} ${tokenSymbol}`);
      
      // Test agent status
      const adminIsAgent = await token.isAgent(ADMIN_ADDRESS);
      console.log(`🕵️ Admin is agent: ${adminIsAgent}`);
      
      // Test pause functionality
      console.log('⏸️ Testing pause functionality...');
      const pauseTx = await token.pause();
      await pauseTx.wait();
      console.log('✅ Token paused successfully');
      
      const unpauseTx = await token.unpause();
      await unpauseTx.wait();
      console.log('▶️ Token unpaused successfully');
      console.log('');

      // STEP 7: Final Summary
      console.log('🎉 DEPLOYMENT COMPLETE!');
      console.log('='.repeat(80));
      console.log('');
      console.log('📊 DEPLOYMENT SUMMARY:');
      console.log(`🆔 Simple Working Wrapper: ${wrapperAddress}`);
      console.log(`🪙 Simple Fresh ERC-3643 Token: ${tokenAddress}`);
      console.log(`👤 Admin Address: ${ADMIN_ADDRESS}`);
      console.log(`✅ Admin Verified: ${tokenVerified}`);
      console.log(`💰 Initial Balance: ${ethers.formatUnits(balance, 18)} ${tokenSymbol}`);
      console.log('');
      console.log('🎯 TOKEN STATUS: FULLY FUNCTIONAL!');
      console.log('✅ Identity verification working');
      console.log('✅ Minting successful');
      console.log('✅ All institutional features available');
      console.log('✅ Pause/unpause working');
      console.log('✅ Ready for production use');
      console.log('');
      console.log('🚀 NEXT STEPS:');
      console.log('1. Register additional users in the Identity Registry');
      console.log('2. Test transfers between verified users');
      console.log('3. Configure additional agents if needed');
      console.log('4. Set up monitoring and operational procedures');
      console.log('');
      console.log('🎉 SUCCESS! Your fresh ERC-3643 token with working wrapper is ready!');

      return {
        workingWrapper: wrapperAddress,
        freshToken: tokenAddress,
        adminVerified: tokenVerified,
        initialBalance: ethers.formatUnits(balance, 18),
        success: true
      };

    } catch (mintError) {
      console.log(`❌ Mint failed: ${mintError.message}`);
      
      if (mintError.message.includes('user not verified')) {
        console.log('🔍 Admin verification issue detected');
        console.log('The token doesn\'t see admin as verified for minting');
      } else if (mintError.message.includes('AccessControl')) {
        console.log('🔍 Access control issue detected');
        console.log('The deployer doesn\'t have the required role');
      }
      
      return {
        workingWrapper: wrapperAddress,
        freshToken: tokenAddress,
        adminVerified: tokenVerified,
        initialBalance: '0',
        success: false,
        error: mintError.message
      };
    }

  } catch (error) {
    console.error('❌ DEPLOYMENT FAILED:', error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  deploySimpleFresh()
    .then((result) => {
      if (result && result.success) {
        console.log('✅ Deployment completed successfully');
        process.exit(0);
      } else {
        console.log('⚠️ Deployment completed with issues');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('❌ Deployment failed');
      process.exit(1);
    });
}

module.exports = { deploySimpleFresh };
