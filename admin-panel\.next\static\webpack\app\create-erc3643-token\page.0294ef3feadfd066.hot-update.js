"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-erc3643-token/page",{

/***/ "(app-pages-browser)/./src/app/create-erc3643-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-erc3643-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateERC3643TokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/factory.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract ABIs - Updated for ERC-3643 compliant tokens\nconst SecurityTokenCoreABI = [\n    \"function initialize(string memory name_, string memory symbol_, uint8 decimals_, uint256 maxSupply_, address admin_, string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_, string memory tokenImageUrl_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function maxSupply() view returns (uint256)\",\n    \"function identityRegistry() view returns (address)\",\n    \"function compliance() view returns (address)\",\n    \"function onchainID() view returns (address)\",\n    \"function isERC3643Compliant() view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst IdentityRegistryABI = [\n    \"function initialize(address admin_, address claimRegistry_) external\"\n];\nconst ComplianceABI = [\n    \"function initialize(address admin_, address identityRegistry_) external\"\n];\nconst TrustedIssuersRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function addTrustedIssuer(address trustedIssuer, uint256[] calldata claimTopics) external\"\n];\nconst ClaimTopicsRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function batchAddClaimTopics(uint256[] calldata topics) external\"\n];\nconst IdentityContractFactoryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst ERC3643TokenWrapperABI = [\n    \"function initialize(address underlyingToken_, address erc3643IdentityRegistry_, address erc3643Compliance_, address admin_, address onchainID_, string memory version_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\"\n];\nfunction CreateERC3643TokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [upgradeableDeploying, setUpgradeableDeploying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 2,\n        maxSupply: '1000000',\n        adminAddress: '',\n        tokenPrice: '100.00',\n        currency: 'USD',\n        bonusTiers: 'Early: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'ERC-3643 compliant security token with built-in identity registry and compliance modules',\n        tokenImageUrl: '',\n        enableERC3643: true,\n        initialClaimTopics: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ] // KYC, AML, Identity, Qualification, Accreditation, Residence\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateERC3643TokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateERC3643TokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'MATIC');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.1')) {\n                    setError('Insufficient balance. You need at least 0.1 MATIC for gas fees to deploy all contracts.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployContract = async function(contractName, bytecode, abi) {\n        let constructorArgs = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        if (!signer) throw new Error('No signer available');\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory(abi, bytecode, signer);\n        const contract = await factory.deploy(...constructorArgs);\n        await contract.waitForDeployment();\n        const address = await contract.getAddress();\n        console.log(\"\".concat(contractName, \" deployed to:\"), address);\n        return address;\n    };\n    const deployProxyContract = async (contractName, implementationAddress, initData)=>{\n        if (!signer) throw new Error('No signer available');\n        // ERC1967Proxy bytecode (simplified)\n        const proxyBytecode = \"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\";\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory([\n            \"constructor(address implementation, bytes memory data)\"\n        ], proxyBytecode, signer);\n        const proxy = await factory.deploy(implementationAddress, initData);\n        await proxy.waitForDeployment();\n        const address = await proxy.getAddress();\n        console.log(\"\".concat(contractName, \" proxy deployed to:\"), address);\n        return address;\n    };\n    const deployFullFeaturedToken = async ()=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Checking working ERC-3643 infrastructure...');\n            // First, try to deploy a real contract using our working infrastructure\n            setDeploymentStep('🚀 Deploying real ERC-3643 contract with working infrastructure...');\n            const realDeployResponse = await fetch('/api/deploy-real-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy'\n                })\n            });\n            const realResult = await realDeployResponse.json();\n            if (realResult.success) {\n                // Real deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: realResult.realTokenAddress,\n                    erc3643TokenWrapper: realResult.wrapperAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: realResult.realTokenAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: realResult.adminVerified,\n                    contracts: realResult.contracts,\n                    explorerUrls: realResult.explorerUrls,\n                    features: realResult.features,\n                    deploymentType: 'Real ERC-3643 Contract',\n                    erc3643Components: {\n                        workingWrapper: realResult.wrapperAddress,\n                        workingUnderlyingRegistry: realResult.contracts.workingUnderlyingRegistry,\n                        tokenAddress: realResult.realTokenAddress\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Real ERC-3643 Token deployed successfully using working infrastructure!\\n\\n✅ Token Address: \".concat(realResult.realTokenAddress, \"\\n✅ Wrapper Address: \").concat(realResult.wrapperAddress, \"\\n✅ Admin Verified: \").concat(realResult.adminVerified ? 'Yes' : 'No', \"\\n✅ Ready for institutional use!\\n\\n\\uD83D\\uDD17 View on Explorer: https://amoy.polygonscan.com/address/\").concat(realResult.realTokenAddress));\n                setDeploymentStep('');\n            } else {\n                // Real deployment failed, fall back to deployment plan\n                setDeploymentStep('⚠️ Real deployment failed, creating deployment plan with working infrastructure...');\n                const planResponse = await fetch('/api/deploy-fresh-token', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        decimals: formData.decimals,\n                        maxSupply: formData.maxSupply,\n                        adminAddress: formData.adminAddress,\n                        network: 'amoy'\n                    })\n                });\n                const planResult = await planResponse.json();\n                if (planResult.success) {\n                    var _planResult_systemStatus, _planResult_systemStatus1;\n                    setDeployedToken({\n                        success: true,\n                        securityTokenCore: planResult.primaryTokenAddress,\n                        erc3643TokenWrapper: planResult.contracts.workingWrapper,\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        primaryTokenAddress: planResult.primaryTokenAddress,\n                        isERC3643Compliant: true,\n                        systemStatus: planResult.systemStatus,\n                        contracts: planResult.contracts,\n                        deploymentScript: planResult.deploymentScript,\n                        explorerUrls: planResult.explorerUrls,\n                        features: planResult.features,\n                        deploymentType: 'Deployment Plan (Working Infrastructure)',\n                        erc3643Components: {\n                            workingWrapper: planResult.contracts.workingWrapper,\n                            workingUnderlyingRegistry: planResult.contracts.workingUnderlyingRegistry,\n                            planAddress: planResult.primaryTokenAddress\n                        }\n                    });\n                    setSuccess(\"\\uD83D\\uDCCB ERC-3643 Token deployment plan created successfully using working infrastructure!\\n\\n✅ Plan Address: \".concat(planResult.primaryTokenAddress, \"\\n✅ System Status: \").concat(((_planResult_systemStatus = planResult.systemStatus) === null || _planResult_systemStatus === void 0 ? void 0 : _planResult_systemStatus.systemHealthy) ? 'Healthy' : 'Issues Detected', \"\\n✅ Working Infrastructure: Ready\\n✅ Admin Verified: \").concat(((_planResult_systemStatus1 = planResult.systemStatus) === null || _planResult_systemStatus1 === void 0 ? void 0 : _planResult_systemStatus1.adminVerified) ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Use the provided deployment script to deploy the actual contract.\\n\\uD83D\\uDD17 Working Infrastructure: All components verified and functional\"));\n                    setDeploymentStep('');\n                } else {\n                    throw new Error(\"Both real deployment and plan creation failed: \".concat(planResult.error));\n                }\n            }\n        } catch (error) {\n            console.error('Error deploying token:', error);\n            setError(\"Failed to deploy token using working infrastructure: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while trying to use the verified working ERC-3643 infrastructure.\\n\\uD83D\\uDCA1 The working infrastructure includes:\\n   - Working Identity Registry: 0x1281a057881cbe94dc2a79561275aa6b35bf7854\\n   - Working Wrapper: 0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02\\n   - Admin Verification: Confirmed working\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setIsSubmitting(false);\n            setDeploymentStep('');\n        }\n    };\n    const deployUpgradeableToken = async ()=>{\n        setUpgradeableDeploying(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Deploying upgradeable ERC-3643 token with working infrastructure...');\n            const response = await fetch('/api/deploy-upgradeable-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Upgradeable deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: result.proxyAddress,\n                    erc3643TokenWrapper: result.implementationAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: result.proxyAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: result.adminVerified,\n                    contracts: result.contracts,\n                    explorerUrls: result.explorerUrls,\n                    features: result.features,\n                    deploymentType: 'Upgradeable ERC-3643 Contract',\n                    upgradeInfo: result.upgradeInfo,\n                    upgradeManagement: result.upgradeManagement,\n                    erc3643Components: {\n                        proxy: result.proxyAddress,\n                        implementation: result.implementationAddress,\n                        identityRegistry: result.contracts.identityRegistry\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Upgradeable ERC-3643 Token deployed successfully!\\n\\n✅ Proxy Address: \".concat(result.proxyAddress, \"\\n✅ Implementation: \").concat(result.implementationAddress, \"\\n✅ Admin Verified: \").concat(result.adminVerified ? 'Yes' : 'No', \"\\n✅ Upgradeable: \").concat(result.upgradeInfo.canUpgrade ? 'Yes' : 'Renounced', \"\\n✅ Upgrade Timelock: \").concat(result.upgradeInfo.upgradeTimelock, \"\\n✅ Ready for institutional use with optional upgradeability!\\n\\n\\uD83D\\uDD17 View Proxy: https://amoy.polygonscan.com/address/\").concat(result.proxyAddress, \"\\n\\uD83D\\uDD17 View Implementation: https://amoy.polygonscan.com/address/\").concat(result.implementationAddress));\n                setDeploymentStep('');\n            } else {\n                throw new Error(\"Upgradeable deployment failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Upgradeable deployment error:', error);\n            setError(\"Failed to deploy upgradeable token: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while deploying the upgradeable ERC-3643 token.\\n\\uD83D\\uDCA1 The upgradeable token includes:\\n   - UUPS proxy pattern for gas efficiency\\n   - Optional upgrade renunciation for maximum trust\\n   - 30-day timelock protection\\n   - All ERC-3643 compliance features\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setUpgradeableDeploying(false);\n            setDeploymentStep('');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployFullFeaturedToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create ERC-3643 Compliant Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create fully ERC-3643 compliant security tokens with built-in identity registry and compliance modules.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n            lineNumber: 457,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create ERC-3643 Compliant Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800\",\n                        children: \"✅ Working Infrastructure\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 483,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"✅\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"ERC-3643 Compliant Security Token with Working Infrastructure\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Deploy fully compliant ERC-3643 security tokens using verified working infrastructure:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Working Infrastructure:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Uses verified functional Identity Registry and Wrapper\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Admin Pre-Verified:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Admin address is already verified and ready to use\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Real Contract Deployment:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Attempts to deploy actual contracts first\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Fallback Plan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Creates deployment plan if real deployment fails\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Full ERC-3643 Compliance:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" All standard functions and security features\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Institutional Ready:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Freeze, force transfer, whitelist, KYC included\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"\\uD83C\\uDF89 Now using verified working ERC-3643 infrastructure!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" All components tested and functional.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 497,\n                columnNumber: 7\n            }, this),\n            isSubmitting && deploymentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-600 mr-2\",\n                            children: \"⏳\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: deploymentStep\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 526,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 536,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 546,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.securityTokenCore\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this),\n                            deployedToken.erc3643TokenWrapper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ERC-3643 Wrapper:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.erc3643TokenWrapper\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.securityTokenCore), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 556,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Full Featured Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., FFST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    \"Decimals * \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"(0=shares, 2=currency, 18=crypto)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals \",\n                                                            i === 0 ? '(whole numbers)' : i === 2 ? '(currency-like)' : i === 18 ? '(crypto-like)' : ''\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"100.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 746,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-lg mr-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"ERC-3643 Compliance Enabled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-700 mt-1\",\n                                                        children: \"All tokens are now ERC-3643 compliant by default with built-in identityRegistry(), compliance(), and onchainID() functions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                        children: isSubmitting ? '🔄 Creating ERC-3643 Compliant Token...' : '🏆 Create ERC-3643 Compliant Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: deployUpgradeableToken,\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-green-500\",\n                                        children: upgradeableDeploying ? '🔄 Creating Upgradeable ERC-3643 Token...' : '🔧 Create Upgradeable ERC-3643 Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 bg-gray-50 p-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83C\\uDFC6 Standard:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Immutable contract (maximum security, no upgrades possible)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83D\\uDD27 Upgradeable:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" UUPS proxy pattern (can be upgraded with 30-day timelock, can renounce upgrades)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 791,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 597,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-xl\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-800\",\n                                    children: \"All Features Included\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every token deployed includes: Configurable decimals, Upgradeable architecture, Role-based access control, Mint/burn capabilities, Force transfer, Address freezing, On-chain whitelist/KYC, Batch operations, Recovery mechanisms, and comprehensive events.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 font-medium text-green-700\",\n                                            children: \"Plus ERC-3643 Compliance: Built-in identityRegistry(), compliance(), onchainID() functions, Individual identity contracts, Claims-based verification, Trusted issuers registry, Compliance engine, and institutional-grade features.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 825,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 824,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n        lineNumber: 482,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateERC3643TokenPage, \"2M9FzfsPIJuuxwN/U0JykwKjnUw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateERC3643TokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateERC3643TokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-erc3643-token/page.tsx\n"));

/***/ })

});