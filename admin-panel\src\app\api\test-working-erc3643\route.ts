import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL)
const WORKING_ERC3643_CONTRACTS = {
  // Working underlying registry (verified functional)
  workingUnderlyingRegistry: "******************************************",
  
  // Fresh working wrapper (just deployed and verified)
  workingWrapper: "******************************************",
  
  // Example working token (for reference)
  workingTokenExample: "******************************************"
};

const RPC_URL = process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/';
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const ADMIN_ADDRESS = '******************************************';

// GET /api/test-working-erc3643 - Test the working ERC-3643 system
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 TESTING WORKING ERC-3643 SYSTEM FROM ADMIN PANEL');

    if (!PRIVATE_KEY) {
      return NextResponse.json({
        success: false,
        error: 'Admin private key not configured'
      }, { status: 500 });
    }

    // Create provider and signer
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const adminWallet = new ethers.Wallet(PRIVATE_KEY, provider);

    console.log(`🔑 Admin: ${adminWallet.address}`);

    // Test 1: Check underlying registry
    console.log('🔍 TEST 1: CHECKING UNDERLYING REGISTRY');
    
    const underlyingRegistryABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const underlyingRegistry = new ethers.Contract(
      WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry,
      underlyingRegistryABI,
      adminWallet
    );
    
    const underlyingVerified = await underlyingRegistry.isVerified(ADMIN_ADDRESS);
    console.log(`✅ Admin verified in underlying registry: ${underlyingVerified}`);

    // Test 2: Check working wrapper
    console.log('🔍 TEST 2: CHECKING WORKING WRAPPER');
    
    const wrapperABI = [
      "function isVerified(address userAddress) external view returns (bool)",
      "function underlyingRegistry() external view returns (address)"
    ];
    
    const wrapper = new ethers.Contract(
      WORKING_ERC3643_CONTRACTS.workingWrapper,
      wrapperABI,
      adminWallet
    );
    
    const wrapperVerified = await wrapper.isVerified(ADMIN_ADDRESS);
    const wrapperUnderlying = await wrapper.underlyingRegistry();
    
    console.log(`✅ Admin verified in wrapper: ${wrapperVerified}`);
    console.log(`🔗 Wrapper's underlying registry: ${wrapperUnderlying}`);

    // Test 3: Check working token example
    console.log('🔍 TEST 3: CHECKING WORKING TOKEN EXAMPLE');
    
    const tokenABI = [
      "function name() external view returns (string memory)",
      "function symbol() external view returns (string memory)",
      "function decimals() external view returns (uint8)",
      "function totalSupply() external view returns (uint256)",
      "function maxSupply() external view returns (uint256)",
      "function identityRegistry() external view returns (address)",
      "function isVerified(address user) external view returns (bool)",
      "function balanceOf(address account) external view returns (uint256)",
      "function availableBalanceOf(address account) external view returns (uint256)",
      "function frozenBalanceOf(address account) external view returns (uint256)"
    ];
    
    const token = new ethers.Contract(
      WORKING_ERC3643_CONTRACTS.workingTokenExample,
      tokenABI,
      adminWallet
    );
    
    const tokenName = await token.name();
    const tokenSymbol = await token.symbol();
    const tokenDecimals = await token.decimals();
    const tokenTotalSupply = await token.totalSupply();
    const tokenMaxSupply = await token.maxSupply();
    const tokenIdentityRegistry = await token.identityRegistry();
    const tokenVerified = await token.isVerified(ADMIN_ADDRESS);
    const adminBalance = await token.balanceOf(ADMIN_ADDRESS);
    const adminAvailableBalance = await token.availableBalanceOf(ADMIN_ADDRESS);
    const adminFrozenBalance = await token.frozenBalanceOf(ADMIN_ADDRESS);
    
    console.log(`📋 Token Name: ${tokenName}`);
    console.log(`🏷️ Token Symbol: ${tokenSymbol}`);
    console.log(`🔢 Token Decimals: ${tokenDecimals}`);
    console.log(`📊 Total Supply: ${ethers.formatUnits(tokenTotalSupply, tokenDecimals)}`);
    console.log(`📊 Max Supply: ${ethers.formatUnits(tokenMaxSupply, tokenDecimals)}`);
    console.log(`🆔 Token's Identity Registry: ${tokenIdentityRegistry}`);
    console.log(`✅ Token sees admin as verified: ${tokenVerified}`);
    console.log(`💰 Admin balance: ${ethers.formatUnits(adminBalance, tokenDecimals)} ${tokenSymbol}`);
    console.log(`💳 Admin available balance: ${ethers.formatUnits(adminAvailableBalance, tokenDecimals)} ${tokenSymbol}`);
    console.log(`🧊 Admin frozen balance: ${ethers.formatUnits(adminFrozenBalance, tokenDecimals)} ${tokenSymbol}`);

    // Test 4: System health check
    const systemHealthy = underlyingVerified && wrapperVerified && tokenVerified;
    
    console.log(`🎯 SYSTEM HEALTH: ${systemHealthy ? 'HEALTHY' : 'ISSUES DETECTED'}`);

    return NextResponse.json({
      success: true,
      message: 'Working ERC-3643 system test completed',
      systemHealthy: systemHealthy,
      contracts: WORKING_ERC3643_CONTRACTS,
      testResults: {
        underlyingRegistry: {
          address: WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry,
          adminVerified: underlyingVerified,
          status: underlyingVerified ? 'WORKING' : 'BROKEN'
        },
        workingWrapper: {
          address: WORKING_ERC3643_CONTRACTS.workingWrapper,
          adminVerified: wrapperVerified,
          underlyingRegistry: wrapperUnderlying,
          correctUnderlying: wrapperUnderlying.toLowerCase() === WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry.toLowerCase(),
          status: wrapperVerified ? 'WORKING' : 'BROKEN'
        },
        workingTokenExample: {
          address: WORKING_ERC3643_CONTRACTS.workingTokenExample,
          name: tokenName,
          symbol: tokenSymbol,
          decimals: tokenDecimals,
          totalSupply: ethers.formatUnits(tokenTotalSupply, tokenDecimals),
          maxSupply: ethers.formatUnits(tokenMaxSupply, tokenDecimals),
          identityRegistry: tokenIdentityRegistry,
          adminVerified: tokenVerified,
          adminBalance: ethers.formatUnits(adminBalance, tokenDecimals),
          adminAvailableBalance: ethers.formatUnits(adminAvailableBalance, tokenDecimals),
          adminFrozenBalance: ethers.formatUnits(adminFrozenBalance, tokenDecimals),
          status: tokenVerified ? 'WORKING' : 'BROKEN'
        }
      },
      adminAddress: ADMIN_ADDRESS,
      explorerUrls: {
        underlyingRegistry: `https://amoy.polygonscan.com/address/${WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry}`,
        workingWrapper: `https://amoy.polygonscan.com/address/${WORKING_ERC3643_CONTRACTS.workingWrapper}`,
        workingToken: `https://amoy.polygonscan.com/address/${WORKING_ERC3643_CONTRACTS.workingTokenExample}`
      },
      recommendations: systemHealthy ? [
        'System is fully functional and ready for production use',
        'You can deploy new tokens using the working wrapper',
        'All institutional features are available',
        'Admin is properly verified across all components'
      ] : [
        'System has issues that need to be addressed',
        'Check individual component status for details',
        'May need to re-deploy broken components'
      ]
    });

  } catch (error: any) {
    console.error('❌ Working ERC-3643 test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to test working ERC-3643 system',
      details: error.message,
      contracts: WORKING_ERC3643_CONTRACTS
    }, { status: 500 });
  }
}
