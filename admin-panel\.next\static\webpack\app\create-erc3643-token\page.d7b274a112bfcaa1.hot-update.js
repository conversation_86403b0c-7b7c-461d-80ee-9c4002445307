"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-erc3643-token/page",{

/***/ "(app-pages-browser)/./src/app/create-erc3643-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-erc3643-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateERC3643TokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/factory.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract ABIs - Updated for ERC-3643 compliant tokens\nconst SecurityTokenCoreABI = [\n    \"function initialize(string memory name_, string memory symbol_, uint8 decimals_, uint256 maxSupply_, address admin_, string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_, string memory tokenImageUrl_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function maxSupply() view returns (uint256)\",\n    \"function identityRegistry() view returns (address)\",\n    \"function compliance() view returns (address)\",\n    \"function onchainID() view returns (address)\",\n    \"function isERC3643Compliant() view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst IdentityRegistryABI = [\n    \"function initialize(address admin_, address claimRegistry_) external\"\n];\nconst ComplianceABI = [\n    \"function initialize(address admin_, address identityRegistry_) external\"\n];\nconst TrustedIssuersRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function addTrustedIssuer(address trustedIssuer, uint256[] calldata claimTopics) external\"\n];\nconst ClaimTopicsRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function batchAddClaimTopics(uint256[] calldata topics) external\"\n];\nconst IdentityContractFactoryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst ERC3643TokenWrapperABI = [\n    \"function initialize(address underlyingToken_, address erc3643IdentityRegistry_, address erc3643Compliance_, address admin_, address onchainID_, string memory version_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\"\n];\nfunction CreateERC3643TokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [upgradeableDeploying, setUpgradeableDeploying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 2,\n        maxSupply: '1000000',\n        adminAddress: '',\n        tokenPrice: '100.00',\n        currency: 'USD',\n        bonusTiers: 'Early: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'ERC-3643 compliant security token with built-in identity registry and compliance modules',\n        tokenImageUrl: '',\n        enableERC3643: true,\n        initialClaimTopics: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ],\n        selectedClaims: [\n            'KYC_CLAIM',\n            'QUALIFICATION_CLAIM'\n        ] // Required claims for this token\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateERC3643TokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateERC3643TokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'MATIC');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.1')) {\n                    setError('Insufficient balance. You need at least 0.1 MATIC for gas fees to deploy all contracts.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployContract = async function(contractName, bytecode, abi) {\n        let constructorArgs = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        if (!signer) throw new Error('No signer available');\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory(abi, bytecode, signer);\n        const contract = await factory.deploy(...constructorArgs);\n        await contract.waitForDeployment();\n        const address = await contract.getAddress();\n        console.log(\"\".concat(contractName, \" deployed to:\"), address);\n        return address;\n    };\n    const deployProxyContract = async (contractName, implementationAddress, initData)=>{\n        if (!signer) throw new Error('No signer available');\n        // ERC1967Proxy bytecode (simplified)\n        const proxyBytecode = \"0x608060405234801561001057600080fd5b506040516108e93803806108e983398101604081905261002f916100b9565b61005c60017f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc610179565b6000805160206108a28339815191521461007557610075610196565b61007e826101ac565b6100aa8260008051602061088283398151915283604051806020016040528060008152506101f5565b50506101ac565b600080604083850312156100cc57600080fd5b82516001600160a01b03811681146100e357600080fd5b602084015190925067ffffffffffffffff8082111561010157600080fd5b818501915085601f83011261011557600080fd5b81518181111561012757610127610220565b604051601f8201601f19908116603f0116810190838211818310171561014f5761014f610220565b8160405282815288602084870101111561016857600080fd5b610179836020830160208801610236565b80955050505050509250929050565b60008282101561019a5761019a610256565b500390565b634e487b7160e01b600052600160045260246000fd5b6001600160a01b0381166101d25760405163d92e233d60e01b815260040160405180910390fd5b806000805160206108a283398151915255565b6101fe83610269565b6000825111806102095750805b1561021a5761021883836102c9565b505b505050565b634e487b7160e01b600052604160045260246000fd5b60005b83811015610251578181015183820152602001610239565b838111156102185750506000910152565b634e487b7160e01b600052601160045260246000fd5b610272816101ac565b6040516001600160a01b038216907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b90600090a250565b60606001600160a01b0383163b6102f25760405162461bcd60e51b815260206004820152602660248201527f416464726573733a2064656c65676174652063616c6c20746f206e6f6e2d636f6044820152651b9d1c9858dd60d21b60648201526084015b60405180910390fd5b600080846001600160a01b03168460405161030d9190610355565b600060405180830381855af49150503d8060008114610348576040519150601f19603f3d011682016040523d82523d6000602084013e61034d565b606091505b509150915061035d828261036f565b949350505050565b60008251610377818460208701610236565b9190910192915050565b6060831561037e575081610388565b61038883836103a8565b9392505050565b6001600160a01b03811681146103a457600080fd5b5050565b8151156103b85781518083602001fd5b8060405162461bcd60e51b81526004016102e99190610371565b60006020828403121561040157600080fd5b815161038881610390565b6104b8806103f16000396000f3fe608060405236601057600e6013565b005b600e5b601f601b6021565b6058565b565b6000604c607a565b905073ffffffffffffffffffffffffffffffffffffffff8116603f57600080fd5b3660008037600080366000845af43d6000803e8080156076573d6000f35b3d6000fd5b60007f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc5473ffffffffffffffffffffffffffffffffffffffff16905090565b56fea2646970667358221220c0b5b8c7f8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b864736f6c634300080f0033360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc416464726573733a206c6f772d6c6576656c2064656c65676174652063616c6c206661696c6564\";\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory([\n            \"constructor(address implementation, bytes memory data)\"\n        ], proxyBytecode, signer);\n        const proxy = await factory.deploy(implementationAddress, initData);\n        await proxy.waitForDeployment();\n        const address = await proxy.getAddress();\n        console.log(\"\".concat(contractName, \" proxy deployed to:\"), address);\n        return address;\n    };\n    const deployFullFeaturedToken = async ()=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Checking working ERC-3643 infrastructure...');\n            // First, try to deploy a real contract using our working infrastructure\n            setDeploymentStep('🚀 Deploying real ERC-3643 contract with working infrastructure...');\n            const realDeployResponse = await fetch('/api/deploy-real-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy',\n                    selectedClaims: formData.selectedClaims.join(',')\n                })\n            });\n            const realResult = await realDeployResponse.json();\n            if (realResult.success) {\n                // Real deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: realResult.realTokenAddress,\n                    erc3643TokenWrapper: realResult.wrapperAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: realResult.realTokenAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: realResult.adminVerified,\n                    contracts: realResult.contracts,\n                    explorerUrls: realResult.explorerUrls,\n                    features: realResult.features,\n                    deploymentType: 'Real ERC-3643 Contract',\n                    erc3643Components: {\n                        workingWrapper: realResult.wrapperAddress,\n                        workingUnderlyingRegistry: realResult.contracts.workingUnderlyingRegistry,\n                        tokenAddress: realResult.realTokenAddress\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Real ERC-3643 Token deployed successfully using working infrastructure!\\n\\n✅ Token Address: \".concat(realResult.realTokenAddress, \"\\n✅ Wrapper Address: \").concat(realResult.wrapperAddress, \"\\n✅ Admin Verified: \").concat(realResult.adminVerified ? 'Yes' : 'No', \"\\n✅ Ready for institutional use!\\n\\n\\uD83D\\uDD17 View on Explorer: https://amoy.polygonscan.com/address/\").concat(realResult.realTokenAddress));\n                setDeploymentStep('');\n            } else {\n                // Real deployment failed, fall back to deployment plan\n                setDeploymentStep('⚠️ Real deployment failed, creating deployment plan with working infrastructure...');\n                const planResponse = await fetch('/api/deploy-fresh-token', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        decimals: formData.decimals,\n                        maxSupply: formData.maxSupply,\n                        adminAddress: formData.adminAddress,\n                        network: 'amoy'\n                    })\n                });\n                const planResult = await planResponse.json();\n                if (planResult.success) {\n                    var _planResult_systemStatus, _planResult_systemStatus1;\n                    setDeployedToken({\n                        success: true,\n                        securityTokenCore: planResult.primaryTokenAddress,\n                        erc3643TokenWrapper: planResult.contracts.workingWrapper,\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        primaryTokenAddress: planResult.primaryTokenAddress,\n                        isERC3643Compliant: true,\n                        systemStatus: planResult.systemStatus,\n                        contracts: planResult.contracts,\n                        deploymentScript: planResult.deploymentScript,\n                        explorerUrls: planResult.explorerUrls,\n                        features: planResult.features,\n                        deploymentType: 'Deployment Plan (Working Infrastructure)',\n                        erc3643Components: {\n                            workingWrapper: planResult.contracts.workingWrapper,\n                            workingUnderlyingRegistry: planResult.contracts.workingUnderlyingRegistry,\n                            planAddress: planResult.primaryTokenAddress\n                        }\n                    });\n                    setSuccess(\"\\uD83D\\uDCCB ERC-3643 Token deployment plan created successfully using working infrastructure!\\n\\n✅ Plan Address: \".concat(planResult.primaryTokenAddress, \"\\n✅ System Status: \").concat(((_planResult_systemStatus = planResult.systemStatus) === null || _planResult_systemStatus === void 0 ? void 0 : _planResult_systemStatus.systemHealthy) ? 'Healthy' : 'Issues Detected', \"\\n✅ Working Infrastructure: Ready\\n✅ Admin Verified: \").concat(((_planResult_systemStatus1 = planResult.systemStatus) === null || _planResult_systemStatus1 === void 0 ? void 0 : _planResult_systemStatus1.adminVerified) ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Use the provided deployment script to deploy the actual contract.\\n\\uD83D\\uDD17 Working Infrastructure: All components verified and functional\"));\n                    setDeploymentStep('');\n                } else {\n                    throw new Error(\"Both real deployment and plan creation failed: \".concat(planResult.error));\n                }\n            }\n        } catch (error) {\n            console.error('Error deploying token:', error);\n            setError(\"Failed to deploy token using working infrastructure: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while trying to use the verified working ERC-3643 infrastructure.\\n\\uD83D\\uDCA1 The working infrastructure includes:\\n   - Working Identity Registry: 0x1281a057881cbe94dc2a79561275aa6b35bf7854\\n   - Working Wrapper: 0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02\\n   - Admin Verification: Confirmed working\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setIsSubmitting(false);\n            setDeploymentStep('');\n        }\n    };\n    const deployUpgradeableToken = async ()=>{\n        setUpgradeableDeploying(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Deploying upgradeable ERC-3643 token with working infrastructure...');\n            const response = await fetch('/api/deploy-upgradeable-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy',\n                    selectedClaims: formData.selectedClaims.join(',')\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Upgradeable deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: result.proxyAddress,\n                    erc3643TokenWrapper: result.implementationAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: result.proxyAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: result.adminVerified,\n                    contracts: result.contracts,\n                    explorerUrls: result.explorerUrls,\n                    features: result.features,\n                    deploymentType: 'Upgradeable ERC-3643 Contract',\n                    upgradeInfo: result.upgradeInfo,\n                    upgradeManagement: result.upgradeManagement,\n                    erc3643Components: {\n                        proxy: result.proxyAddress,\n                        implementation: result.implementationAddress,\n                        identityRegistry: result.contracts.identityRegistry\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Upgradeable ERC-3643 Token deployed successfully!\\n\\n✅ Proxy Address: \".concat(result.proxyAddress, \"\\n✅ Implementation: \").concat(result.implementationAddress, \"\\n✅ Admin Verified: \").concat(result.adminVerified ? 'Yes' : 'No', \"\\n✅ Upgradeable: \").concat(result.upgradeInfo.canUpgrade ? 'Yes' : 'Renounced', \"\\n✅ Upgrade Timelock: \").concat(result.upgradeInfo.upgradeTimelock, \"\\n✅ Ready for institutional use with optional upgradeability!\\n\\n\\uD83D\\uDD17 View Proxy: https://amoy.polygonscan.com/address/\").concat(result.proxyAddress, \"\\n\\uD83D\\uDD17 View Implementation: https://amoy.polygonscan.com/address/\").concat(result.implementationAddress));\n                setDeploymentStep('');\n            } else {\n                throw new Error(\"Upgradeable deployment failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Upgradeable deployment error:', error);\n            setError(\"Failed to deploy upgradeable token: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while deploying the upgradeable ERC-3643 token.\\n\\uD83D\\uDCA1 The upgradeable token includes:\\n   - UUPS proxy pattern for gas efficiency\\n   - Optional upgrade renunciation for maximum trust\\n   - 30-day timelock protection\\n   - All ERC-3643 compliance features\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setUpgradeableDeploying(false);\n            setDeploymentStep('');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployFullFeaturedToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create ERC-3643 Compliant Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create fully ERC-3643 compliant security tokens with built-in identity registry and compliance modules.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n            lineNumber: 461,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create ERC-3643 Compliant Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800\",\n                        children: \"✅ Working Infrastructure\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-50 border-2 border-red-500 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-600 mr-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-red-800\",\n                                        children: \"⚠️ WARNING: THIS PAGE CREATES BROKEN TOKENS!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-sm text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: \"This deployment system creates basic ERC-20 tokens that are NOT ERC-3643 compliant!\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1\",\n                                                children: \"Tokens created here will be missing:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-1 list-disc list-inside space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"❌ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ERC-3643 Compliance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - Not a security token\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"❌ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"OnchainID Support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - No identity management\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"❌ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Whitelist System\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - No investor verification\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"❌ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Force Transfer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - No compliance controls\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"❌ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Token Freezing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - No security features\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"❌ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"UUPS Upgradeability\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - Cannot be upgraded\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-300 rounded p-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 font-semibold\",\n                                children: \"\\uD83D\\uDE80 USE THE WORKING SYSTEM INSTEAD:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/create-proper-erc3643\",\n                                className: \"inline-block mt-2 bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors\",\n                                children: \"✅ Go to Working ERC-3643 Token Creator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg opacity-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-xl\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-600\",\n                                    children: \"[BROKEN] ERC-3643 Compliant Security Token with Working Infrastructure\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-gray-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"⚠️ This system is broken and creates non-compliant tokens. Use the new system above instead.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, this),\n            isSubmitting && deploymentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-600 mr-2\",\n                            children: \"⏳\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: deploymentStep\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 555,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 554,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 564,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 574,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.securityTokenCore\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this),\n                            deployedToken.erc3643TokenWrapper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ERC-3643 Wrapper:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.erc3643TokenWrapper\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.securityTokenCore), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 584,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Full Featured Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., FFST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    \"Decimals * \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"(0=shares, 2=currency, 18=crypto)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals \",\n                                                            i === 0 ? '(whole numbers)' : i === 2 ? '(currency-like)' : i === 18 ? '(crypto-like)' : ''\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"100.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"\\uD83D\\uDD12 Compliance Claims Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-800 mb-2\",\n                                                children: \"Shared Claims System\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-700 text-sm\",\n                                                children: \"Select which claims are required for this token. Clients who have these claims can invest in multiple tokens without re-verification.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Required Claims for Token Access *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    {\n                                                        id: 'KYC_CLAIM',\n                                                        label: 'KYC Verification',\n                                                        description: 'Know Your Customer verification completed'\n                                                    },\n                                                    {\n                                                        id: 'QUALIFICATION_CLAIM',\n                                                        label: 'Investor Qualification',\n                                                        description: 'Accredited/qualified investor status'\n                                                    },\n                                                    {\n                                                        id: 'AML_CLAIM',\n                                                        label: 'AML Screening',\n                                                        description: 'Anti-Money Laundering screening passed'\n                                                    },\n                                                    {\n                                                        id: 'RESIDENCE_CLAIM',\n                                                        label: 'Residence Verification',\n                                                        description: 'Country/jurisdiction residence verified'\n                                                    },\n                                                    {\n                                                        id: 'IDENTITY_CLAIM',\n                                                        label: 'Identity Verification',\n                                                        description: 'Government ID verification completed'\n                                                    },\n                                                    {\n                                                        id: 'ACCREDITATION_CLAIM',\n                                                        label: 'Accreditation Status',\n                                                        description: 'Professional accreditation verified'\n                                                    }\n                                                ].map((claim)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center h-5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: claim.id,\n                                                                    type: \"checkbox\",\n                                                                    checked: formData.selectedClaims.includes(claim.id),\n                                                                    onChange: (e)=>{\n                                                                        const updatedClaims = e.target.checked ? [\n                                                                            ...formData.selectedClaims,\n                                                                            claim.id\n                                                                        ] : formData.selectedClaims.filter((c)=>c !== claim.id);\n                                                                        setFormData({\n                                                                            ...formData,\n                                                                            selectedClaims: updatedClaims\n                                                                        });\n                                                                    },\n                                                                    className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: claim.id,\n                                                                        className: \"font-medium text-gray-700\",\n                                                                        children: claim.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: claim.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, claim.id, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-xs text-gray-600\",\n                                                children: [\n                                                    \"Selected claims: \",\n                                                    formData.selectedClaims.length > 0 ? formData.selectedClaims.join(', ') : 'None selected'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 831,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-lg mr-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"ERC-3643 Compliance Enabled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-700 mt-1\",\n                                                        children: \"All tokens are now ERC-3643 compliant by default with built-in identityRegistry(), compliance(), and onchainID() functions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                        children: isSubmitting ? '🔄 Creating ERC-3643 Compliant Token...' : '🏆 Create ERC-3643 Compliant Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: deployUpgradeableToken,\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-green-500\",\n                                        children: upgradeableDeploying ? '🔄 Creating Upgradeable ERC-3643 Token...' : '🔧 Create Upgradeable ERC-3643 Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl mr-2\",\n                                                            children: \"\\uD83D\\uDE80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-yellow-800\",\n                                                                    children: \"NEW: Proper ERC-3643 Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-yellow-700\",\n                                                                    children: \"Use our latest, fully tested ERC-3643 implementation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/create-proper-erc3643\",\n                                                className: \"w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-yellow-400 inline-block text-center\",\n                                                children: \"\\uD83D\\uDE80 Use New Proper ERC-3643 Creator\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 903,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 bg-gray-50 p-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83C\\uDFC6 Standard:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Immutable contract (maximum security, no upgrades possible)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83D\\uDD27 Upgradeable:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" UUPS proxy pattern (can be upgraded with 30-day timelock, can renounce upgrades)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 625,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-xl\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 926,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-800\",\n                                    children: \"All Features Included\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every token deployed includes: Configurable decimals, Upgradeable architecture, Role-based access control, Mint/burn capabilities, Force transfer, Address freezing, On-chain whitelist/KYC, Batch operations, Recovery mechanisms, and comprehensive events.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 font-medium text-green-700\",\n                                            children: \"Plus ERC-3643 Compliance: Built-in identityRegistry(), compliance(), onchainID() functions, Individual identity contracts, Claims-based verification, Trusted issuers registry, Compliance engine, and institutional-grade features.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 929,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 925,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 924,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n        lineNumber: 486,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateERC3643TokenPage, \"85xGG4Mkj8zgRThgdInbuaG5p2s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateERC3643TokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateERC3643TokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-erc3643-token/page.tsx\n"));

/***/ })

});