{"_format": "hh-sol-artifact-1", "contractName": "FreshERC3643Token", "sourceName": "contracts/fresh/FreshERC3643Token.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ForcedTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldRegistry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newRegistry", "type": "address"}], "name": "IdentityRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COMPLIANCE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "availableBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "batchMint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "freezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "frozenBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTokenInfo", "outputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "totalSupply_", "type": "uint256"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "contract IWorkingIdentityRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "_identityRegistry", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newRegistry", "type": "address"}], "name": "setIdentityRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unfreezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}