{"_format": "hh-sol-artifact-1", "contractName": "SimpleWorkingWrapper", "sourceName": "contracts/fresh/SimpleWorkingWrapper.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_underlyingRegistry", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "userAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "identity", "type": "address"}, {"indexed": false, "internalType": "uint16", "name": "country", "type": "uint16"}], "name": "IdentityRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "identity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "investorCountry", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "userAddress", "type": "address"}, {"internalType": "address", "name": "onchainId", "type": "address"}, {"internalType": "uint16", "name": "country", "type": "uint16"}], "name": "registerIdentity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "underlyingRegistry", "outputs": [{"internalType": "contract IWorkingUnderlyingRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106101005760003560e01c80637e42683b11610097578063a217fddf11610066578063a217fddf1461024b578063b9209e3314610253578063d547741f14610266578063f0eb5e541461027957600080fd5b80637e42683b146101ec57806384e798421461021257806391d148541461022557806397a6278e1461023857600080fd5b8063248a9ca3116100d3578063248a9ca31461018e5780632f2ff15d146101b157806336568abe146101c6578063454a03e0146101d957600080fd5b806301ffc9a71461010557806318e718db1461012d5780631ffbb0641461015857806322459e181461016b575b600080fd5b6101186101133660046107d0565b61028c565b60405190151581526020015b60405180910390f35b600154610140906001600160a01b031681565b6040516001600160a01b039091168152602001610124565b61011861016636600461081d565b6102c3565b61018060008051602061093183398151915281565b604051908152602001610124565b61018061019c366004610838565b60009081526020819052604090206001015490565b6101c46101bf366004610851565b6102dd565b005b6101c46101d4366004610851565b610308565b6101c46101e736600461087d565b610340565b6101ff6101fa36600461081d565b61049c565b60405161ffff9091168152602001610124565b6101c461022036600461081d565b6104cd565b610118610233366004610851565b610552565b6101c461024636600461081d565b61057b565b610180600081565b61011861026136600461081d565b61059e565b6101c4610274366004610851565b610634565b61014061028736600461081d565b610659565b60006001600160e01b03198216637965db0b60e01b14806102bd57506301ffc9a760e01b6001600160e01b03198316145b92915050565b60006102bd60008051602061093183398151915283610552565b6000828152602081905260409020600101546102f881610689565b6103028383610696565b50505050565b6001600160a01b03811633146103315760405163334bd91960e11b815260040160405180910390fd5b61033b8282610728565b505050565b60008051602061093183398151915261035881610689565b6001600160a01b0384166103875760405162461bcd60e51b815260040161037e906108cb565b60405180910390fd5b60008261ffff16116103e75760405162461bcd60e51b815260206004820152602360248201527f53696d706c65577261707065723a20696e76616c696420636f756e74727920636044820152626f646560e81b606482015260840161037e565b60015460405163c5a80fa760e01b81526001600160a01b03868116600483015261ffff851660248301529091169063c5a80fa790604401600060405180830381600087803b15801561043857600080fd5b505af115801561044c573d6000803e3d6000fd5b505060405161ffff851681526001600160a01b038087169350871691507f74cfd0a46a2905d52ee4947cc1ac80b597bd474e7a0496263f14119e749472869060200160405180910390a350505050565b60006001600160a01b0382166104c45760405162461bcd60e51b815260040161037e906108cb565b50610348919050565b60006104d881610689565b6001600160a01b03821661053a5760405162461bcd60e51b8152602060048201526024808201527f53696d706c65577261707065723a20696e76616c6964206167656e74206164646044820152637265737360e01b606482015260840161037e565b61033b60008051602061093183398151915283610696565b6000918252602082815260408084206001600160a01b0393909316845291905290205460ff1690565b600061058681610689565b61033b60008051602061093183398151915283610728565b60006001600160a01b0382166105c65760405162461bcd60e51b815260040161037e906108cb565b60015460405163b9209e3360e01b81526001600160a01b0384811660048301529091169063b9209e3390602401602060405180830381865afa158015610610573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906102bd919061090e565b60008281526020819052604090206001015461064f81610689565b6103028383610728565b60006001600160a01b0382166106815760405162461bcd60e51b815260040161037e906108cb565b506000919050565b6106938133610793565b50565b60006106a28383610552565b610720576000838152602081815260408083206001600160a01b03861684529091529020805460ff191660011790556106d83390565b6001600160a01b0316826001600160a01b0316847f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45060016102bd565b5060006102bd565b60006107348383610552565b15610720576000838152602081815260408083206001600160a01b0386168085529252808320805460ff1916905551339286917ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b9190a45060016102bd565b61079d8282610552565b6107cc5760405163e2517d3f60e01b81526001600160a01b03821660048201526024810183905260440161037e565b5050565b6000602082840312156107e257600080fd5b81356001600160e01b0319811681146107fa57600080fd5b9392505050565b80356001600160a01b038116811461081857600080fd5b919050565b60006020828403121561082f57600080fd5b6107fa82610801565b60006020828403121561084a57600080fd5b5035919050565b6000806040838503121561086457600080fd5b8235915061087460208401610801565b90509250929050565b60008060006060848603121561089257600080fd5b61089b84610801565b92506108a960208501610801565b9150604084013561ffff811681146108c057600080fd5b809150509250925092565b60208082526023908201527f53696d706c65577261707065723a20696e76616c69642075736572206164647260408201526265737360e81b606082015260800190565b60006020828403121561092057600080fd5b815180151581146107fa57600080fdfecab5a0bfe0b79d2c4b1c2e02599fa044d115b7511f9659307cb4276950967709a2646970667358221220fc9fb1aa796c61ce56ff3b9f7f3cb0094607a0001f20e4ed54e108b5c9f33f0a64736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}