"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-erc3643-token/page",{

/***/ "(app-pages-browser)/./src/app/create-erc3643-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-erc3643-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateERC3643TokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/factory.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract ABIs - Updated for ERC-3643 compliant tokens\nconst SecurityTokenCoreABI = [\n    \"function initialize(string memory name_, string memory symbol_, uint8 decimals_, uint256 maxSupply_, address admin_, string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_, string memory tokenImageUrl_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function maxSupply() view returns (uint256)\",\n    \"function identityRegistry() view returns (address)\",\n    \"function compliance() view returns (address)\",\n    \"function onchainID() view returns (address)\",\n    \"function isERC3643Compliant() view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst IdentityRegistryABI = [\n    \"function initialize(address admin_, address claimRegistry_) external\"\n];\nconst ComplianceABI = [\n    \"function initialize(address admin_, address identityRegistry_) external\"\n];\nconst TrustedIssuersRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function addTrustedIssuer(address trustedIssuer, uint256[] calldata claimTopics) external\"\n];\nconst ClaimTopicsRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function batchAddClaimTopics(uint256[] calldata topics) external\"\n];\nconst IdentityContractFactoryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst ERC3643TokenWrapperABI = [\n    \"function initialize(address underlyingToken_, address erc3643IdentityRegistry_, address erc3643Compliance_, address admin_, address onchainID_, string memory version_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\"\n];\nfunction CreateERC3643TokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [upgradeableDeploying, setUpgradeableDeploying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 2,\n        maxSupply: '1000000',\n        adminAddress: '',\n        tokenPrice: '100.00',\n        currency: 'USD',\n        bonusTiers: 'Early: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'ERC-3643 compliant security token with built-in identity registry and compliance modules',\n        tokenImageUrl: '',\n        enableERC3643: true,\n        initialClaimTopics: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ],\n        selectedClaims: [\n            'KYC_CLAIM',\n            'QUALIFICATION_CLAIM'\n        ] // Required claims for this token\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateERC3643TokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateERC3643TokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'MATIC');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.1')) {\n                    setError('Insufficient balance. You need at least 0.1 MATIC for gas fees to deploy all contracts.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployContract = async function(contractName, bytecode, abi) {\n        let constructorArgs = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        if (!signer) throw new Error('No signer available');\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory(abi, bytecode, signer);\n        const contract = await factory.deploy(...constructorArgs);\n        await contract.waitForDeployment();\n        const address = await contract.getAddress();\n        console.log(\"\".concat(contractName, \" deployed to:\"), address);\n        return address;\n    };\n    const deployProxyContract = async (contractName, implementationAddress, initData)=>{\n        if (!signer) throw new Error('No signer available');\n        // ERC1967Proxy bytecode (simplified)\n        const proxyBytecode = \"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\";\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory([\n            \"constructor(address implementation, bytes memory data)\"\n        ], proxyBytecode, signer);\n        const proxy = await factory.deploy(implementationAddress, initData);\n        await proxy.waitForDeployment();\n        const address = await proxy.getAddress();\n        console.log(\"\".concat(contractName, \" proxy deployed to:\"), address);\n        return address;\n    };\n    const deployFullFeaturedToken = async ()=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Checking working ERC-3643 infrastructure...');\n            // First, try to deploy a real contract using our working infrastructure\n            setDeploymentStep('🚀 Deploying real ERC-3643 contract with working infrastructure...');\n            const realDeployResponse = await fetch('/api/deploy-real-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy',\n                    selectedClaims: formData.selectedClaims.join(',')\n                })\n            });\n            const realResult = await realDeployResponse.json();\n            if (realResult.success) {\n                // Real deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: realResult.realTokenAddress,\n                    erc3643TokenWrapper: realResult.wrapperAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: realResult.realTokenAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: realResult.adminVerified,\n                    contracts: realResult.contracts,\n                    explorerUrls: realResult.explorerUrls,\n                    features: realResult.features,\n                    deploymentType: 'Real ERC-3643 Contract',\n                    erc3643Components: {\n                        workingWrapper: realResult.wrapperAddress,\n                        workingUnderlyingRegistry: realResult.contracts.workingUnderlyingRegistry,\n                        tokenAddress: realResult.realTokenAddress\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Real ERC-3643 Token deployed successfully using working infrastructure!\\n\\n✅ Token Address: \".concat(realResult.realTokenAddress, \"\\n✅ Wrapper Address: \").concat(realResult.wrapperAddress, \"\\n✅ Admin Verified: \").concat(realResult.adminVerified ? 'Yes' : 'No', \"\\n✅ Ready for institutional use!\\n\\n\\uD83D\\uDD17 View on Explorer: https://amoy.polygonscan.com/address/\").concat(realResult.realTokenAddress));\n                setDeploymentStep('');\n            } else {\n                // Real deployment failed, fall back to deployment plan\n                setDeploymentStep('⚠️ Real deployment failed, creating deployment plan with working infrastructure...');\n                const planResponse = await fetch('/api/deploy-fresh-token', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        decimals: formData.decimals,\n                        maxSupply: formData.maxSupply,\n                        adminAddress: formData.adminAddress,\n                        network: 'amoy'\n                    })\n                });\n                const planResult = await planResponse.json();\n                if (planResult.success) {\n                    var _planResult_systemStatus, _planResult_systemStatus1;\n                    setDeployedToken({\n                        success: true,\n                        securityTokenCore: planResult.primaryTokenAddress,\n                        erc3643TokenWrapper: planResult.contracts.workingWrapper,\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        primaryTokenAddress: planResult.primaryTokenAddress,\n                        isERC3643Compliant: true,\n                        systemStatus: planResult.systemStatus,\n                        contracts: planResult.contracts,\n                        deploymentScript: planResult.deploymentScript,\n                        explorerUrls: planResult.explorerUrls,\n                        features: planResult.features,\n                        deploymentType: 'Deployment Plan (Working Infrastructure)',\n                        erc3643Components: {\n                            workingWrapper: planResult.contracts.workingWrapper,\n                            workingUnderlyingRegistry: planResult.contracts.workingUnderlyingRegistry,\n                            planAddress: planResult.primaryTokenAddress\n                        }\n                    });\n                    setSuccess(\"\\uD83D\\uDCCB ERC-3643 Token deployment plan created successfully using working infrastructure!\\n\\n✅ Plan Address: \".concat(planResult.primaryTokenAddress, \"\\n✅ System Status: \").concat(((_planResult_systemStatus = planResult.systemStatus) === null || _planResult_systemStatus === void 0 ? void 0 : _planResult_systemStatus.systemHealthy) ? 'Healthy' : 'Issues Detected', \"\\n✅ Working Infrastructure: Ready\\n✅ Admin Verified: \").concat(((_planResult_systemStatus1 = planResult.systemStatus) === null || _planResult_systemStatus1 === void 0 ? void 0 : _planResult_systemStatus1.adminVerified) ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Use the provided deployment script to deploy the actual contract.\\n\\uD83D\\uDD17 Working Infrastructure: All components verified and functional\"));\n                    setDeploymentStep('');\n                } else {\n                    throw new Error(\"Both real deployment and plan creation failed: \".concat(planResult.error));\n                }\n            }\n        } catch (error) {\n            console.error('Error deploying token:', error);\n            setError(\"Failed to deploy token using working infrastructure: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while trying to use the verified working ERC-3643 infrastructure.\\n\\uD83D\\uDCA1 The working infrastructure includes:\\n   - Working Identity Registry: 0x1281a057881cbe94dc2a79561275aa6b35bf7854\\n   - Working Wrapper: 0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02\\n   - Admin Verification: Confirmed working\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setIsSubmitting(false);\n            setDeploymentStep('');\n        }\n    };\n    const deployUpgradeableToken = async ()=>{\n        setUpgradeableDeploying(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Deploying upgradeable ERC-3643 token with working infrastructure...');\n            const response = await fetch('/api/deploy-upgradeable-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy',\n                    selectedClaims: formData.selectedClaims.join(',')\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Upgradeable deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: result.proxyAddress,\n                    erc3643TokenWrapper: result.implementationAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: result.proxyAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: result.adminVerified,\n                    contracts: result.contracts,\n                    explorerUrls: result.explorerUrls,\n                    features: result.features,\n                    deploymentType: 'Upgradeable ERC-3643 Contract',\n                    upgradeInfo: result.upgradeInfo,\n                    upgradeManagement: result.upgradeManagement,\n                    erc3643Components: {\n                        proxy: result.proxyAddress,\n                        implementation: result.implementationAddress,\n                        identityRegistry: result.contracts.identityRegistry\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Upgradeable ERC-3643 Token deployed successfully!\\n\\n✅ Proxy Address: \".concat(result.proxyAddress, \"\\n✅ Implementation: \").concat(result.implementationAddress, \"\\n✅ Admin Verified: \").concat(result.adminVerified ? 'Yes' : 'No', \"\\n✅ Upgradeable: \").concat(result.upgradeInfo.canUpgrade ? 'Yes' : 'Renounced', \"\\n✅ Upgrade Timelock: \").concat(result.upgradeInfo.upgradeTimelock, \"\\n✅ Ready for institutional use with optional upgradeability!\\n\\n\\uD83D\\uDD17 View Proxy: https://amoy.polygonscan.com/address/\").concat(result.proxyAddress, \"\\n\\uD83D\\uDD17 View Implementation: https://amoy.polygonscan.com/address/\").concat(result.implementationAddress));\n                setDeploymentStep('');\n            } else {\n                throw new Error(\"Upgradeable deployment failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Upgradeable deployment error:', error);\n            setError(\"Failed to deploy upgradeable token: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while deploying the upgradeable ERC-3643 token.\\n\\uD83D\\uDCA1 The upgradeable token includes:\\n   - UUPS proxy pattern for gas efficiency\\n   - Optional upgrade renunciation for maximum trust\\n   - 30-day timelock protection\\n   - All ERC-3643 compliance features\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setUpgradeableDeploying(false);\n            setDeploymentStep('');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployFullFeaturedToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create ERC-3643 Compliant Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create fully ERC-3643 compliant security tokens with built-in identity registry and compliance modules.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n            lineNumber: 461,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create ERC-3643 Compliant Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800\",\n                        children: \"✅ Working Infrastructure\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"✅\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"ERC-3643 Compliant Security Token with Working Infrastructure\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Deploy fully compliant ERC-3643 security tokens using verified working infrastructure:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Working Infrastructure:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Uses verified functional Identity Registry and Wrapper\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Admin Pre-Verified:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Admin address is already verified and ready to use\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Real Contract Deployment:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Attempts to deploy actual contracts first\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Fallback Plan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Creates deployment plan if real deployment fails\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Full ERC-3643 Compliance:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" All standard functions and security features\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Institutional Ready:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Freeze, force transfer, whitelist, KYC included\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"\\uD83C\\uDF89 Now using verified working ERC-3643 infrastructure!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" All components tested and functional.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            isSubmitting && deploymentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-600 mr-2\",\n                            children: \"⏳\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: deploymentStep\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 530,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 540,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 550,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.securityTokenCore\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this),\n                            deployedToken.erc3643TokenWrapper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ERC-3643 Wrapper:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.erc3643TokenWrapper\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 562,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.securityTokenCore), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 560,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Full Featured Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., FFST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    \"Decimals * \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"(0=shares, 2=currency, 18=crypto)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals \",\n                                                            i === 0 ? '(whole numbers)' : i === 2 ? '(currency-like)' : i === 18 ? '(crypto-like)' : ''\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"100.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"\\uD83D\\uDD12 Compliance Claims Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-800 mb-2\",\n                                                children: \"Shared Claims System\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-700 text-sm\",\n                                                children: \"Select which claims are required for this token. Clients who have these claims can invest in multiple tokens without re-verification.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Required Claims for Token Access *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    {\n                                                        id: 'KYC_CLAIM',\n                                                        label: 'KYC Verification',\n                                                        description: 'Know Your Customer verification completed'\n                                                    },\n                                                    {\n                                                        id: 'QUALIFICATION_CLAIM',\n                                                        label: 'Investor Qualification',\n                                                        description: 'Accredited/qualified investor status'\n                                                    },\n                                                    {\n                                                        id: 'AML_CLAIM',\n                                                        label: 'AML Screening',\n                                                        description: 'Anti-Money Laundering screening passed'\n                                                    },\n                                                    {\n                                                        id: 'RESIDENCE_CLAIM',\n                                                        label: 'Residence Verification',\n                                                        description: 'Country/jurisdiction residence verified'\n                                                    },\n                                                    {\n                                                        id: 'IDENTITY_CLAIM',\n                                                        label: 'Identity Verification',\n                                                        description: 'Government ID verification completed'\n                                                    },\n                                                    {\n                                                        id: 'ACCREDITATION_CLAIM',\n                                                        label: 'Accreditation Status',\n                                                        description: 'Professional accreditation verified'\n                                                    }\n                                                ].map((claim)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center h-5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: claim.id,\n                                                                    type: \"checkbox\",\n                                                                    checked: formData.selectedClaims.includes(claim.id),\n                                                                    onChange: (e)=>{\n                                                                        const updatedClaims = e.target.checked ? [\n                                                                            ...formData.selectedClaims,\n                                                                            claim.id\n                                                                        ] : formData.selectedClaims.filter((c)=>c !== claim.id);\n                                                                        setFormData({\n                                                                            ...formData,\n                                                                            selectedClaims: updatedClaims\n                                                                        });\n                                                                    },\n                                                                    className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: claim.id,\n                                                                        className: \"font-medium text-gray-700\",\n                                                                        children: claim.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: claim.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, claim.id, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-xs text-gray-600\",\n                                                children: [\n                                                    \"Selected claims: \",\n                                                    formData.selectedClaims.length > 0 ? formData.selectedClaims.join(', ') : 'None selected'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 803,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-lg mr-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"ERC-3643 Compliance Enabled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-700 mt-1\",\n                                                        children: \"All tokens are now ERC-3643 compliant by default with built-in identityRegistry(), compliance(), and onchainID() functions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                        children: isSubmitting ? '🔄 Creating ERC-3643 Compliant Token...' : '🏆 Create ERC-3643 Compliant Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: deployUpgradeableToken,\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-green-500\",\n                                        children: upgradeableDeploying ? '🔄 Creating Upgradeable ERC-3643 Token...' : '🔧 Create Upgradeable ERC-3643 Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl mr-2\",\n                                                            children: \"\\uD83D\\uDE80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-yellow-800\",\n                                                                    children: \"NEW: Proper ERC-3643 Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-yellow-700\",\n                                                                    children: \"Use our latest, fully tested ERC-3643 implementation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/create-proper-erc3643\",\n                                                className: \"w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-yellow-400 inline-block text-center\",\n                                                children: \"\\uD83D\\uDE80 Use New Proper ERC-3643 Creator\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 bg-gray-50 p-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83C\\uDFC6 Standard:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Immutable contract (maximum security, no upgrades possible)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83D\\uDD27 Upgradeable:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" UUPS proxy pattern (can be upgraded with 30-day timelock, can renounce upgrades)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 888,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 848,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 601,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-xl\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 902,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-800\",\n                                    children: \"All Features Included\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every token deployed includes: Configurable decimals, Upgradeable architecture, Role-based access control, Mint/burn capabilities, Force transfer, Address freezing, On-chain whitelist/KYC, Batch operations, Recovery mechanisms, and comprehensive events.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 font-medium text-green-700\",\n                                            children: \"Plus ERC-3643 Compliance: Built-in identityRegistry(), compliance(), onchainID() functions, Individual identity contracts, Claims-based verification, Trusted issuers registry, Compliance engine, and institutional-grade features.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 905,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 901,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 900,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n        lineNumber: 486,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateERC3643TokenPage, \"85xGG4Mkj8zgRThgdInbuaG5p2s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateERC3643TokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateERC3643TokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-erc3643-token/page.tsx\n"));

/***/ })

});