'use client';

import { useState, useEffect } from 'react';

// Available claim types for token compliance requirements
const AVAILABLE_CLAIMS = [
  { id: 1, name: 'KYC Claim', description: 'Know Your Customer verification', required: true },
  { id: 2, name: 'AML Claim', description: 'Anti-Money Laundering compliance', required: false },
  { id: 3, name: 'Identity Claim', description: 'Identity verification', required: false },
  { id: 4, name: 'Qualification Claim', description: 'Investor qualification status', required: true },
  { id: 5, name: 'Accreditation Claim', description: 'Accredited investor status', required: false },
  { id: 6, name: 'Residence Claim', description: 'Residence verification', required: false },
  { id: 7, name: 'Token Issuer Claim', description: 'Token issuer authorization', required: false }
];

interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  selectedClaims: string | null;
  network: string;
  tokenType: string;
}

export default function TokenClaimsManagementPage() {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedToken, setSelectedToken] = useState<Token | null>(null);
  const [updatingClaims, setUpdatingClaims] = useState(false);

  useEffect(() => {
    fetchTokens();
  }, []);

  const fetchTokens = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/tokens');
      if (!response.ok) {
        throw new Error('Failed to fetch tokens');
      }
      const data = await response.json();
      setTokens(data.tokens || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tokens');
    } finally {
      setLoading(false);
    }
  };

  const updateTokenClaims = async (tokenId: string, newClaims: number[]) => {
    try {
      setUpdatingClaims(true);
      const response = await fetch('/api/token-claims', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenId,
          selectedClaims: newClaims
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update token claims');
      }

      // Refresh tokens list
      await fetchTokens();
      setSelectedToken(null);
      alert('Token claims updated successfully!');
    } catch (err) {
      alert(`Error updating claims: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setUpdatingClaims(false);
    }
  };

  const getTokenClaims = (token: Token): number[] => {
    if (!token.selectedClaims) return [1, 4]; // Default: KYC + Qualification
    return token.selectedClaims.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  };

  const getClaimNames = (claimIds: number[]): string => {
    return AVAILABLE_CLAIMS
      .filter(claim => claimIds.includes(claim.id))
      .map(claim => claim.name)
      .join(', ') || 'None';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading tokens...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <button
            onClick={fetchTokens}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">📋 Token Claims Management</h1>
          <p className="mt-2 text-gray-600">
            Configure which claims are required for each token. Claims are shared across tokens - 
            once a client has a claim, they can invest in any token that requires it.
          </p>
        </div>

        {/* Token Selection */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Select Token to Configure</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tokens.map((token) => (
                <div
                  key={token.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedToken?.id === token.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedToken(token)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">{token.name}</h3>
                    <span className="text-sm text-gray-500">{token.symbol}</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">
                    {token.address.substring(0, 10)}...{token.address.substring(token.address.length - 8)}
                  </p>
                  <div className="text-xs">
                    <span className="text-gray-500">Required Claims:</span>
                    <p className="text-gray-700 mt-1">{getClaimNames(getTokenClaims(token))}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Claims Configuration */}
        {selectedToken && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Configure Claims for {selectedToken.name} ({selectedToken.symbol})
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Select which claims investors must have to hold this token
              </p>
            </div>
            <div className="p-6">
              <ClaimsSelector
                token={selectedToken}
                onUpdate={updateTokenClaims}
                updating={updatingClaims}
              />
            </div>
          </div>
        )}

        {tokens.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No tokens found. Deploy some tokens first!</p>
          </div>
        )}
      </div>
    </div>
  );
}

interface ClaimsSelectorProps {
  token: Token;
  onUpdate: (tokenId: string, claims: number[]) => void;
  updating: boolean;
}

function ClaimsSelector({ token, onUpdate, updating }: ClaimsSelectorProps) {
  const [selectedClaims, setSelectedClaims] = useState<number[]>(() => {
    if (!token.selectedClaims) return [1, 4]; // Default: KYC + Qualification
    return token.selectedClaims.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  });

  const handleClaimToggle = (claimId: number, checked: boolean) => {
    if (checked) {
      setSelectedClaims([...selectedClaims, claimId]);
    } else {
      setSelectedClaims(selectedClaims.filter(id => id !== claimId));
    }
  };

  const handleSave = () => {
    onUpdate(token.id, selectedClaims);
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-sm text-blue-700">
          <strong>🔗 Shared Compliance System:</strong> Claims are issued to client OnchainIDs and can be reused across multiple tokens. 
          When a client has the required claims, they can invest in any token that requires those same claims.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {AVAILABLE_CLAIMS.map((claim) => (
          <label key={claim.id} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg border hover:bg-gray-100">
            <input
              type="checkbox"
              checked={selectedClaims.includes(claim.id)}
              onChange={(e) => handleClaimToggle(claim.id, e.target.checked)}
              disabled={claim.required || updating}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">{claim.name}</span>
                {claim.required && (
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Required</span>
                )}
              </div>
              <p className="text-xs text-gray-600 mt-1">{claim.description}</p>
            </div>
          </label>
        ))}
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-sm text-yellow-800">
          <strong>Currently Selected:</strong> {selectedClaims.length > 0 
            ? AVAILABLE_CLAIMS.filter(c => selectedClaims.includes(c.id)).map(c => c.name).join(', ')
            : 'None selected'
          }
        </p>
      </div>

      <div className="flex justify-end space-x-4">
        <button
          onClick={() => setSelectedClaims([1, 4])}
          disabled={updating}
          className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded disabled:opacity-50"
        >
          Reset to Default
        </button>
        <button
          onClick={handleSave}
          disabled={updating}
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded disabled:opacity-50"
        >
          {updating ? 'Updating...' : 'Save Claims Configuration'}
        </button>
      </div>
    </div>
  );
}
