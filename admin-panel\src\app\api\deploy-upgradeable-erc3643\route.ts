import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { prisma } from '../../../lib/prisma';

const execAsync = promisify(exec);

// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL ✅)
const WORKING_ERC3643_CONTRACTS = {
  workingUnderlyingRegistry: "0x1281a057881cbe94dc2a79561275aa6b35bf7854",
  workingWrapper: "0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02",
  workingTokenExample: "0x3F3e2E88542D26C22B7539b5B42328AA3e9DD303"
};

// POST /api/deploy-upgradeable-erc3643 - Deploy upgradeable ERC-3643 token
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'symbol', 'decimals', 'maxSupply', 'adminAddress'];
    for (const field of requiredFields) {
      if (body[field] === undefined || body[field] === null) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    const {
      name,
      symbol,
      decimals,
      maxSupply,
      adminAddress,
      network = 'amoy',
      upgradeTimelock = 30 // days
    } = body;

    console.log('🚀 DEPLOYING UPGRADEABLE ERC-3643 TOKEN');
    console.log(`📋 Name: ${name}`);
    console.log(`🏷️ Symbol: ${symbol}`);
    console.log(`👑 Admin: ${adminAddress}`);
    console.log(`⏰ Upgrade Timelock: ${upgradeTimelock} days`);

    // Create deployment script with environment variables
    const deploymentScript = `
const { ethers, upgrades } = require('hardhat');

async function deployUpgradeableToken() {
  console.log('🚀 DEPLOYING UPGRADEABLE ERC-3643 TOKEN');
  
  try {
    const [deployer] = await ethers.getSigners();
    console.log('👤 Deployer:', deployer.address);
    
    // Token configuration
    const tokenConfig = {
      name: '${name}',
      symbol: '${symbol}',
      decimals: ${decimals},
      maxSupply: ethers.parseUnits('${maxSupply}', ${decimals}),
      admin: '${adminAddress}',
      identityRegistry: '${WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry}',
      compliance: ethers.ZeroAddress,
      version: '1.0.0'
    };

    console.log('📦 Deploying upgradeable contract...');
    
    const UpgradeableERC3643Token = await ethers.getContractFactory('UpgradeableERC3643Token');
    
    const token = await upgrades.deployProxy(
      UpgradeableERC3643Token,
      [
        tokenConfig.name,
        tokenConfig.symbol,
        tokenConfig.decimals,
        tokenConfig.maxSupply,
        tokenConfig.admin,
        tokenConfig.identityRegistry,
        tokenConfig.compliance,
        tokenConfig.version
      ],
      {
        initializer: 'initialize',
        kind: 'uups'
      }
    );

    await token.waitForDeployment();
    const proxyAddress = await token.getAddress();
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    
    console.log('✅ Proxy deployed:', proxyAddress);
    console.log('✅ Implementation deployed:', implementationAddress);
    
    // Verify functionality
    const isVerified = await token.isVerified('${adminAddress}');
    const upgradesRenounced = await token.upgradesRenounced();
    const upgradeTimelock = await token.upgradeTimelock();
    
    console.log('🔍 Admin verified:', isVerified);
    console.log('🔒 Upgrades renounced:', upgradesRenounced);
    console.log('⏰ Upgrade timelock:', upgradeTimelock / 86400n, 'days');
    
    // Output JSON for parsing
    console.log('DEPLOYMENT_RESULT:', JSON.stringify({
      success: true,
      proxyAddress: proxyAddress,
      implementationAddress: implementationAddress,
      adminVerified: isVerified,
      upgradesRenounced: upgradesRenounced,
      upgradeTimelock: upgradeTimelock.toString(),
      identityRegistry: tokenConfig.identityRegistry,
      compliance: tokenConfig.compliance
    }));
    
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    console.log('DEPLOYMENT_RESULT:', JSON.stringify({
      success: false,
      error: error.message
    }));
  }
}

deployUpgradeableToken().catch(console.error);
    `;

    // Write the deployment script to a temporary file
    const fs = require('fs').promises;
    const tempScriptPath = path.join(process.cwd(), '..', 'Token', 'temp-deploy-upgradeable.js');
    await fs.writeFile(tempScriptPath, deploymentScript);

    console.log('📝 Created temporary deployment script');

    // Execute the deployment
    const tokenDir = path.join(process.cwd(), '..', 'Token');
    console.log('🔄 Executing deployment...');

    try {
      const { stdout, stderr } = await execAsync(
        'npx hardhat run temp-deploy-upgradeable.js --network amoy --config hardhat.fresh.config.js',
        { 
          cwd: tokenDir,
          timeout: 300000 // 5 minutes timeout
        }
      );

      console.log('📊 Deployment output:', stdout);
      if (stderr) {
        console.log('⚠️ Deployment stderr:', stderr);
      }

      // Parse the deployment result
      const resultMatch = stdout.match(/DEPLOYMENT_RESULT: (.+)/);
      let deploymentResult = { success: false, error: 'Could not parse deployment result' };
      
      if (resultMatch) {
        try {
          deploymentResult = JSON.parse(resultMatch[1]);
        } catch (parseError) {
          console.error('Failed to parse deployment result:', parseError);
        }
      }

      // Clean up temporary file
      try {
        await fs.unlink(tempScriptPath);
      } catch (cleanupError) {
        console.log('⚠️ Could not clean up temp file:', cleanupError.message);
      }

      if (deploymentResult.success) {
        // Save the upgradeable deployment to database
        try {
          const savedToken = await prisma.token.create({
            data: {
              name: name,
              symbol: symbol,
              decimals: parseInt(decimals),
              maxSupply: maxSupply,
              address: deploymentResult.proxyAddress,
              adminAddress: adminAddress,
              network: network,
              tokenType: 'security',
              tokenPrice: '1.00 USD',
              currency: 'USD',
              hasKYC: true,
              isActive: true,
              deployedBy: adminAddress,
              selectedClaims: body.selectedClaims ? body.selectedClaims.join(',') : '1,4', // Default: KYC + Qualification
              deploymentNotes: `UPGRADEABLE ERC-3643 token deployed. Proxy: ${deploymentResult.proxyAddress}, Implementation: ${deploymentResult.implementationAddress}, Registry: ${deploymentResult.identityRegistry}, Upgrade Timelock: ${parseInt(deploymentResult.upgradeTimelock) / 86400} days, Upgrades Renounced: ${deploymentResult.upgradesRenounced}. Required Claims: ${body.selectedClaims ? body.selectedClaims.join(',') : '1,4'}`
            }
          });

          console.log('✅ Upgradeable token saved to database');

          return NextResponse.json({
            success: true,
            message: '🎉 Upgradeable ERC-3643 Token deployed successfully!',
            proxyAddress: deploymentResult.proxyAddress,
            implementationAddress: deploymentResult.implementationAddress,
            adminVerified: deploymentResult.adminVerified,
            upgradeInfo: {
              upgradesRenounced: deploymentResult.upgradesRenounced,
              upgradeTimelock: `${parseInt(deploymentResult.upgradeTimelock) / 86400} days`,
              canUpgrade: !deploymentResult.upgradesRenounced
            },
            databaseSaved: true,
            contracts: {
              proxy: deploymentResult.proxyAddress,
              implementation: deploymentResult.implementationAddress,
              identityRegistry: deploymentResult.identityRegistry,
              compliance: deploymentResult.compliance
            },
            tokenData: {
              name: name,
              symbol: symbol,
              decimals: decimals,
              maxSupply: maxSupply,
              adminAddress: adminAddress,
              network: network
            },
            explorerUrls: {
              proxy: `https://amoy.polygonscan.com/address/${deploymentResult.proxyAddress}`,
              implementation: `https://amoy.polygonscan.com/address/${deploymentResult.implementationAddress}`,
              identityRegistry: `https://amoy.polygonscan.com/address/${deploymentResult.identityRegistry}`
            },
            features: [
              '✅ Real deployed upgradeable ERC-3643 contract',
              '✅ UUPS proxy pattern for gas efficiency',
              '✅ Identity verification working',
              '✅ Admin properly verified',
              '✅ Optional upgrade renunciation',
              `✅ ${parseInt(deploymentResult.upgradeTimelock) / 86400}-day upgrade timelock`,
              '✅ Force transfer and freezing capabilities',
              '✅ Role-based access control',
              '✅ Institutional-grade security'
            ],
            upgradeManagement: {
              proposeUpgrade: 'Call proposeUpgrade(address) with UPGRADER_ROLE',
              executeUpgrade: 'Call executeUpgrade() after timelock expires',
              renounceUpgrades: 'Call renounceUpgrades() to permanently disable (IRREVERSIBLE)',
              increaseTimelock: 'Call increaseTimelock(uint256) to increase security'
            }
          });

        } catch (dbError: any) {
          console.error('❌ Database save failed:', dbError);
          
          return NextResponse.json({
            success: true, // Deployment succeeded
            message: '⚠️ Upgradeable token deployed but database save failed',
            proxyAddress: deploymentResult.proxyAddress,
            implementationAddress: deploymentResult.implementationAddress,
            adminVerified: deploymentResult.adminVerified,
            databaseSaved: false,
            error: dbError.message
          });
        }

      } else {
        return NextResponse.json({
          success: false,
          error: 'Upgradeable token deployment failed',
          details: deploymentResult.error,
          deploymentOutput: stdout,
          deploymentErrors: stderr
        }, { status: 500 });
      }

    } catch (execError: any) {
      console.error('❌ Deployment execution failed:', execError);
      
      // Clean up temporary file
      try {
        await fs.unlink(tempScriptPath);
      } catch (cleanupError) {
        console.log('⚠️ Could not clean up temp file:', cleanupError.message);
      }

      return NextResponse.json({
        success: false,
        error: 'Failed to execute upgradeable deployment',
        details: execError.message
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Upgradeable ERC-3643 deployment failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to deploy upgradeable ERC-3643 token',
      details: error.message
    }, { status: 500 });
  }
}
