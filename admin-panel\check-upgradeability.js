const { ethers } = require('ethers');

const TOKEN_ADDRESS = '******************************************';
const RPC_URL = 'https://rpc-amoy.polygon.technology/';

async function checkUpgradeability() {
  console.log('🔍 DETAILED UPGRADEABILITY INVESTIGATION');
  console.log('='.repeat(80));
  console.log(`📍 Token Address: ${TOKEN_ADDRESS}`);
  console.log('');

  try {
    const provider = new ethers.JsonRpcProvider(RPC_URL);

    console.log('🔍 STEP 1: CHECKING IF THIS IS A PROXY CONTRACT');
    console.log('-'.repeat(60));

    // Check if this address has contract code
    const contractCode = await provider.getCode(TOKEN_ADDRESS);
    console.log(`📝 Contract Code Length: ${contractCode.length} characters`);
    console.log(`📝 Has Contract Code: ${contractCode !== '0x' ? 'Yes' : 'No'}`);

    if (contractCode === '0x') {
      console.log('❌ No contract code found - this is not a deployed contract');
      return;
    }

    // Check for proxy patterns by looking at storage slots
    console.log('');
    console.log('🔍 STEP 2: CHECKING PROXY STORAGE PATTERNS');
    console.log('-'.repeat(60));

    // EIP-1967 standard storage slots
    const IMPLEMENTATION_SLOT = '0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc';
    const ADMIN_SLOT = '0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103';
    const BEACON_SLOT = '0xa3f0ad74e5423aebfd80d3ef4346578335a9a72aeaee59ff6cb3582b35133d50';

    try {
      const implementationSlot = await provider.getStorage(TOKEN_ADDRESS, IMPLEMENTATION_SLOT);
      const adminSlot = await provider.getStorage(TOKEN_ADDRESS, ADMIN_SLOT);
      const beaconSlot = await provider.getStorage(TOKEN_ADDRESS, BEACON_SLOT);

      console.log(`🔧 Implementation Slot (EIP-1967): ${implementationSlot}`);
      console.log(`👑 Admin Slot (EIP-1967): ${adminSlot}`);
      console.log(`📡 Beacon Slot (EIP-1967): ${beaconSlot}`);

      // Check if implementation slot has a non-zero value
      const hasImplementation = implementationSlot !== '0x0000000000000000000000000000000000000000000000000000000000000000';
      const hasAdmin = adminSlot !== '0x0000000000000000000000000000000000000000000000000000000000000000';
      const hasBeacon = beaconSlot !== '0x0000000000000000000000000000000000000000000000000000000000000000';

      console.log(`✅ Has Implementation: ${hasImplementation}`);
      console.log(`✅ Has Admin: ${hasAdmin}`);
      console.log(`✅ Has Beacon: ${hasBeacon}`);

      if (hasImplementation) {
        // Extract implementation address from storage
        const implementationAddress = '0x' + implementationSlot.slice(-40);
        console.log(`🎯 Implementation Address: ${implementationAddress}`);
        
        // Check if implementation address has code
        const implCode = await provider.getCode(implementationAddress);
        console.log(`📝 Implementation Has Code: ${implCode !== '0x' ? 'Yes' : 'No'}`);
      }

      if (hasAdmin) {
        // Extract admin address from storage
        const adminAddress = '0x' + adminSlot.slice(-40);
        console.log(`👑 Proxy Admin Address: ${adminAddress}`);
      }

    } catch (e) {
      console.log('❌ Error reading proxy storage slots:', e.message);
    }

    console.log('');
    console.log('🔍 STEP 3: TESTING PROXY INTERFACE FUNCTIONS');
    console.log('-'.repeat(60));

    // Test different proxy interface functions
    const PROXY_ABI = [
      // Transparent Proxy
      "function implementation() external view returns (address)",
      "function admin() external view returns (address)",
      "function changeAdmin(address newAdmin) external",
      "function upgradeTo(address newImplementation) external",
      "function upgradeToAndCall(address newImplementation, bytes calldata data) external payable",
      
      // UUPS Proxy
      "function proxiableUUID() external view returns (bytes32)",
      "function upgradeTo(address newImplementation) external",
      "function upgradeToAndCall(address newImplementation, bytes calldata data) external payable",
      
      // Beacon Proxy
      "function beacon() external view returns (address)",
      
      // OpenZeppelin specific
      "function getImplementation() external view returns (address)",
      "function getAdmin() external view returns (address)"
    ];

    const proxy = new ethers.Contract(TOKEN_ADDRESS, PROXY_ABI, provider);

    // Test Transparent Proxy functions
    console.log('🔍 Testing Transparent Proxy Functions:');
    try {
      const implementation = await proxy.implementation();
      console.log(`✅ implementation(): ${implementation}`);
    } catch (e) {
      console.log(`❌ implementation(): Not available`);
    }

    try {
      const admin = await proxy.admin();
      console.log(`✅ admin(): ${admin}`);
    } catch (e) {
      console.log(`❌ admin(): Not available`);
    }

    try {
      const getImplementation = await proxy.getImplementation();
      console.log(`✅ getImplementation(): ${getImplementation}`);
    } catch (e) {
      console.log(`❌ getImplementation(): Not available`);
    }

    try {
      const getAdmin = await proxy.getAdmin();
      console.log(`✅ getAdmin(): ${getAdmin}`);
    } catch (e) {
      console.log(`❌ getAdmin(): Not available`);
    }

    // Test UUPS Proxy functions
    console.log('');
    console.log('🔍 Testing UUPS Proxy Functions:');
    try {
      const proxiableUUID = await proxy.proxiableUUID();
      console.log(`✅ proxiableUUID(): ${proxiableUUID}`);
    } catch (e) {
      console.log(`❌ proxiableUUID(): Not available`);
    }

    // Test Beacon Proxy functions
    console.log('');
    console.log('🔍 Testing Beacon Proxy Functions:');
    try {
      const beacon = await proxy.beacon();
      console.log(`✅ beacon(): ${beacon}`);
    } catch (e) {
      console.log(`❌ beacon(): Not available`);
    }

    console.log('');
    console.log('🔍 STEP 4: CHECKING FOR UPGRADE AUTHORIZATION FUNCTIONS');
    console.log('-'.repeat(60));

    const UPGRADE_AUTH_ABI = [
      "function _authorizeUpgrade(address newImplementation) external view",
      "function hasRole(bytes32 role, address account) external view returns (bool)",
      "function DEFAULT_ADMIN_ROLE() external view returns (bytes32)",
      "function owner() external view returns (address)"
    ];

    const authContract = new ethers.Contract(TOKEN_ADDRESS, UPGRADE_AUTH_ABI, provider);

    try {
      const defaultAdminRole = await authContract.DEFAULT_ADMIN_ROLE();
      console.log(`👑 DEFAULT_ADMIN_ROLE: ${defaultAdminRole}`);
      
      // Check if a test address has admin role
      const testAdmin = '******************************************';
      const hasAdminRole = await authContract.hasRole(defaultAdminRole, testAdmin);
      console.log(`🔍 Test address has admin role: ${hasAdminRole}`);
    } catch (e) {
      console.log(`❌ Role-based access control: Not available`);
    }

    try {
      const owner = await authContract.owner();
      console.log(`👑 Owner (Ownable): ${owner}`);
    } catch (e) {
      console.log(`❌ Ownable pattern: Not available`);
    }

    console.log('');
    console.log('🔍 STEP 5: ANALYZING CONTRACT BYTECODE FOR PROXY PATTERNS');
    console.log('-'.repeat(60));

    // Look for common proxy bytecode patterns
    const bytecode = contractCode.toLowerCase();
    
    // Common proxy bytecode signatures
    const proxyPatterns = {
      'delegatecall': bytecode.includes('f4'), // DELEGATECALL opcode
      'sload_implementation': bytecode.includes('54'), // SLOAD opcode (for reading implementation)
      'minimal_proxy': bytecode.includes('3d602d80600a3d3981f3363d3d373d3d3d363d73'), // EIP-1167 minimal proxy
      'transparent_proxy': bytecode.includes('7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc'), // Implementation slot
      'uups_proxy': bytecode.includes('52d1902d'), // UUPS identifier
    };

    console.log('🔍 Bytecode Analysis:');
    for (const [pattern, found] of Object.entries(proxyPatterns)) {
      console.log(`   ${found ? '✅' : '❌'} ${pattern}: ${found ? 'Found' : 'Not found'}`);
    }

    console.log('');
    console.log('📋 FINAL UPGRADEABILITY ANALYSIS');
    console.log('='.repeat(80));

    // Determine if this is actually upgradeable
    const isProxy = hasImplementation || hasAdmin || hasBeacon;
    const hasProxyFunctions = false; // We'll determine this from the tests above
    const hasUpgradeAuth = false; // We'll determine this from the tests above

    console.log('🎯 UPGRADEABILITY VERDICT:');
    console.log('');

    if (isProxy) {
      console.log('✅ THIS IS A PROXY CONTRACT');
      console.log('✅ Contract is upgradeable through proxy pattern');
      
      if (hasImplementation) {
        console.log('📋 Proxy Type: Transparent or UUPS Proxy');
      }
      if (hasBeacon) {
        console.log('📋 Proxy Type: Beacon Proxy');
      }
      
      console.log('');
      console.log('💡 WHAT UPGRADEABILITY MEANS:');
      console.log('   • The contract logic can be changed by deploying a new implementation');
      console.log('   • The contract address and storage remain the same');
      console.log('   • Only authorized addresses (admin/owner) can perform upgrades');
      console.log('   • Users continue to interact with the same address');
      console.log('   • Token balances and state are preserved during upgrades');
      
    } else {
      console.log('❌ THIS IS NOT A PROXY CONTRACT');
      console.log('❌ Contract is NOT upgradeable');
      console.log('');
      console.log('💡 WHAT THIS MEANS:');
      console.log('   • The contract logic is immutable and cannot be changed');
      console.log('   • This is a direct deployment, not a proxy');
      console.log('   • Any changes would require deploying a new contract');
      console.log('   • This provides maximum security but no flexibility');
    }

    console.log('');
    console.log('🔗 Learn more about proxy patterns:');
    console.log('   • EIP-1967: https://eips.ethereum.org/EIPS/eip-1967');
    console.log('   • OpenZeppelin Proxies: https://docs.openzeppelin.com/contracts/4.x/proxies');

  } catch (error) {
    console.error('❌ Upgradeability investigation failed:', error);
  }
}

// Run the investigation
checkUpgradeability().catch(console.error);
