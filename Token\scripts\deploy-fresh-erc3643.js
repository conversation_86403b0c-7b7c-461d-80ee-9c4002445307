const { ethers, upgrades } = require('hardhat');

// Configuration
const WORKING_UNDERLYING_REGISTRY = '******************************************';
const TOKEN_CONFIG = {
  name: 'Fresh ERC3643 Token',
  symbol: 'FRESH3643',
  decimals: 18,
  maxSupply: ethers.parseUnits('1000000', 18) // 1 million tokens
};

async function deployFreshERC3643() {
  console.log('🚀 DEPLOYING FRESH ERC-3643 SYSTEM');
  console.log('='.repeat(80));
  console.log('');

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Deployer Balance: ${ethers.formatEther(await ethers.provider.getBalance(deployer.address))} MATIC`);
    console.log(`🆔 Working Underlying Registry: ${WORKING_UNDERLYING_REGISTRY}`);
    console.log('');

    // STEP 1: Verify admin is verified in the working registry
    console.log('🔍 STEP 1: VERIFYING ADMIN IN WORKING REGISTRY');
    console.log('-'.repeat(60));
    
    const workingRegistryABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const workingRegistry = new ethers.Contract(
      WORKING_UNDERLYING_REGISTRY, 
      workingRegistryABI, 
      deployer
    );
    
    const adminVerified = await workingRegistry.isVerified(deployer.address);
    console.log(`✅ Admin verified in working registry: ${adminVerified}`);
    
    if (!adminVerified) {
      console.log('❌ DEPLOYMENT STOPPED: Admin is not verified in the working registry');
      console.log('💡 Please register the admin in the Identity Registry first');
      return;
    }
    console.log('');

    // STEP 2: Deploy Working Identity Registry Wrapper
    console.log('🔧 STEP 2: DEPLOYING WORKING IDENTITY REGISTRY WRAPPER');
    console.log('-'.repeat(60));
    
    const WorkingIdentityRegistryWrapper = await ethers.getContractFactory('WorkingIdentityRegistryWrapper');
    
    console.log('📝 Deploying wrapper proxy...');
    const workingWrapper = await upgrades.deployProxy(
      WorkingIdentityRegistryWrapper,
      [
        WORKING_UNDERLYING_REGISTRY, // underlying registry
        deployer.address // admin
      ],
      { 
        initializer: 'initialize', 
        kind: 'uups',
        timeout: 120000 // 2 minutes timeout
      }
    );
    
    await workingWrapper.waitForDeployment();
    const workingWrapperAddress = await workingWrapper.getAddress();
    
    console.log(`✅ Working Wrapper deployed: ${workingWrapperAddress}`);
    
    // Verify wrapper sees admin as verified
    const wrapperVerified = await workingWrapper.isVerified(deployer.address);
    console.log(`🔍 Wrapper sees admin as verified: ${wrapperVerified}`);
    
    if (!wrapperVerified) {
      console.log('❌ WARNING: Wrapper doesn\'t see admin as verified');
      console.log('This might cause issues with token functionality');
    }
    console.log('');

    // STEP 3: Deploy Fresh ERC-3643 Token
    console.log('🪙 STEP 3: DEPLOYING FRESH ERC-3643 TOKEN');
    console.log('-'.repeat(60));
    
    const FreshERC3643Token = await ethers.getContractFactory('FreshERC3643Token');
    
    console.log('📝 Deploying token proxy...');
    console.log(`   Name: ${TOKEN_CONFIG.name}`);
    console.log(`   Symbol: ${TOKEN_CONFIG.symbol}`);
    console.log(`   Decimals: ${TOKEN_CONFIG.decimals}`);
    console.log(`   Max Supply: ${ethers.formatUnits(TOKEN_CONFIG.maxSupply, TOKEN_CONFIG.decimals)}`);
    
    const freshToken = await upgrades.deployProxy(
      FreshERC3643Token,
      [
        TOKEN_CONFIG.name,
        TOKEN_CONFIG.symbol,
        TOKEN_CONFIG.decimals,
        TOKEN_CONFIG.maxSupply,
        workingWrapperAddress, // use our working wrapper
        deployer.address // admin
      ],
      { 
        initializer: 'initialize', 
        kind: 'uups',
        timeout: 120000 // 2 minutes timeout
      }
    );
    
    await freshToken.waitForDeployment();
    const freshTokenAddress = await freshToken.getAddress();
    
    console.log(`✅ Fresh Token deployed: ${freshTokenAddress}`);
    console.log('');

    // STEP 4: Verify Token Functionality
    console.log('🧪 STEP 4: VERIFYING TOKEN FUNCTIONALITY');
    console.log('-'.repeat(60));
    
    // Check token info
    const tokenName = await freshToken.name();
    const tokenSymbol = await freshToken.symbol();
    const tokenDecimals = await freshToken.decimals();
    const tokenMaxSupply = await freshToken.maxSupply();
    const tokenIdentityRegistry = await freshToken.identityRegistry();
    
    console.log(`📋 Token Name: ${tokenName}`);
    console.log(`🏷️ Token Symbol: ${tokenSymbol}`);
    console.log(`🔢 Token Decimals: ${tokenDecimals}`);
    console.log(`📊 Max Supply: ${ethers.formatUnits(tokenMaxSupply, tokenDecimals)}`);
    console.log(`🆔 Identity Registry: ${tokenIdentityRegistry}`);
    
    // Check if admin is verified via token
    const tokenVerified = await freshToken.isVerified(deployer.address);
    console.log(`✅ Token sees admin as verified: ${tokenVerified}`);
    
    if (!tokenVerified) {
      console.log('❌ CRITICAL: Token doesn\'t see admin as verified');
      console.log('This will prevent minting and other operations');
      return;
    }
    console.log('');

    // STEP 5: Test Minting
    console.log('💰 STEP 5: TESTING TOKEN MINTING');
    console.log('-'.repeat(60));
    
    const mintAmount = ethers.parseUnits('1000', TOKEN_CONFIG.decimals);
    console.log(`🔄 Attempting to mint ${ethers.formatUnits(mintAmount, TOKEN_CONFIG.decimals)} tokens...`);
    
    const mintTx = await freshToken.mint(deployer.address, mintAmount);
    console.log(`⏳ Mint transaction submitted: ${mintTx.hash}`);
    
    await mintTx.wait();
    console.log(`✅ Mint transaction confirmed!`);
    
    const balance = await freshToken.balanceOf(deployer.address);
    console.log(`💰 Admin balance: ${ethers.formatUnits(balance, TOKEN_CONFIG.decimals)} ${tokenSymbol}`);
    console.log('');

    // STEP 6: Test Additional Functionality
    console.log('🔧 STEP 6: TESTING ADDITIONAL FUNCTIONALITY');
    console.log('-'.repeat(60));
    
    // Test available balance (should equal total balance since nothing is frozen)
    const availableBalance = await freshToken.availableBalanceOf(deployer.address);
    console.log(`💳 Available balance: ${ethers.formatUnits(availableBalance, TOKEN_CONFIG.decimals)} ${tokenSymbol}`);
    
    const frozenBalance = await freshToken.frozenBalanceOf(deployer.address);
    console.log(`🧊 Frozen balance: ${ethers.formatUnits(frozenBalance, TOKEN_CONFIG.decimals)} ${tokenSymbol}`);
    
    // Test agent status
    const isAgent = await freshToken.isAgent(deployer.address);
    console.log(`🕵️ Admin is agent: ${isAgent}`);
    
    // Test pause functionality
    console.log('⏸️ Testing pause functionality...');
    const pauseTx = await freshToken.pause();
    await pauseTx.wait();
    console.log('✅ Token paused successfully');
    
    const unpauseTx = await freshToken.unpause();
    await unpauseTx.wait();
    console.log('▶️ Token unpaused successfully');
    console.log('');

    // STEP 7: Final Summary
    console.log('🎉 DEPLOYMENT COMPLETE!');
    console.log('='.repeat(80));
    console.log('');
    console.log('📊 DEPLOYMENT SUMMARY:');
    console.log(`🆔 Working Identity Registry Wrapper: ${workingWrapperAddress}`);
    console.log(`🪙 Fresh ERC-3643 Token: ${freshTokenAddress}`);
    console.log(`👤 Admin Address: ${deployer.address}`);
    console.log(`✅ Admin Verified: ${tokenVerified}`);
    console.log(`💰 Initial Balance: ${ethers.formatUnits(balance, TOKEN_CONFIG.decimals)} ${tokenSymbol}`);
    console.log('');
    console.log('🎯 TOKEN STATUS: FULLY FUNCTIONAL!');
    console.log('✅ Identity verification working');
    console.log('✅ Minting successful');
    console.log('✅ All institutional features available');
    console.log('✅ Ready for production use');
    console.log('');
    console.log('🚀 NEXT STEPS:');
    console.log('1. Register additional users in the Identity Registry');
    console.log('2. Test transfers between verified users');
    console.log('3. Configure additional agents if needed');
    console.log('4. Set up monitoring and operational procedures');
    console.log('');
    console.log('🎉 SUCCESS! Your ERC-3643 token is ready for institutional use!');

    return {
      workingWrapper: workingWrapperAddress,
      freshToken: freshTokenAddress,
      adminVerified: tokenVerified,
      initialBalance: ethers.formatUnits(balance, TOKEN_CONFIG.decimals)
    };

  } catch (error) {
    console.error('❌ DEPLOYMENT FAILED:', error);
    
    if (error.message.includes('recipient not verified')) {
      console.log('');
      console.log('🔍 DIAGNOSIS: Admin verification issue');
      console.log('The token deployment succeeded but admin verification failed');
      console.log('This suggests an issue with the Identity Registry connection');
    } else if (error.message.includes('execution reverted')) {
      console.log('');
      console.log('🔍 DIAGNOSIS: Contract execution error');
      console.log('Check the contract logic and initialization parameters');
    }
    
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  deployFreshERC3643()
    .then((result) => {
      console.log('✅ Deployment completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Deployment failed');
      process.exit(1);
    });
}

module.exports = { deployFreshERC3643 };
