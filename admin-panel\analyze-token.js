const { ethers } = require('ethers');

const TOKEN_ADDRESS = '******************************************';
const RPC_URL = 'https://rpc-amoy.polygon.technology/';

async function analyzeToken() {
  console.log('🔍 ANALYZING TOKEN ON BLOCKCHAIN');
  console.log('='.repeat(80));
  console.log(`📍 Token Address: ${TOKEN_ADDRESS}`);
  console.log(`🌐 Network: Polygon Amoy Testnet`);
  console.log('');

  try {
    const provider = new ethers.JsonRpcProvider(RPC_URL);

    // Basic ERC-20 ABI
    const ERC20_ABI = [
      "function name() external view returns (string memory)",
      "function symbol() external view returns (string memory)",
      "function decimals() external view returns (uint8)",
      "function totalSupply() external view returns (uint256)",
      "function balanceOf(address account) external view returns (uint256)",
      "function owner() external view returns (address)",
      "function paused() external view returns (bool)"
    ];

    // ERC-3643 specific ABI
    const ERC3643_ABI = [
      "function identityRegistry() external view returns (address)",
      "function compliance() external view returns (address)",
      "function isVerified(address user) external view returns (bool)",
      "function onchainID() external view returns (address)",
      "function version() external view returns (string memory)"
    ];

    // Custody/Security features ABI
    const SECURITY_ABI = [
      "function forcedTransfer(address from, address to, uint256 amount) external returns (bool)",
      "function mint(address to, uint256 amount) external",
      "function burn(address from, uint256 amount) external",
      "function freeze(address account) external",
      "function unfreeze(address account) external",
      "function isFrozen(address account) external view returns (bool)",
      "function freezeTokens(address account, uint256 amount) external",
      "function unfreezeTokens(address account, uint256 amount) external",
      "function frozenBalanceOf(address account) external view returns (uint256)",
      "function availableBalanceOf(address account) external view returns (uint256)",
      "function pause() external",
      "function unpause() external"
    ];

    // Access Control ABI
    const ACCESS_CONTROL_ABI = [
      "function hasRole(bytes32 role, address account) external view returns (bool)",
      "function DEFAULT_ADMIN_ROLE() external view returns (bytes32)",
      "function AGENT_ROLE() external view returns (bytes32)",
      "function getRoleAdmin(bytes32 role) external view returns (bytes32)",
      "function grantRole(bytes32 role, address account) external",
      "function revokeRole(bytes32 role, address account) external"
    ];

    // Upgradeable ABI
    const UPGRADEABLE_ABI = [
      "function implementation() external view returns (address)",
      "function admin() external view returns (address)",
      "function proxiableUUID() external view returns (bytes32)"
    ];

    console.log('📊 BASIC TOKEN INFORMATION');
    console.log('-'.repeat(60));

    // Test basic ERC-20 functions
    const token = new ethers.Contract(TOKEN_ADDRESS, ERC20_ABI, provider);
    
    try {
      const name = await token.name();
      console.log(`📋 Name: ${name}`);
    } catch (e) {
      console.log('📋 Name: ❌ Not available');
    }

    try {
      const symbol = await token.symbol();
      console.log(`🏷️ Symbol: ${symbol}`);
    } catch (e) {
      console.log('🏷️ Symbol: ❌ Not available');
    }

    try {
      const decimals = await token.decimals();
      console.log(`🔢 Decimals: ${decimals}`);
    } catch (e) {
      console.log('🔢 Decimals: ❌ Not available');
    }

    try {
      const totalSupply = await token.totalSupply();
      console.log(`📊 Total Supply: ${ethers.formatUnits(totalSupply, 18)}`);
    } catch (e) {
      console.log('📊 Total Supply: ❌ Not available');
    }

    try {
      const owner = await token.owner();
      console.log(`👑 Owner: ${owner}`);
    } catch (e) {
      console.log('👑 Owner: ❌ Not available or not Ownable');
    }

    try {
      const paused = await token.paused();
      console.log(`⏸️ Paused: ${paused ? 'Yes' : 'No'}`);
    } catch (e) {
      console.log('⏸️ Pausable: ❌ Not implemented');
    }

    console.log('');
    console.log('🏛️ ERC-3643 COMPLIANCE CHECK');
    console.log('-'.repeat(60));

    // Test ERC-3643 specific functions
    const erc3643Token = new ethers.Contract(TOKEN_ADDRESS, ERC3643_ABI, provider);
    let isERC3643 = true;
    let erc3643Features = {};

    try {
      const identityRegistry = await erc3643Token.identityRegistry();
      console.log(`🆔 Identity Registry: ${identityRegistry}`);
      erc3643Features.identityRegistry = identityRegistry;
    } catch (e) {
      console.log('🆔 Identity Registry: ❌ Not available');
      isERC3643 = false;
    }

    try {
      const compliance = await erc3643Token.compliance();
      console.log(`⚖️ Compliance Module: ${compliance}`);
      erc3643Features.compliance = compliance;
    } catch (e) {
      console.log('⚖️ Compliance Module: ❌ Not available');
      isERC3643 = false;
    }

    try {
      const onchainID = await erc3643Token.onchainID();
      console.log(`🔗 OnchainID: ${onchainID}`);
      erc3643Features.onchainID = onchainID;
    } catch (e) {
      console.log('🔗 OnchainID: ❌ Not available');
    }

    try {
      const version = await erc3643Token.version();
      console.log(`📦 Version: ${version}`);
      erc3643Features.version = version;
    } catch (e) {
      console.log('📦 Version: ❌ Not available');
    }

    // Test verification function
    const testAddress = '0x56f3726C92B8B92a6ab71983886F91718540d888';
    try {
      const isVerified = await erc3643Token.isVerified(testAddress);
      console.log(`✅ Verification Check (${testAddress.substring(0,10)}...): ${isVerified ? 'Verified' : 'Not Verified'}`);
      erc3643Features.hasVerification = true;
    } catch (e) {
      console.log('✅ Verification Function: ❌ Not available');
    }

    console.log('');
    console.log('🔒 SECURITY & CUSTODY FEATURES');
    console.log('-'.repeat(60));

    const securityToken = new ethers.Contract(TOKEN_ADDRESS, SECURITY_ABI, provider);
    let securityFeatures = {};

    // Test force transfer
    try {
      // Just check if the function exists by calling staticCall
      await provider.call({
        to: TOKEN_ADDRESS,
        data: securityToken.interface.encodeFunctionData('forcedTransfer', [ethers.ZeroAddress, ethers.ZeroAddress, 0])
      });
      console.log('🚨 Force Transfer: ✅ Available');
      securityFeatures.forcedTransfer = true;
    } catch (e) {
      if (e.message.includes('function selector was not recognized')) {
        console.log('🚨 Force Transfer: ❌ Not available');
      } else {
        console.log('🚨 Force Transfer: ✅ Available (function exists)');
        securityFeatures.forcedTransfer = true;
      }
    }

    // Test freezing functions
    try {
      const frozenBalance = await securityToken.frozenBalanceOf(testAddress);
      console.log(`🧊 Frozen Balance Check: ✅ Available (${ethers.formatUnits(frozenBalance, 18)})`);
      securityFeatures.partialFreezing = true;
    } catch (e) {
      console.log('🧊 Partial Freezing: ❌ Not available');
    }

    try {
      const availableBalance = await securityToken.availableBalanceOf(testAddress);
      console.log(`💳 Available Balance Check: ✅ Available (${ethers.formatUnits(availableBalance, 18)})`);
      securityFeatures.availableBalance = true;
    } catch (e) {
      console.log('💳 Available Balance: ❌ Not available');
    }

    try {
      const isFrozen = await securityToken.isFrozen(testAddress);
      console.log(`❄️ Account Freezing: ✅ Available (${testAddress.substring(0,10)}... is ${isFrozen ? 'frozen' : 'not frozen'})`);
      securityFeatures.accountFreezing = true;
    } catch (e) {
      console.log('❄️ Account Freezing: ❌ Not available');
    }

    console.log('');
    console.log('🔐 ACCESS CONTROL & ROLES');
    console.log('-'.repeat(60));

    const accessToken = new ethers.Contract(TOKEN_ADDRESS, ACCESS_CONTROL_ABI, provider);
    let accessFeatures = {};

    try {
      const defaultAdminRole = await accessToken.DEFAULT_ADMIN_ROLE();
      console.log(`👑 Default Admin Role: ✅ Available (${defaultAdminRole})`);
      accessFeatures.roleBasedAccess = true;
      
      // Check if test address has admin role
      const hasAdminRole = await accessToken.hasRole(defaultAdminRole, testAddress);
      console.log(`🔍 Test Address Admin: ${hasAdminRole ? 'Yes' : 'No'}`);
    } catch (e) {
      console.log('👑 Role-Based Access: ❌ Not available');
    }

    try {
      const agentRole = await accessToken.AGENT_ROLE();
      console.log(`🕵️ Agent Role: ✅ Available (${agentRole})`);
      accessFeatures.agentRole = true;
    } catch (e) {
      console.log('🕵️ Agent Role: ❌ Not available');
    }

    console.log('');
    console.log('🔄 UPGRADEABILITY CHECK');
    console.log('-'.repeat(60));

    const upgradeToken = new ethers.Contract(TOKEN_ADDRESS, UPGRADEABLE_ABI, provider);
    let upgradeFeatures = {};

    try {
      const implementation = await upgradeToken.implementation();
      console.log(`🔧 Implementation: ✅ Upgradeable (${implementation})`);
      upgradeFeatures.upgradeable = true;
    } catch (e) {
      console.log('🔧 Upgradeability: ❌ Not upgradeable or different pattern');
    }

    try {
      const proxyUUID = await upgradeToken.proxiableUUID();
      console.log(`🆔 Proxy UUID: ✅ UUPS Pattern (${proxyUUID})`);
      upgradeFeatures.uupsPattern = true;
    } catch (e) {
      console.log('🆔 UUPS Pattern: ❌ Not UUPS or not upgradeable');
    }

    console.log('');
    console.log('📋 FINAL ANALYSIS SUMMARY');
    console.log('='.repeat(80));

    console.log(`🏛️ ERC-3643 Compliant: ${isERC3643 ? '✅ YES' : '❌ NO'}`);
    
    if (isERC3643) {
      console.log('   ✅ Has Identity Registry');
      console.log('   ✅ Has Compliance Module');
      if (erc3643Features.onchainID && erc3643Features.onchainID !== ethers.ZeroAddress) {
        console.log('   ✅ Has OnchainID');
      } else {
        console.log('   ⚠️ OnchainID not set or zero address');
      }
      if (erc3643Features.hasVerification) {
        console.log('   ✅ Has Verification Function');
      }
    }

    console.log('');
    console.log('🔒 Security Features:');
    if (securityFeatures.forcedTransfer) console.log('   ✅ Force Transfer (Custody)');
    if (securityFeatures.partialFreezing) console.log('   ✅ Partial Token Freezing');
    if (securityFeatures.availableBalance) console.log('   ✅ Available Balance Tracking');
    if (securityFeatures.accountFreezing) console.log('   ✅ Account Freezing');
    if (securityFeatures.roleBasedAccess) console.log('   ✅ Role-Based Access Control');
    if (securityFeatures.agentRole) console.log('   ✅ Agent Role System');

    console.log('');
    console.log('🔄 Architecture:');
    if (upgradeFeatures.upgradeable) console.log('   ✅ Upgradeable Contract');
    if (upgradeFeatures.uupsPattern) console.log('   ✅ UUPS Proxy Pattern');

    console.log('');
    console.log('🎯 INSTITUTIONAL READINESS:');
    const institutionalScore = Object.keys(securityFeatures).length + Object.keys(accessFeatures).length;
    if (isERC3643 && institutionalScore >= 4) {
      console.log('   🏆 EXCELLENT - Fully institutional ready');
    } else if (isERC3643 && institutionalScore >= 2) {
      console.log('   ✅ GOOD - Suitable for institutional use');
    } else if (isERC3643) {
      console.log('   ⚠️ BASIC - ERC-3643 compliant but limited features');
    } else {
      console.log('   ❌ NOT SUITABLE - Not ERC-3643 compliant');
    }

    console.log('');
    console.log('🔗 Blockchain Explorer:');
    console.log(`   https://amoy.polygonscan.com/address/${TOKEN_ADDRESS}`);

  } catch (error) {
    console.error('❌ Analysis failed:', error);
  }
}

// Run the analysis
analyzeToken().catch(console.error);
