{"_format": "hh-sol-artifact-1", "contractName": "Address", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220f0a467a10c04721c0fddc955e88d253117cad803d69d1e92bde08fd95759797764736f6c63430008160033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220f0a467a10c04721c0fddc955e88d253117cad803d69d1e92bde08fd95759797764736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}