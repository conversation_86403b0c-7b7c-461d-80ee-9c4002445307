'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { safeContractCall, safeOptionalContractCall, safeContractTransaction, FallbackProvider } from '@/utils/fallbackProvider';
import SecurityTokenCoreABI from '@/contracts/SecurityTokenCore.json';
import AgentsList from '@/components/AgentsList';

// Default token address from environment (fallback)
const DEFAULT_SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_SECURITY_TOKEN_CORE_ADDRESS || '******************************************';

interface TokenInfo {
  name: string;
  symbol: string;
  version: string;
  totalSupply: bigint;
  maxSupply: bigint;
  decimals: number;
  paused: boolean;
  metadata: {
    tokenPrice: bigint;
    currency: string;
    bonusTiers: string;
  };
}

interface WhitelistedClient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  walletAddress: string;
  kycStatus: string;
  isWhitelisted: boolean;
  whitelistedAt: string | null;
  tokenApproval?: {
    id: string;
    approvalStatus: string;
    whitelistApproved: boolean;
    kycApproved: boolean;
    approvedAt: string | null;
    approvedBy: string | null;
    notes: string | null;
  };
}

function ReliableTokensContent() {
  const searchParams = useSearchParams();
  const urlTokenAddress = searchParams.get('token');

  // Use URL parameter if provided, otherwise fall back to environment variable
  const SECURITY_TOKEN_CORE_ADDRESS = urlTokenAddress || DEFAULT_SECURITY_TOKEN_CORE_ADDRESS;

  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [walletConnected, setWalletConnected] = useState(false);
  const [pauseFunctionAvailable, setPauseFunctionAvailable] = useState(false);

  // Tab management
  const [activeTab, setActiveTab] = useState<'management' | 'whitelist'>('management');

  // Whitelist management
  const [whitelistedClients, setWhitelistedClients] = useState<WhitelistedClient[]>([]);
  const [whitelistLoading, setWhitelistLoading] = useState(false);
  const [whitelistError, setWhitelistError] = useState<string | null>(null);
  const [tokenFromDb, setTokenFromDb] = useState<any>(null);

  // ERC-3643 wrapper detection
  const knownERC3643Wrappers = [
    '******************************************',
    '******************************************',
    '******************************************'
  ];

  const knownUnderlyingToken = '******************************************';
  const isERC3643Wrapper = knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS);
  const underlyingTokenAddress = isERC3643Wrapper ? knownUnderlyingToken : SECURITY_TOKEN_CORE_ADDRESS;
  const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;

  // Debug logging
  console.log('🔍 ReliableTokensContent rendered');
  console.log('🔍 URL token parameter:', urlTokenAddress);
  console.log('🔍 Using token address:', SECURITY_TOKEN_CORE_ADDRESS);
  console.log('🔍 Default address:', DEFAULT_SECURITY_TOKEN_CORE_ADDRESS);
  console.log('🛡️ Is ERC-3643 wrapper:', isERC3643Wrapper);
  console.log('🔗 Data address for metadata calls:', dataAddress);

  // Load token information using fallback-first approach
  const loadTokenInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Loading token info with fallback-first approach...');

      // Check if this is an ERC-3643 wrapper
      const knownERC3643Wrappers = [
        '******************************************',
        '******************************************',
        '******************************************'
      ];

      const knownUnderlyingToken = '******************************************';

      let isERC3643Wrapper = false;
      let underlyingTokenAddress = SECURITY_TOKEN_CORE_ADDRESS;

      if (knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS)) {
        console.log('🛡️ Detected known ERC-3643 wrapper address');
        isERC3643Wrapper = true;
        underlyingTokenAddress = knownUnderlyingToken;
        console.log('🔗 Using known underlying token for data calls:', underlyingTokenAddress);
      }

      // For ERC-3643 wrappers, get basic info from wrapper but maxSupply/metadata from underlying
      const basicInfoAddress = SECURITY_TOKEN_CORE_ADDRESS;
      const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;

      console.log('📊 Getting basic info from:', basicInfoAddress);
      console.log('📊 Getting data (maxSupply, metadata) from:', dataAddress);

      // Use safe contract calls that automatically handle fallbacks
      // Core functions that should always exist
      const [name, symbol, version, totalSupply, decimals] = await Promise.all([
        safeContractCall<string>(basicInfoAddress, SecurityTokenCoreABI.abi, 'name'),
        safeContractCall<string>(basicInfoAddress, SecurityTokenCoreABI.abi, 'symbol'),
        safeContractCall<string>(basicInfoAddress, SecurityTokenCoreABI.abi, 'version'),
        safeContractCall<bigint>(basicInfoAddress, SecurityTokenCoreABI.abi, 'totalSupply'),
        safeContractCall<number>(basicInfoAddress, SecurityTokenCoreABI.abi, 'decimals')
      ]);

      // Optional functions that may not exist on all contracts
      const paused = await safeOptionalContractCall<boolean>(
        basicInfoAddress,
        SecurityTokenCoreABI.abi,
        'paused',
        false // fallback value
      );

      // Check if pause function is available by trying to call it
      let pauseAvailable = false;
      try {
        await safeContractCall<boolean>(basicInfoAddress, SecurityTokenCoreABI.abi, 'paused');
        pauseAvailable = true;
        console.log('✅ paused() function available');
      } catch (error) {
        pauseAvailable = false;
        console.log('⚠️ paused() function not available');
      }

      setPauseFunctionAvailable(pauseAvailable);

      // Get maxSupply and metadata from the appropriate address
      let maxSupply: bigint;
      let metadata: any;

      try {
        maxSupply = await safeContractCall<bigint>(dataAddress, SecurityTokenCoreABI.abi, 'maxSupply');
        console.log('✅ maxSupply loaded from:', dataAddress);
      } catch (error) {
        console.log('⚠️ maxSupply failed, using 0:', error);
        maxSupply = BigInt(0);
      }

      try {
        metadata = await safeContractCall<any>(dataAddress, SecurityTokenCoreABI.abi, 'getTokenMetadata');
        console.log('✅ metadata loaded from:', dataAddress);
      } catch (error) {
        console.log('⚠️ metadata failed, using defaults:', error);
        metadata = {
          tokenPrice: '0',
          currency: 'USD',
          bonusTiers: ''
        };
      }

      setTokenInfo({
        name,
        symbol,
        version,
        totalSupply,
        maxSupply,
        decimals,
        paused,
        metadata: {
          tokenPrice: metadata.tokenPrice || '0',
          currency: metadata.currency || 'USD',
          bonusTiers: metadata.bonusTiers || ''
        }
      });

      console.log('✅ Token info loaded successfully');
      
    } catch (error: any) {
      console.error('❌ Failed to load token info:', error);
      setError(`Failed to load token information: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Connect wallet
  const connectWallet = async () => {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        await window.ethereum.request({ method: 'eth_requestAccounts' });
        setWalletConnected(true);
        setSuccess('Wallet connected successfully');
      } else {
        setError('MetaMask not found. Please install MetaMask to use this feature.');
      }
    } catch (error: any) {
      setError(`Failed to connect wallet: ${error.message}`);
    }
  };

  // Toggle pause state using fallback-first approach
  const togglePause = async () => {
    if (!walletConnected) {
      setError('Please connect your wallet first');
      return;
    }

    try {
      setActionLoading(true);
      setError(null);
      setSuccess(null);

      const currentPaused = tokenInfo?.paused;
      const isPausing = !currentPaused;
      const functionName = isPausing ? 'pause' : 'unpause';
      
      console.log(`🔄 ${isPausing ? 'Pausing' : 'Unpausing'} token...`);

      // Get real-time state to ensure accuracy (if paused function is available)
      const realTimePaused = await safeOptionalContractCall<boolean>(
        SECURITY_TOKEN_CORE_ADDRESS,
        SecurityTokenCoreABI.abi,
        'paused',
        currentPaused || false // fallback to current state
      );
      console.log('✅ Real-time paused state:', realTimePaused);

      // Validate state change is needed
      if (isPausing && realTimePaused) {
        throw new Error('Token is already paused. Refreshing page data...');
      }
      if (!isPausing && !realTimePaused) {
        throw new Error('Token is already unpaused. Refreshing page data...');
      }

      // Execute the transaction using safe transaction approach
      const tx = await safeContractTransaction(
        SECURITY_TOKEN_CORE_ADDRESS,
        SecurityTokenCoreABI.abi,
        functionName,
        []
      );

      console.log(`✅ ${functionName} transaction sent: ${tx.hash}`);
      
      // Wait for confirmation
      const receipt = await tx.wait();
      console.log(`✅ ${functionName} confirmed in block ${receipt?.blockNumber}`);

      setSuccess(`Token ${isPausing ? 'paused' : 'unpaused'} successfully!`);
      
      // Refresh token info
      await loadTokenInfo();

    } catch (error: any) {
      console.error(`❌ Toggle pause failed:`, error);
      
      if (error.message?.includes('already paused') || error.message?.includes('already unpaused')) {
        setError(`${error.message}`);
        // Auto-refresh data after state conflict
        setTimeout(async () => {
          await loadTokenInfo();
          setError(null);
        }, 2000);
      } else {
        setError(`Failed to ${tokenInfo?.paused ? 'unpause' : 'pause'} token: ${error.message}`);
      }
    } finally {
      setActionLoading(false);
    }
  };

  // Mint tokens using fallback-first approach
  const mintTokens = async (amount: string, recipient: string) => {
    if (!walletConnected) {
      setError('Please connect your wallet first');
      return;
    }

    try {
      setActionLoading(true);
      setError(null);
      setSuccess(null);

      console.log(`🔄 Minting ${amount} tokens to ${recipient}...`);

      // 🚨 CRITICAL SECURITY CHECK: Verify recipient is whitelisted before minting
      console.log('🔒 SECURITY CHECK: Verifying recipient is whitelisted...');
      try {
        const isWhitelisted = await safeContractCall<boolean>(
          SECURITY_TOKEN_CORE_ADDRESS,
          SecurityTokenCoreABI.abi,
          'isWhitelisted',
          [recipient]
        );

        if (!isWhitelisted) {
          throw new Error(`SECURITY VIOLATION: Recipient address ${recipient} is not whitelisted. Only whitelisted addresses can receive tokens for ERC-3643 compliance.`);
        }

        console.log('✅ SECURITY PASSED: Recipient is whitelisted, proceeding with mint');

      } catch (whitelistError: any) {
        console.error('❌ SECURITY CHECK FAILED:', whitelistError);
        throw new Error(`Security validation failed: ${whitelistError.message}`);
      }

      // Convert amount based on decimals
      const decimals = tokenInfo?.decimals || 0;
      const mintAmount = decimals > 0
        ? BigInt(amount) * (10n ** BigInt(decimals))
        : BigInt(amount);

      // Execute mint transaction
      const tx = await safeContractTransaction(
        SECURITY_TOKEN_CORE_ADDRESS,
        SecurityTokenCoreABI.abi,
        'mint',
        [recipient, mintAmount]
      );

      console.log(`✅ Mint transaction sent: ${tx.hash}`);
      
      const receipt = await tx.wait();
      console.log(`✅ Mint confirmed in block ${receipt?.blockNumber}`);

      setSuccess(`Successfully minted ${amount} tokens to ${recipient}!`);
      
      // Refresh token info
      await loadTokenInfo();

    } catch (error: any) {
      console.error(`❌ Mint failed:`, error);
      setError(`Failed to mint tokens: ${error.message}`);
    } finally {
      setActionLoading(false);
    }
  };

  // Load token from database to get the ID for whitelist queries
  const loadTokenFromDatabase = async () => {
    if (!SECURITY_TOKEN_CORE_ADDRESS) return;

    try {
      const response = await fetch('/api/tokens');
      if (response.ok) {
        const data = await response.json();
        // Handle new API response format
        const tokens = data.success && Array.isArray(data.tokens) ? data.tokens :
                       Array.isArray(data) ? data : [];

        // Find token by address
        const token = tokens.find((t: any) =>
          t.address.toLowerCase() === SECURITY_TOKEN_CORE_ADDRESS.toLowerCase()
        );

        if (token) {
          setTokenFromDb(token);
          console.log('✅ Token found in database:', token);
        } else {
          console.log('⚠️ Token not found in database, checking all available tokens:', tokens.map((t: any) => t.address));
        }
      }
    } catch (error) {
      console.log('⚠️ Error loading token from database:', error);
      setWhitelistError('Failed to load token from database');
    }
  };

  // Load whitelisted clients for this token
  const loadWhitelistedClients = async () => {
    if (!tokenFromDb?.id) {
      setWhitelistError('Token not found in database');
      return;
    }

    setWhitelistLoading(true);
    setWhitelistError(null);

    try {
      console.log('🔍 Loading whitelisted clients for token ID:', tokenFromDb.id);

      const response = await fetch(`/api/tokens?whitelistedInvestors=true&tokenId=${tokenFromDb.id}`);

      console.log('📥 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📊 API Response:', data);

      if (data.success) {
        setWhitelistedClients(data.investors || []);
        console.log('✅ Loaded whitelisted clients:', data.investors?.length || 0);
      } else {
        throw new Error(data.error || 'Failed to load whitelisted clients');
      }

    } catch (err: any) {
      console.error('❌ Error loading whitelisted clients:', err);
      setWhitelistError(err.message || 'Failed to load whitelisted clients');
      setWhitelistedClients([]); // Clear the list on error
    } finally {
      setWhitelistLoading(false);
    }
  };

  // Remove client from whitelist
  const handleRemoveFromWhitelist = async (clientId: string) => {
    if (!tokenFromDb?.id) return;

    if (!confirm('Are you sure you want to remove this client from the whitelist?')) {
      return;
    }

    try {
      const response = await fetch(`/api/clients/${clientId}/token-approval`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenId: tokenFromDb.id,
          whitelistApproved: false,
          approvalStatus: 'REJECTED',
          notes: 'Removed from whitelist by admin'
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to remove from whitelist');
      }

      // Refresh the clients list
      loadWhitelistedClients();

      setSuccess('Client removed from whitelist successfully');

    } catch (err: any) {
      console.error('Error removing from whitelist:', err);
      setError(`Error: ${err.message}`);
    }
  };

  // Load token info on component mount
  useEffect(() => {
    loadTokenInfo();
    loadTokenFromDatabase();
  }, []);

  // Load whitelist when token from DB is available and whitelist tab is active
  useEffect(() => {
    if (tokenFromDb && activeTab === 'whitelist') {
      loadWhitelistedClients();
    }
  }, [tokenFromDb, activeTab]);

  // Check wallet connection on mount
  useEffect(() => {
    if (typeof window !== 'undefined' && window.ethereum) {
      window.ethereum.request({ method: 'eth_accounts' })
        .then((accounts: string[]) => {
          if (accounts.length > 0) {
            setWalletConnected(true);
          }
        })
        .catch(console.error);
    }
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Reliable Token Management
          </h1>
          <p className="text-gray-600">
            Bulletproof token management with fallback-first architecture - no more network errors!
          </p>
        </div>

        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold mb-2">Connection Status</h2>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-2 ${walletConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm">
                    Wallet: {walletConnected ? 'Connected' : 'Not Connected'}
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                  <span className="text-sm">Network: Reliable RPC Active</span>
                </div>
              </div>
            </div>
            {!walletConnected && (
              <button
                onClick={connectWallet}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                Connect Wallet
              </button>
            )}
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-red-600 mr-2">⚠️</span>
                <span>{error}</span>
              </div>
              <button
                onClick={() => setError(null)}
                className="text-red-600 hover:text-red-800 text-xl"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <span>{success}</span>
              </div>
              <button
                onClick={() => setSuccess(null)}
                className="text-green-600 hover:text-green-800 text-xl"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('management')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'management'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                🛡️ Token Management
              </button>
              <button
                onClick={() => setActiveTab('whitelist')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'whitelist'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                👥 Whitelisted Clients
                {whitelistedClients.length > 0 && (
                  <span className="ml-2 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">
                    {whitelistedClients.length}
                  </span>
                )}
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'management' && (
          <>
            {/* Token Information */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Token Information</h2>
            <button
              onClick={loadTokenInfo}
              disabled={loading}
              className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading token information...</p>
            </div>
          ) : tokenInfo ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <p className="text-lg font-semibold">{tokenInfo.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Symbol</label>
                <p className="text-lg font-semibold">{tokenInfo.symbol}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Version</label>
                <p className="text-lg">{tokenInfo.version}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Decimals</label>
                <p className="text-lg">{tokenInfo.decimals}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Total Supply</label>
                <p className="text-lg">{tokenInfo.totalSupply?.toString() || '0'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Supply</label>
                <p className="text-lg">{tokenInfo.maxSupply?.toString() || '0'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  tokenInfo.paused 
                    ? 'bg-red-100 text-red-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {tokenInfo.paused ? '⏸️ Paused' : '▶️ Active'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Token Price</label>
                <p className="text-lg">${tokenInfo.metadata?.tokenPrice?.toString() || '0'}.00 {tokenInfo.metadata?.currency || 'USD'}</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">No token information available</p>
          )}
        </div>

        {/* Token Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Pause/Unpause - Only show if function is available */}
          {pauseFunctionAvailable && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Pause Control</h3>
              <p className="text-gray-600 mb-4">
                {tokenInfo?.paused
                  ? 'Token is currently paused. Unpause to allow transfers.'
                  : 'Token is currently active. Pause to stop all transfers.'}
              </p>
              <button
                onClick={togglePause}
                disabled={actionLoading || !tokenInfo}
                className={`w-full px-4 py-2 rounded-lg font-medium disabled:opacity-50 ${
                  tokenInfo?.paused
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
              {actionLoading
                ? 'Processing...'
                : tokenInfo?.paused
                  ? '▶️ Unpause Token'
                  : '⏸️ Pause Token'}
            </button>
          </div>
          )}

          {/* Mint Tokens */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Mint Tokens</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Recipient Address
                </label>
                <input
                  type="text"
                  id="mintRecipient"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0x..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount
                </label>
                <input
                  type="number"
                  id="mintAmount"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="100"
                />
              </div>
              <button
                onClick={() => {
                  const recipient = (document.getElementById('mintRecipient') as HTMLInputElement)?.value;
                  const amount = (document.getElementById('mintAmount') as HTMLInputElement)?.value;
                  if (recipient && amount) {
                    mintTokens(amount, recipient);
                  } else {
                    setError('Please enter both recipient address and amount');
                  }
                }}
                disabled={actionLoading || !tokenInfo}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
              >
                {actionLoading ? 'Processing...' : '🪙 Mint Tokens'}
              </button>
            </div>
          </div>

          {/* Whitelist Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Whitelist Management</h3>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <input
                    type="text"
                    id="whitelistAddress"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0x..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <select
                    id="whitelistCountry"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    defaultValue="US"
                  >
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="GB">United Kingdom</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                    <option value="JP">Japan</option>
                    <option value="AU">Australia</option>
                    <option value="SG">Singapore</option>
                    <option value="CH">Switzerland</option>
                    <option value="NL">Netherlands</option>
                    <option value="SE">Sweden</option>
                    <option value="NO">Norway</option>
                    <option value="DK">Denmark</option>
                    <option value="FI">Finland</option>
                    <option value="IT">Italy</option>
                    <option value="ES">Spain</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={async () => {
                    const address = (document.getElementById('whitelistAddress') as HTMLInputElement)?.value;
                    const country = (document.getElementById('whitelistCountry') as HTMLInputElement)?.value || 'US';

                    if (address) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        console.log('🔄 Adding to whitelist via ERC-3643 compliant API...');

                        const response = await fetch('/api/admin/add-to-whitelist', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                          body: JSON.stringify({
                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,
                            userAddress: address,
                            country: country,
                          }),
                        });

                        const result = await response.json();

                        if (!response.ok) {
                          throw new Error(result.error || 'API request failed');
                        }

                        // Display detailed results
                        let successMessage = `✅ ${result.message}\n`;
                        if (result.results) {
                          const { results } = result;
                          if (results.identityCreated) successMessage += '🏭 Identity contract created\n';
                          if (results.identityRegistered) successMessage += '📝 Identity registered\n';
                          if (results.whitelisted) successMessage += '📋 Added to whitelist\n';
                          if (results.txHashes.length > 0) {
                            successMessage += `📄 Transactions: ${results.txHashes.length}\n`;
                          }
                          if (results.errors.length > 0) {
                            successMessage += `⚠️ Warnings: ${results.errors.join(', ')}\n`;
                          }
                        }

                        setSuccess(successMessage);
                        console.log('✅ ERC-3643 whitelist process completed:', result);

                        // Refresh whitelist data
                        await loadWhitelistedClients();

                      } catch (error: any) {
                        console.error('❌ Whitelist add failed:', error);
                        setError(`Failed to add to whitelist: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter an address');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50"
                >
                  ✅ Add with ERC-3643 Compliance
                </button>
                <button
                  onClick={async () => {
                    const address = (document.getElementById('whitelistAddress') as HTMLInputElement)?.value;
                    if (address) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        console.log('🔄 Removing from whitelist via API...');

                        const response = await fetch('/api/admin/remove-from-whitelist', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                          body: JSON.stringify({
                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,
                            userAddress: address,
                          }),
                        });

                        const result = await response.json();

                        if (!response.ok) {
                          throw new Error(result.error || 'API request failed');
                        }

                        setSuccess(`✅ API Success: ${result.message} (Tx: ${result.txHash})`);
                        console.log('✅ Whitelist remove successful:', result);

                      } catch (error: any) {
                        console.error('❌ Whitelist remove failed:', error);
                        setError(`Failed to remove from whitelist: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter an address');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50"
                >
                  ❌ Remove (API)
                </button>
              </div>
              <button
                onClick={async () => {
                  const address = (document.getElementById('whitelistAddress') as HTMLInputElement)?.value;
                  if (address) {
                    try {
                      setActionLoading(true);
                      setError(null);
                      setSuccess(null);

                      // Validate address format to prevent ENS resolution issues
                      if (!address.startsWith('0x') || address.length !== 42) {
                        throw new Error('Please enter a valid Ethereum address (0x... format)');
                      }

                      const isWhitelisted = await safeContractCall<boolean>(
                        SECURITY_TOKEN_CORE_ADDRESS,
                        SecurityTokenCoreABI.abi,
                        'isWhitelisted',
                        [address]
                      );
                      setSuccess(`Address ${address} is ${isWhitelisted ? '✅ whitelisted' : '❌ not whitelisted'}`);
                    } catch (error: any) {
                      // Handle ENS-related errors specifically
                      if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {
                        setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');
                      } else {
                        setError(`Failed to check whitelist: ${error.message}`);
                      }
                    } finally {
                      setActionLoading(false);
                    }
                  } else {
                    setError('Please enter an address');
                  }
                }}
                disabled={actionLoading}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50"
              >
                🔍 Check Status
              </button>
            </div>
          </div>
        </div>

        {/* Additional Tools */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {/* Balance Checker */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Balance Checker</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address to Check
                </label>
                <input
                  type="text"
                  id="balanceAddress"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0x..."
                />
              </div>
              <button
                onClick={async () => {
                  const address = (document.getElementById('balanceAddress') as HTMLInputElement)?.value;
                  if (address) {
                    try {
                      setActionLoading(true);
                      setError(null);
                      setSuccess(null);

                      // Validate address format to prevent ENS resolution issues
                      if (!address.startsWith('0x') || address.length !== 42) {
                        throw new Error('Please enter a valid Ethereum address (0x... format)');
                      }

                      const balance = await safeContractCall<bigint>(
                        SECURITY_TOKEN_CORE_ADDRESS,
                        SecurityTokenCoreABI.abi,
                        'balanceOf',
                        [address]
                      );
                      const decimals = tokenInfo?.decimals || 0;
                      const formattedBalance = decimals > 0
                        ? (Number(balance) / Math.pow(10, decimals)).toString()
                        : balance?.toString() || '0';
                      setSuccess(`Balance: ${formattedBalance} ${tokenInfo?.symbol || 'tokens'}`);
                    } catch (error: any) {
                      // Handle ENS-related errors specifically
                      if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {
                        setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');
                      } else {
                        setError(`Failed to get balance: ${error.message}`);
                      }
                    } finally {
                      setActionLoading(false);
                    }
                  } else {
                    setError('Please enter an address');
                  }
                }}
                disabled={actionLoading}
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
              >
                💰 Check Balance
              </button>
            </div>
          </div>

          {/* Burn Tokens */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Burn Tokens</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount to Burn
                </label>
                <input
                  type="number"
                  id="burnAmount"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="100"
                />
              </div>
              <p className="text-sm text-gray-600">
                ⚠️ This will permanently destroy tokens from your wallet. This action cannot be undone.
              </p>
              <button
                onClick={async () => {
                  const amount = (document.getElementById('burnAmount') as HTMLInputElement)?.value;
                  if (amount) {
                    if (confirm(`Are you sure you want to burn ${amount} tokens? This action cannot be undone.`)) {
                      try {
                        setActionLoading(true);
                        const decimals = tokenInfo?.decimals || 0;
                        const burnAmount = decimals > 0
                          ? BigInt(amount) * (10n ** BigInt(decimals))
                          : BigInt(amount);

                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'burn',
                          [burnAmount]
                        );

                        await tx.wait();
                        setSuccess(`Successfully burned ${amount} tokens`);
                        await loadTokenInfo();
                      } catch (error: any) {
                        setError(`Failed to burn tokens: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    }
                  } else {
                    setError('Please enter an amount to burn');
                  }
                }}
                disabled={actionLoading}
                className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
              >
                🔥 Burn Tokens
              </button>
            </div>
          </div>

          {/* Agent Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Agent Management</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Agent Address
                  </label>
                  <input
                    type="text"
                    id="agentAddress"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0x..."
                  />
                </div>
                <div className="flex items-end space-x-2">
                  <button
                    onClick={async () => {
                      const address = (document.getElementById('agentAddress') as HTMLInputElement)?.value;
                      if (address) {
                        try {
                          setActionLoading(true);
                          setError(null);
                          setSuccess(null);

                          // Validate address format
                          if (!address.startsWith('0x') || address.length !== 42) {
                            throw new Error('Please enter a valid Ethereum address (0x... format)');
                          }

                          // Get AGENT_ROLE hash and use direct role management
                          const AGENT_ROLE = await safeContractCall<string>(
                            SECURITY_TOKEN_CORE_ADDRESS,
                            SecurityTokenCoreABI.abi,
                            'AGENT_ROLE',
                            []
                          );

                          const tx = await safeContractTransaction(
                            SECURITY_TOKEN_CORE_ADDRESS,
                            SecurityTokenCoreABI.abi,
                            'grantRole',
                            [AGENT_ROLE, address]
                          );

                          await tx.wait();
                          setSuccess(`Successfully added ${address} as agent!`);
                        } catch (error: any) {
                          setError(`Failed to add agent: ${error.message}`);
                        } finally {
                          setActionLoading(false);
                        }
                      } else {
                        setError('Please enter an agent address');
                      }
                    }}
                    disabled={actionLoading}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                  >
                    ➕ Add Agent
                  </button>
                  <button
                    onClick={async () => {
                      const address = (document.getElementById('agentAddress') as HTMLInputElement)?.value;
                      if (address) {
                        try {
                          setActionLoading(true);
                          setError(null);
                          setSuccess(null);

                          // Validate address format
                          if (!address.startsWith('0x') || address.length !== 42) {
                            throw new Error('Please enter a valid Ethereum address (0x... format)');
                          }

                          // Get AGENT_ROLE hash and use direct role management
                          const AGENT_ROLE = await safeContractCall<string>(
                            SECURITY_TOKEN_CORE_ADDRESS,
                            SecurityTokenCoreABI.abi,
                            'AGENT_ROLE',
                            []
                          );

                          const tx = await safeContractTransaction(
                            SECURITY_TOKEN_CORE_ADDRESS,
                            SecurityTokenCoreABI.abi,
                            'revokeRole',
                            [AGENT_ROLE, address]
                          );

                          await tx.wait();
                          setSuccess(`Successfully removed ${address} as agent!`);
                        } catch (error: any) {
                          setError(`Failed to remove agent: ${error.message}`);
                        } finally {
                          setActionLoading(false);
                        }
                      } else {
                        setError('Please enter an agent address');
                      }
                    }}
                    disabled={actionLoading}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                  >
                    ➖ Remove Agent
                  </button>
                </div>
              </div>
              <div className="mt-4">
                <button
                  onClick={async () => {
                    try {
                      setActionLoading(true);
                      setError(null);
                      setSuccess(null);

                      // Note: Since AGENT_MANAGER module is not registered, we can't enumerate agents
                      // Instead, we'll show how to check if specific addresses have AGENT_ROLE

                      const AGENT_ROLE = await safeContractCall<string>(
                        SECURITY_TOKEN_CORE_ADDRESS,
                        SecurityTokenCoreABI.abi,
                        'AGENT_ROLE',
                        []
                      );

                      // Check some common addresses for demonstration
                      const addressesToCheck = [
                        '0x56f3726C92B8B92a6ab71983886F91718540d888', // Your address
                        '0x1f1a17fe870f5A0EEf1274247c080478E3d0840A'  // Test address
                      ];

                      const agentResults: string[] = [];

                      for (const address of addressesToCheck) {
                        try {
                          const hasRole = await safeContractCall<boolean>(
                            SECURITY_TOKEN_CORE_ADDRESS,
                            SecurityTokenCoreABI.abi,
                            'hasRole',
                            [AGENT_ROLE, address]
                          );

                          if (hasRole) {
                            agentResults.push(`✅ ${address}`);
                          }
                        } catch (error) {
                          // Skip addresses that cause errors
                        }
                      }

                      if (agentResults.length === 0) {
                        setSuccess('No agents found in checked addresses. Use "Check Role" to verify specific addresses.');
                      } else {
                        setSuccess(`Agents found: ${agentResults.join(', ')}`);
                      }
                    } catch (error: any) {
                      setError(`Failed to get agents: ${error.message}`);
                    } finally {
                      setActionLoading(false);
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                >
                  👥 Check Known Agents
                </button>
              </div>

              {/* Agent List Display */}
              <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-3">📋 Current Agents List</h4>
                <AgentsList tokenAddress={SECURITY_TOKEN_CORE_ADDRESS} />
              </div>
            </div>
          </div>

          {/* Role Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Role Management</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <input
                    type="text"
                    id="roleAddress"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0x..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Role
                  </label>
                  <select
                    id="roleSelect"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="AGENT_ROLE">AGENT_ROLE</option>
                    <option value="TRANSFER_MANAGER_ROLE">TRANSFER_MANAGER_ROLE</option>
                    <option value="MODULE_MANAGER_ROLE">MODULE_MANAGER_ROLE</option>
                    <option value="DEFAULT_ADMIN_ROLE">DEFAULT_ADMIN_ROLE (⚠️ Dangerous)</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={async () => {
                    const address = (document.getElementById('roleAddress') as HTMLInputElement)?.value;
                    const roleSelect = (document.getElementById('roleSelect') as HTMLSelectElement)?.value;

                    if (address && roleSelect) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        // Validate address format
                        if (!address.startsWith('0x') || address.length !== 42) {
                          throw new Error('Please enter a valid Ethereum address (0x... format)');
                        }

                        // Get role hash
                        const roleHash = await safeContractCall<string>(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          roleSelect,
                          []
                        );

                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'grantRole',
                          [roleHash, address]
                        );

                        await tx.wait();
                        setSuccess(`Successfully granted ${roleSelect} to ${address}!`);
                      } catch (error: any) {
                        setError(`Failed to grant role: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter both address and select a role');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                >
                  ✅ Grant Role
                </button>
                <button
                  onClick={async () => {
                    const address = (document.getElementById('roleAddress') as HTMLInputElement)?.value;
                    const roleSelect = (document.getElementById('roleSelect') as HTMLSelectElement)?.value;

                    if (address && roleSelect) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        // Validate address format
                        if (!address.startsWith('0x') || address.length !== 42) {
                          throw new Error('Please enter a valid Ethereum address (0x... format)');
                        }

                        // Get role hash
                        const roleHash = await safeContractCall<string>(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          roleSelect,
                          []
                        );

                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'revokeRole',
                          [roleHash, address]
                        );

                        await tx.wait();
                        setSuccess(`Successfully revoked ${roleSelect} from ${address}!`);
                      } catch (error: any) {
                        setError(`Failed to revoke role: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter both address and select a role');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                >
                  ❌ Revoke Role
                </button>
                <button
                  onClick={async () => {
                    const address = (document.getElementById('roleAddress') as HTMLInputElement)?.value;
                    const roleSelect = (document.getElementById('roleSelect') as HTMLSelectElement)?.value;

                    if (address && roleSelect) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        // Validate address format
                        if (!address.startsWith('0x') || address.length !== 42) {
                          throw new Error('Please enter a valid Ethereum address (0x... format)');
                        }

                        // Get role hash
                        const roleHash = await safeContractCall<string>(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          roleSelect,
                          []
                        );

                        const hasRole = await safeContractCall<boolean>(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'hasRole',
                          [roleHash, address]
                        );

                        setSuccess(`Address ${address} ${hasRole ? '✅ HAS' : '❌ DOES NOT HAVE'} ${roleSelect}`);
                      } catch (error: any) {
                        setError(`Failed to check role: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter both address and select a role');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                >
                  🔍 Check Role
                </button>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-sm text-yellow-800">
                  <strong>⚠️ Role Descriptions:</strong><br/>
                  • <strong>AGENT_ROLE</strong>: Can perform basic token operations<br/>
                  • <strong>TRANSFER_MANAGER_ROLE</strong>: Can manage transfers and compliance<br/>
                  • <strong>MODULE_MANAGER_ROLE</strong>: Can register/unregister modules<br/>
                  • <strong>DEFAULT_ADMIN_ROLE</strong>: Full admin access (use with extreme caution)
                </p>
              </div>
            </div>
          </div>

          {/* Metadata Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Token Metadata</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Token Price
                  </label>
                  <input
                    type="text"
                    id="newTokenPrice"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="100.00 USD"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bonus Tiers
                  </label>
                  <input
                    type="text"
                    id="newBonusTiers"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Early: 20%, Standard: 10%"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Token Details/Type
                </label>
                <textarea
                  id="newTokenDetails"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Token type: Carbon Credits, Debt Securities, Real Estate, etc. Additional details..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Token Image URL
                </label>
                <input
                  type="url"
                  id="newTokenImageUrl"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://example.com/token-logo.png"
                />
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={async () => {
                    const price = (document.getElementById('newTokenPrice') as HTMLInputElement)?.value;
                    const tiers = (document.getElementById('newBonusTiers') as HTMLInputElement)?.value;
                    const details = (document.getElementById('newTokenDetails') as HTMLTextAreaElement)?.value;

                    if (price || tiers || details) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        // Get current values if not provided
                        const currentPrice = price || tokenInfo?.tokenPrice || '';
                        const currentTiers = tiers || tokenInfo?.bonusTiers || '';
                        const currentDetails = details || tokenInfo?.tokenDetails || '';

                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'updateTokenMetadata',
                          [currentPrice, currentTiers, currentDetails]
                        );

                        await tx.wait();
                        setSuccess('Successfully updated token metadata!');
                        await loadTokenInfo(); // Refresh token info
                      } catch (error: any) {
                        setError(`Failed to update metadata: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter at least one field to update');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                >
                  📝 Update Metadata
                </button>
                <button
                  onClick={async () => {
                    const imageUrl = (document.getElementById('newTokenImageUrl') as HTMLInputElement)?.value;

                    if (imageUrl) {
                      try {
                        setActionLoading(true);
                        setError(null);
                        setSuccess(null);

                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'updateTokenImageUrl',
                          [imageUrl]
                        );

                        await tx.wait();
                        setSuccess('Successfully updated token image!');
                        await loadTokenInfo(); // Refresh token info
                      } catch (error: any) {
                        setError(`Failed to update image: ${error.message}`);
                      } finally {
                        setActionLoading(false);
                      }
                    } else {
                      setError('Please enter an image URL');
                    }
                  }}
                  disabled={actionLoading}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                >
                  🖼️ Update Image
                </button>
              </div>
            </div>
          </div>

          {/* Update Token Price */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Quick Price Update</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current Price
                </label>
                <p className="text-lg font-semibold text-gray-900">
                  ${tokenInfo?.metadata?.tokenPrice?.toString() || '0'}.00 {tokenInfo?.metadata?.currency || 'USD'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  New Price (USD)
                </label>
                <input
                  type="number"
                  id="newTokenPrice"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="150"
                />
              </div>
              <button
                onClick={async () => {
                  const newPrice = (document.getElementById('newTokenPrice') as HTMLInputElement)?.value;
                  if (newPrice) {
                    try {
                      setActionLoading(true);

                      console.log('🔄 Updating token price using fallback-first approach...');

                      // Try direct updateTokenPrice first
                      try {
                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'updateTokenPrice',
                          [newPrice]
                        );

                        await tx.wait();
                        setSuccess(`Token price updated to $${newPrice}.00 USD`);

                      } catch (directError: any) {
                        console.log('Direct method failed, trying fallback...');

                        // Fallback to updateTokenMetadata
                        const currentMetadata = await safeContractCall<any[]>(
                          dataAddress,
                          SecurityTokenCoreABI.abi,
                          'getTokenMetadata'
                        );

                        const currentBonusTiers = currentMetadata[1];
                        const currentDetails = currentMetadata[2];

                        const tx = await safeContractTransaction(
                          SECURITY_TOKEN_CORE_ADDRESS,
                          SecurityTokenCoreABI.abi,
                          'updateTokenMetadata',
                          [newPrice, currentBonusTiers, currentDetails]
                        );

                        await tx.wait();
                        setSuccess(`Token price updated to $${newPrice}.00 USD (via fallback method)`);
                      }

                      // Refresh token info
                      await loadTokenInfo();

                    } catch (error: any) {
                      setError(`Failed to update token price: ${error.message}`);
                    } finally {
                      setActionLoading(false);
                    }
                  } else {
                    setError('Please enter a new price');
                  }
                }}
                disabled={actionLoading}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
              >
                💰 Update Price
              </button>
            </div>
          </div>
        </div>

        {/* Technical Info */}
        <div className="bg-gray-50 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-semibold mb-3">🛡️ Fallback-First Architecture</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">✅ Network Resilience</h4>
              <ul className="text-gray-600 space-y-1">
                <li>• Multiple RPC endpoints</li>
                <li>• Automatic failover</li>
                <li>• Connection testing</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">✅ Error Elimination</h4>
              <ul className="text-gray-600 space-y-1">
                <li>• No "missing revert data"</li>
                <li>• No "Internal JSON-RPC"</li>
                <li>• Reliable contract calls</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">✅ User Experience</h4>
              <ul className="text-gray-600 space-y-1">
                <li>• Seamless operation</li>
                <li>• Clear error messages</li>
                <li>• Automatic recovery</li>
              </ul>
            </div>
          </div>
        </div>
        </>
        )}

        {/* Whitelist Tab Content */}
        {activeTab === 'whitelist' && (
          <div className="space-y-6">
            {/* Whitelist Header */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">👥 Whitelisted Clients</h2>
                  <p className="text-gray-600 mt-1">
                    Manage clients who are approved to hold {tokenInfo?.name || 'this'} tokens
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">{whitelistedClients.length}</div>
                    <div className="text-sm text-gray-600">Whitelisted</div>
                  </div>
                  <Link
                    href={`/clients${tokenFromDb ? `?token=${tokenFromDb.id}` : ''}`}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
                  >
                    ➕ Add Clients
                  </Link>
                </div>
              </div>
            </div>

            {/* Whitelist Content */}
            <div className="bg-white rounded-lg shadow-md">
              {whitelistLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading whitelisted clients...</p>
                </div>
              ) : whitelistError ? (
                <div className="p-6">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <span className="text-red-600 text-xl">❌</span>
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-red-800">Error Loading Whitelist</h3>
                        <p className="text-red-700">{whitelistError}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : whitelistedClients.length === 0 ? (
                <div className="p-8 text-center">
                  <div className="text-gray-400 text-6xl mb-4">👥</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Whitelisted Clients</h3>
                  <p className="text-gray-600 mb-4">
                    No clients have been whitelisted for this token yet.
                  </p>
                  <Link
                    href={`/clients${tokenFromDb ? `?token=${tokenFromDb.id}` : ''}`}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
                  >
                    ➕ Add First Client
                  </Link>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Client
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Wallet Address
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          KYC Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Approved Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {whitelistedClients.map((client) => (
                        <tr key={client.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {client.firstName} {client.lastName}
                              </div>
                              <div className="text-sm text-gray-500">{client.email}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 font-mono">
                              {client.walletAddress}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              client.kycStatus === 'APPROVED'
                                ? 'bg-green-100 text-green-800'
                                : client.kycStatus === 'REJECTED'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {client.kycStatus}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {client.tokenApproval?.approvedAt
                              ? new Date(client.tokenApproval.approvedAt).toLocaleDateString()
                              : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => handleRemoveFromWhitelist(client.id)}
                              className="text-red-600 hover:text-red-900 mr-4"
                            >
                              Remove
                            </button>
                            <Link
                              href={`/clients/${client.id}`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              View Details
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function ReliableTokensPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading token management...</p>
      </div>
    </div>}>
      <ReliableTokensContent />
    </Suspense>
  );
}
