const { ethers } = require('ethers');

// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL)
const WORKING_ERC3643_CONTRACTS = {
  // Working underlying registry (verified functional)
  workingUnderlyingRegistry: "******************************************",
  
  // Fresh working wrapper (just deployed and verified)
  workingWrapper: "******************************************",
  
  // Example working token (for reference)
  workingTokenExample: "******************************************"
};

const RPC_URL = 'https://rpc-amoy.polygon.technology/';
const ADMIN_PRIVATE_KEY = '94692a030d151928530ddf7ae9f1ff07ad43d187b96135c8953fac16183619c1';
const ADMIN_ADDRESS = '******************************************';

async function testWorkingSystemFromAdmin() {
  console.log('🧪 TESTING WORKING ERC-3643 SYSTEM FROM ADMIN PANEL');
  console.log('='.repeat(80));
  console.log('');

  try {
    // Create provider and signer
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const adminWallet = new ethers.Wallet(ADMIN_PRIVATE_KEY, provider);

    console.log(`🔑 Admin: ${adminWallet.address}`);
    console.log(`💰 Admin Balance: ${ethers.formatEther(await provider.getBalance(adminWallet.address))} MATIC`);
    console.log('');

    // Test 1: Check underlying registry
    console.log('🔍 TEST 1: CHECKING UNDERLYING REGISTRY');
    console.log('-'.repeat(60));
    
    const underlyingRegistryABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const underlyingRegistry = new ethers.Contract(
      WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry,
      underlyingRegistryABI,
      adminWallet
    );
    
    const underlyingVerified = await underlyingRegistry.isVerified(ADMIN_ADDRESS);
    console.log(`✅ Admin verified in underlying registry: ${underlyingVerified}`);
    console.log('');

    // Test 2: Check working wrapper
    console.log('🔍 TEST 2: CHECKING WORKING WRAPPER');
    console.log('-'.repeat(60));
    
    const wrapperABI = [
      "function isVerified(address userAddress) external view returns (bool)",
      "function underlyingRegistry() external view returns (address)"
    ];
    
    const wrapper = new ethers.Contract(
      WORKING_ERC3643_CONTRACTS.workingWrapper,
      wrapperABI,
      adminWallet
    );
    
    const wrapperVerified = await wrapper.isVerified(ADMIN_ADDRESS);
    const wrapperUnderlying = await wrapper.underlyingRegistry();
    
    console.log(`✅ Admin verified in wrapper: ${wrapperVerified}`);
    console.log(`🔗 Wrapper's underlying registry: ${wrapperUnderlying}`);
    console.log(`🔍 Registry matches expected: ${wrapperUnderlying.toLowerCase() === WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry.toLowerCase()}`);
    console.log('');

    // Test 3: Check working token example
    console.log('🔍 TEST 3: CHECKING WORKING TOKEN EXAMPLE');
    console.log('-'.repeat(60));
    
    const tokenABI = [
      "function name() external view returns (string memory)",
      "function symbol() external view returns (string memory)",
      "function decimals() external view returns (uint8)",
      "function totalSupply() external view returns (uint256)",
      "function maxSupply() external view returns (uint256)",
      "function identityRegistry() external view returns (address)",
      "function isVerified(address user) external view returns (bool)",
      "function balanceOf(address account) external view returns (uint256)",
      "function availableBalanceOf(address account) external view returns (uint256)",
      "function frozenBalanceOf(address account) external view returns (uint256)"
    ];
    
    const token = new ethers.Contract(
      WORKING_ERC3643_CONTRACTS.workingTokenExample,
      tokenABI,
      adminWallet
    );
    
    const tokenName = await token.name();
    const tokenSymbol = await token.symbol();
    const tokenDecimals = await token.decimals();
    const tokenTotalSupply = await token.totalSupply();
    const tokenMaxSupply = await token.maxSupply();
    const tokenIdentityRegistry = await token.identityRegistry();
    const tokenVerified = await token.isVerified(ADMIN_ADDRESS);
    const adminBalance = await token.balanceOf(ADMIN_ADDRESS);
    const adminAvailableBalance = await token.availableBalanceOf(ADMIN_ADDRESS);
    const adminFrozenBalance = await token.frozenBalanceOf(ADMIN_ADDRESS);
    
    console.log(`📋 Token Name: ${tokenName}`);
    console.log(`🏷️ Token Symbol: ${tokenSymbol}`);
    console.log(`🔢 Token Decimals: ${tokenDecimals}`);
    console.log(`📊 Total Supply: ${ethers.formatUnits(tokenTotalSupply, tokenDecimals)}`);
    console.log(`📊 Max Supply: ${ethers.formatUnits(tokenMaxSupply, tokenDecimals)}`);
    console.log(`🆔 Token's Identity Registry: ${tokenIdentityRegistry}`);
    console.log(`✅ Token sees admin as verified: ${tokenVerified}`);
    console.log(`💰 Admin balance: ${ethers.formatUnits(adminBalance, tokenDecimals)} ${tokenSymbol}`);
    console.log(`💳 Admin available balance: ${ethers.formatUnits(adminAvailableBalance, tokenDecimals)} ${tokenSymbol}`);
    console.log(`🧊 Admin frozen balance: ${ethers.formatUnits(adminFrozenBalance, tokenDecimals)} ${tokenSymbol}`);
    console.log('');

    // Test 4: System health check
    const systemHealthy = underlyingVerified && wrapperVerified && tokenVerified;
    
    console.log('🎯 SYSTEM HEALTH CHECK');
    console.log('-'.repeat(60));
    console.log(`🔍 Underlying Registry: ${underlyingVerified ? '✅ WORKING' : '❌ BROKEN'}`);
    console.log(`🔍 Working Wrapper: ${wrapperVerified ? '✅ WORKING' : '❌ BROKEN'}`);
    console.log(`🔍 Working Token: ${tokenVerified ? '✅ WORKING' : '❌ BROKEN'}`);
    console.log(`🎯 Overall System: ${systemHealthy ? '✅ HEALTHY' : '❌ ISSUES DETECTED'}`);
    console.log('');

    if (systemHealthy) {
      console.log('🎉 SUCCESS! WORKING ERC-3643 SYSTEM IS FULLY FUNCTIONAL!');
      console.log('='.repeat(80));
      console.log('');
      console.log('📊 SYSTEM SUMMARY:');
      console.log(`🆔 Working Underlying Registry: ${WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry}`);
      console.log(`🔗 Working Wrapper: ${WORKING_ERC3643_CONTRACTS.workingWrapper}`);
      console.log(`🪙 Working Token Example: ${WORKING_ERC3643_CONTRACTS.workingTokenExample}`);
      console.log(`👤 Admin Address: ${ADMIN_ADDRESS}`);
      console.log(`✅ Admin Verified: ${tokenVerified}`);
      console.log(`💰 Admin Balance: ${ethers.formatUnits(adminBalance, tokenDecimals)} ${tokenSymbol}`);
      console.log('');
      console.log('🚀 READY FOR ADMIN PANEL INTEGRATION:');
      console.log('✅ All components are working');
      console.log('✅ Admin is properly verified');
      console.log('✅ Token operations are functional');
      console.log('✅ Can be integrated into admin panel');
      console.log('');
      console.log('💡 NEXT STEPS:');
      console.log('1. Update admin panel to use these working contracts');
      console.log('2. Create new token deployment using working wrapper');
      console.log('3. Test all admin panel features with working system');
      console.log('4. Deploy additional tokens as needed');
    } else {
      console.log('❌ SYSTEM HAS ISSUES');
      console.log('Please check individual component status above');
    }

    return {
      systemHealthy,
      contracts: WORKING_ERC3643_CONTRACTS,
      testResults: {
        underlyingRegistry: underlyingVerified,
        workingWrapper: wrapperVerified,
        workingToken: tokenVerified
      }
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Execute test
testWorkingSystemFromAdmin().catch(console.error);
