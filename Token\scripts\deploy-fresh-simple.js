const { ethers } = require('hardhat');

// Configuration
const WORKING_UNDERLYING_REGISTRY = '******************************************';
const ADMIN_ADDRESS = '******************************************';

async function deployFreshSimple() {
  console.log('🚀 DEPLOYING FRESH ERC-3643 SYSTEM (SIMPLE VERSION)');
  console.log('='.repeat(80));
  console.log('');

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Deployer Balance: ${ethers.formatEther(await ethers.provider.getBalance(deployer.address))} MATIC`);
    console.log(`🆔 Working Underlying Registry: ${WORKING_UNDERLYING_REGISTRY}`);
    console.log(`👑 Admin Address: ${ADMIN_ADDRESS}`);
    console.log('');

    // STEP 1: Verify admin is verified in the working registry
    console.log('🔍 STEP 1: VERIFYING ADMIN IN WORKING REGISTRY');
    console.log('-'.repeat(60));
    
    const workingRegistryABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const workingRegistry = new ethers.Contract(
      WORKING_UNDERLYING_REGISTRY, 
      workingRegistryABI, 
      deployer
    );
    
    const adminVerified = await workingRegistry.isVerified(ADMIN_ADDRESS);
    console.log(`✅ Admin verified in working registry: ${adminVerified}`);
    
    if (!adminVerified) {
      console.log('❌ DEPLOYMENT STOPPED: Admin is not verified in the working registry');
      console.log('💡 Please register the admin in the Identity Registry first');
      return;
    }
    console.log('');

    // STEP 2: Deploy Working Identity Registry Wrapper (without upgrades)
    console.log('🔧 STEP 2: DEPLOYING WORKING IDENTITY REGISTRY WRAPPER');
    console.log('-'.repeat(60));
    
    // Deploy a simple non-upgradeable version first
    const SimpleWrapper = await ethers.getContractFactory('WorkingIdentityRegistryWrapper');
    
    console.log('📝 Deploying simple wrapper...');
    
    // Deploy the implementation directly (not as proxy for now)
    const wrapperImpl = await SimpleWrapper.deploy();
    await wrapperImpl.waitForDeployment();
    
    const wrapperAddress = await wrapperImpl.getAddress();
    console.log(`✅ Wrapper Implementation deployed: ${wrapperAddress}`);
    
    // Initialize it
    console.log('📝 Initializing wrapper...');
    const initTx = await wrapperImpl.initialize(WORKING_UNDERLYING_REGISTRY, ADMIN_ADDRESS);
    await initTx.wait();
    console.log('✅ Wrapper initialized');
    
    // Verify wrapper sees admin as verified
    const wrapperVerified = await wrapperImpl.isVerified(ADMIN_ADDRESS);
    console.log(`🔍 Wrapper sees admin as verified: ${wrapperVerified}`);
    
    if (!wrapperVerified) {
      console.log('❌ WARNING: Wrapper doesn\'t see admin as verified');
      console.log('This might cause issues with token functionality');
    }
    console.log('');

    // STEP 3: Deploy Fresh ERC-3643 Token (without upgrades)
    console.log('🪙 STEP 3: DEPLOYING FRESH ERC-3643 TOKEN');
    console.log('-'.repeat(60));
    
    const FreshToken = await ethers.getContractFactory('FreshERC3643Token');
    
    console.log('📝 Deploying token...');
    
    const tokenImpl = await FreshToken.deploy();
    await tokenImpl.waitForDeployment();
    
    const tokenAddress = await tokenImpl.getAddress();
    console.log(`✅ Token Implementation deployed: ${tokenAddress}`);
    
    // Initialize the token
    console.log('📝 Initializing token...');
    const tokenInitTx = await tokenImpl.initialize(
      'Fresh ERC3643 Token',
      'FRESH3643',
      18,
      ethers.parseUnits('1000000', 18), // 1 million max supply
      wrapperAddress,
      ADMIN_ADDRESS
    );
    await tokenInitTx.wait();
    console.log('✅ Token initialized');
    console.log('');

    // STEP 4: Verify Token Functionality
    console.log('🧪 STEP 4: VERIFYING TOKEN FUNCTIONALITY');
    console.log('-'.repeat(60));
    
    // Check token info
    const tokenName = await tokenImpl.name();
    const tokenSymbol = await tokenImpl.symbol();
    const tokenDecimals = await tokenImpl.decimals();
    const tokenMaxSupply = await tokenImpl.maxSupply();
    const tokenIdentityRegistry = await tokenImpl.identityRegistry();
    
    console.log(`📋 Token Name: ${tokenName}`);
    console.log(`🏷️ Token Symbol: ${tokenSymbol}`);
    console.log(`🔢 Token Decimals: ${tokenDecimals}`);
    console.log(`📊 Max Supply: ${ethers.formatUnits(tokenMaxSupply, tokenDecimals)}`);
    console.log(`🆔 Identity Registry: ${tokenIdentityRegistry}`);
    
    // Check if admin is verified via token
    const tokenVerified = await tokenImpl.isVerified(ADMIN_ADDRESS);
    console.log(`✅ Token sees admin as verified: ${tokenVerified}`);
    
    if (!tokenVerified) {
      console.log('❌ CRITICAL: Token doesn\'t see admin as verified');
      console.log('This will prevent minting and other operations');
      return;
    }
    console.log('');

    // STEP 5: Test Minting
    console.log('💰 STEP 5: TESTING TOKEN MINTING');
    console.log('-'.repeat(60));
    
    // Connect as admin for minting
    const adminSigner = await ethers.getImpersonatedSigner(ADMIN_ADDRESS);
    const tokenAsAdmin = tokenImpl.connect(adminSigner);
    
    const mintAmount = ethers.parseUnits('1000', 18);
    console.log(`🔄 Attempting to mint ${ethers.formatUnits(mintAmount, 18)} tokens to admin...`);
    
    try {
      const mintTx = await tokenAsAdmin.mint(ADMIN_ADDRESS, mintAmount);
      console.log(`⏳ Mint transaction submitted: ${mintTx.hash}`);
      
      await mintTx.wait();
      console.log(`✅ Mint transaction confirmed!`);
      
      const balance = await tokenImpl.balanceOf(ADMIN_ADDRESS);
      console.log(`💰 Admin balance: ${ethers.formatUnits(balance, 18)} ${tokenSymbol}`);
      console.log('');

      // STEP 6: Final Summary
      console.log('🎉 DEPLOYMENT COMPLETE!');
      console.log('='.repeat(80));
      console.log('');
      console.log('📊 DEPLOYMENT SUMMARY:');
      console.log(`🆔 Working Identity Registry Wrapper: ${wrapperAddress}`);
      console.log(`🪙 Fresh ERC-3643 Token: ${tokenAddress}`);
      console.log(`👤 Admin Address: ${ADMIN_ADDRESS}`);
      console.log(`✅ Admin Verified: ${tokenVerified}`);
      console.log(`💰 Initial Balance: ${ethers.formatUnits(balance, 18)} ${tokenSymbol}`);
      console.log('');
      console.log('🎯 TOKEN STATUS: FULLY FUNCTIONAL!');
      console.log('✅ Identity verification working');
      console.log('✅ Minting successful');
      console.log('✅ All institutional features available');
      console.log('✅ Ready for production use');
      console.log('');
      console.log('🎉 SUCCESS! Your ERC-3643 token is ready for institutional use!');

      return {
        workingWrapper: wrapperAddress,
        freshToken: tokenAddress,
        adminVerified: tokenVerified,
        initialBalance: ethers.formatUnits(balance, 18)
      };

    } catch (mintError) {
      console.log(`❌ Mint failed: ${mintError.message}`);
      
      if (mintError.message.includes('recipient not verified')) {
        console.log('🔍 Admin verification issue detected');
        console.log('The token sees admin as verified but minting still fails');
        console.log('This suggests additional verification layers');
      }
      
      throw mintError;
    }

  } catch (error) {
    console.error('❌ DEPLOYMENT FAILED:', error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  deployFreshSimple()
    .then((result) => {
      console.log('✅ Deployment completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Deployment failed');
      process.exit(1);
    });
}

module.exports = { deployFreshSimple };
