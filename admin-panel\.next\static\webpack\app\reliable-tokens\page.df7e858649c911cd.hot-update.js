"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/utils/fallbackProvider.ts":
/*!***************************************!*\
  !*** ./src/utils/fallbackProvider.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FallbackProvider: () => (/* binding */ FallbackProvider),\n/* harmony export */   safeContractCall: () => (/* binding */ safeContractCall),\n/* harmony export */   safeContractTransaction: () => (/* binding */ safeContractTransaction)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n/**\n * Fallback-first provider that bypasses browser provider issues\n * Uses reliable RPC endpoints as primary, browser provider as secondary\n */ class FallbackProvider {\n    static async initialize() {\n        if (this.initialized) return;\n        // Initialize reliable RPC providers (in order of preference)\n        const rpcUrls = [\n            'https://rpc-amoy.polygon.technology/',\n            'https://polygon-amoy.drpc.org',\n            'https://rpc.ankr.com/polygon_amoy',\n            'https://polygon-amoy-bor-rpc.publicnode.com'\n        ];\n        for (const url of rpcUrls){\n            try {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_0__.JsonRpcProvider(url);\n                await provider.getBlockNumber(); // Test connectivity\n                this.reliableProviders.push(provider);\n                console.log(\"✅ Reliable provider added: \".concat(url));\n            } catch (error) {\n                console.warn(\"❌ RPC failed: \".concat(url));\n            }\n        }\n        // Initialize browser provider if available\n        if ( true && window.ethereum) {\n            try {\n                this.browserProvider = new ethers__WEBPACK_IMPORTED_MODULE_1__.BrowserProvider(window.ethereum);\n                console.log('✅ Browser provider initialized');\n            } catch (error) {\n                console.warn('❌ Browser provider failed to initialize');\n            }\n        }\n        this.initialized = true;\n        console.log(\"\\uD83D\\uDD27 FallbackProvider initialized with \".concat(this.reliableProviders.length, \" reliable providers\"));\n    }\n    /**\n   * Get a provider for read-only operations (view calls)\n   * Always uses reliable RPC providers to avoid \"missing revert data\" errors\n   */ static async getReadProvider() {\n        await this.initialize();\n        if (this.reliableProviders.length === 0) {\n            throw new Error('No reliable providers available');\n        }\n        // Always use the first working reliable provider for reads\n        for (const provider of this.reliableProviders){\n            try {\n                await provider.getBlockNumber(); // Quick connectivity test\n                return provider;\n            } catch (error) {\n                console.warn('Reliable provider failed, trying next...');\n            }\n        }\n        throw new Error('All reliable providers failed');\n    }\n    /**\n   * Get a provider for write operations (transactions)\n   * Uses browser provider for signing, but with fallback options\n   */ static async getWriteProvider() {\n        await this.initialize();\n        if (!this.browserProvider) {\n            throw new Error('Browser provider not available. Please connect your wallet.');\n        }\n        return this.browserProvider;\n    }\n    /**\n   * Perform a contract call with automatic fallback\n   * Tries browser provider first, falls back to reliable provider\n   */ static async safeCall(contractAddress, abi, functionName) {\n        let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        await this.initialize();\n        // Strategy 1: Try browser provider first (for consistency with wallet)\n        if (this.browserProvider) {\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, this.browserProvider);\n                const result = await contract[functionName](...args);\n                console.log(\"✅ Browser provider call successful: \".concat(functionName));\n                return result;\n            } catch (error) {\n                var _error_message;\n                console.warn(\"❌ Browser provider failed for \".concat(functionName, \":\"), error.message);\n                // Don't retry browser provider for \"missing revert data\" errors\n                if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('missing revert data')) {\n                    console.log('🔄 Switching to reliable provider due to missing revert data');\n                }\n            }\n        }\n        // Strategy 2: Use reliable provider as fallback\n        const readProvider = await this.getReadProvider();\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, readProvider);\n        const result = await contract[functionName](...args);\n        console.log(\"✅ Reliable provider call successful: \".concat(functionName));\n        return result;\n    }\n    /**\n   * Perform a transaction with enhanced error handling\n   */ static async safeTransaction(contractAddress, abi, functionName) {\n        let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [], overrides = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n        const writeProvider = await this.getWriteProvider();\n        const signer = await writeProvider.getSigner();\n        // Debug signer information\n        const signerAddress = await signer.getAddress();\n        console.log(\"\\uD83D\\uDD10 Transaction signer address: \".concat(signerAddress));\n        if (signerAddress === '******************************************') {\n            throw new Error('Invalid signer address (zero address). Please ensure your wallet is connected and unlocked.');\n        }\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, signer);\n        // Get current gas pricing from reliable provider\n        const readProvider = await this.getReadProvider();\n        const feeData = await readProvider.getFeeData();\n        const txOverrides = {\n            gasLimit: 150000,\n            gasPrice: feeData.gasPrice ? feeData.gasPrice * 120n / 100n : ethers__WEBPACK_IMPORTED_MODULE_3__.parseUnits('50', 'gwei'),\n            ...overrides\n        };\n        console.log(\"\\uD83D\\uDD04 Sending transaction: \".concat(functionName, \" with gas price \").concat(ethers__WEBPACK_IMPORTED_MODULE_3__.formatUnits(txOverrides.gasPrice, 'gwei'), \" gwei\"));\n        try {\n            const tx = await contract[functionName](...args, txOverrides);\n            return tx;\n        } catch (error) {\n            console.error(\"❌ Transaction failed for \".concat(functionName, \":\"), error);\n            // Try to get more detailed error information\n            if (error.reason) {\n                console.error(\"Revert reason: \".concat(error.reason));\n            }\n            if (error.data) {\n                console.error(\"Error data: \".concat(error.data));\n            }\n            throw error;\n        }\n    }\n    /**\n   * Reset all providers (useful for troubleshooting)\n   */ static reset() {\n        this.reliableProviders = [];\n        this.browserProvider = null;\n        this.initialized = false;\n        console.log('🔄 FallbackProvider reset');\n    }\n}\nFallbackProvider.reliableProviders = [];\nFallbackProvider.browserProvider = null;\nFallbackProvider.initialized = false;\n/**\n * Convenience function for safe contract calls\n */ async function safeContractCall(contractAddress, abi, functionName) {\n    let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n    return FallbackProvider.safeCall(contractAddress, abi, functionName, args);\n}\n/**\n * Convenience function for safe transactions\n */ async function safeContractTransaction(contractAddress, abi, functionName) {\n    let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [], overrides = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n    return FallbackProvider.safeTransaction(contractAddress, abi, functionName, args, overrides);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/fallbackProvider.ts\n"));

/***/ })

});