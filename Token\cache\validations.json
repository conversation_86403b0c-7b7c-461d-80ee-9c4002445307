{"version": "3.4", "log": [{"@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:51", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:20", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils"], "methods": ["proxiableUUID()", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:30", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:21", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl": {"src": "@openzeppelin\\contracts\\access\\IAccessControl.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967": {"src": "@openzeppelin\\contracts\\interfaces\\IERC1967.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC1822.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils": {"src": "@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Utils.sol:15", "version": {"withMetadata": "988c51be94040b5bb78e6739ecf31c3539c880afc84253fa97a12de186ef7572", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon": {"src": "@openzeppelin\\contracts\\proxy\\beacon\\IBeacon.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata": {"src": "@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol:11", "inherit": ["@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20"], "libraries": [], "methods": ["name()", "symbol()", "decimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin\\contracts\\utils\\Address.sol:11", "version": {"withMetadata": "041c0d4f90bb8e17d3ab85a9968cb4f2c9ad776fac701e799df8a26338a603d7", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Errors.sol:Errors"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Errors.sol:Errors": {"src": "@openzeppelin\\contracts\\utils\\Errors.sol:14", "version": {"withMetadata": "5f46a4584c437cd91e6e4386b93037e61fa724904275bd84b5f58da24743a61f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot": {"src": "@openzeppelin\\contracts\\utils\\StorageSlot.sol:34", "version": {"withMetadata": "e34a460afe63f8e676880ba0751522137768df1fdd3fff06c598d9006111366b", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:FreshERC3643Token": {"src": "contracts\\fresh\\FreshERC3643Token.sol:20", "version": {"withMetadata": "56290d296bd8e1324b8c7b3f0ddaf201ee4275a1b5c2316505b8002841907d96", "withoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c", "linkedWithoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "setIdentityRegistry(address)", "batchMint(address[],uint256[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()", "getTokenInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingIdentityRegistry)2769", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:30"}, {"label": "_decimals", "offset": 20, "slot": "0", "type": "t_uint8", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:31"}, {"label": "maxSupply", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:32"}, {"label": "_frozenBalances", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_uint256)", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingIdentityRegistry)2769": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\FreshERC3643Token.sol:10", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/UpgradeableERC3643Token.sol:UpgradeableERC3643Token": {"src": "contracts\\fresh\\UpgradeableERC3643Token.sol:20", "version": {"withMetadata": "e4e277ed45558e0965f031aa6192b996e8e076bb58514b91b105953b41106cba", "withoutMetadata": "e5e4a39ba4df7398edca2d501c94aad58b75b8ae9a7a6da0615f18dc66296f71", "linkedWithoutMetadata": "e5e4a39ba4df7398edca2d501c94aad58b75b8ae9a7a6da0615f18dc66296f71"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address,address,string)", "identityRegistry()", "compliance()", "version()", "isVerified(address)", "proposeUpgrade(address)", "executeUpgrade()", "cancelUpgrade()", "renounceUpgrades()", "increaseTimelock(uint256)", "forcedTransfer(address,address,uint256)", "freeze(address)", "unfreeze(address)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "is<PERSON><PERSON>zen(address)", "frozenBalanceOf(address)", "availableBalanceOf(address)", "decimals()", "mint(address,uint256)", "burn(address,uint256)", "setIdentityRegistry(address)", "setCompliance(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "0", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:38"}, {"label": "complianceModule", "offset": 0, "slot": "1", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:39"}, {"label": "tokenVersion", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:40"}, {"label": "maxSupply", "offset": 0, "slot": "3", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:41"}, {"label": "upgradesRenounced", "offset": 0, "slot": "4", "type": "t_bool", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:47"}, {"label": "upgradeTimelock", "offset": 0, "slot": "5", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:48"}, {"label": "pendingUpgradeTimestamp", "offset": 0, "slot": "6", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:49"}, {"label": "pendingImplementation", "offset": 0, "slot": "7", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:50"}, {"label": "frozen", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:56"}, {"label": "frozenBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:51", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:20", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils"], "methods": ["proxiableUUID()", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:30", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:21", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl": {"src": "@openzeppelin\\contracts\\access\\IAccessControl.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967": {"src": "@openzeppelin\\contracts\\interfaces\\IERC1967.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC1822.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils": {"src": "@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Utils.sol:15", "version": {"withMetadata": "988c51be94040b5bb78e6739ecf31c3539c880afc84253fa97a12de186ef7572", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon": {"src": "@openzeppelin\\contracts\\proxy\\beacon\\IBeacon.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata": {"src": "@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol:11", "inherit": ["@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20"], "libraries": [], "methods": ["name()", "symbol()", "decimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin\\contracts\\utils\\Address.sol:11", "version": {"withMetadata": "041c0d4f90bb8e17d3ab85a9968cb4f2c9ad776fac701e799df8a26338a603d7", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Errors.sol:Errors"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Errors.sol:Errors": {"src": "@openzeppelin\\contracts\\utils\\Errors.sol:14", "version": {"withMetadata": "5f46a4584c437cd91e6e4386b93037e61fa724904275bd84b5f58da24743a61f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot": {"src": "@openzeppelin\\contracts\\utils\\StorageSlot.sol:34", "version": {"withMetadata": "e34a460afe63f8e676880ba0751522137768df1fdd3fff06c598d9006111366b", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:FreshERC3643Token": {"src": "contracts\\fresh\\FreshERC3643Token.sol:20", "version": {"withMetadata": "0b31189bee29ac2c9c1589c794d20b50cecedfede2b5954bb04157ab37ebb207", "withoutMetadata": "b1e484679645bcec116cc25f3a14d0db6830424ba2a1afe23e98352c36259dac", "linkedWithoutMetadata": "b1e484679645bcec116cc25f3a14d0db6830424ba2a1afe23e98352c36259dac"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "setIdentityRegistry(address)", "batchMint(address[],uint256[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()", "getTokenInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingIdentityRegistry)2769", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:30"}, {"label": "_decimals", "offset": 20, "slot": "0", "type": "t_uint8", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:31"}, {"label": "maxSupply", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:32"}, {"label": "_frozenBalances", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_uint256)", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingIdentityRegistry)2769": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\FreshERC3643Token.sol:10", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/ProperERC3643Token.sol:ProperERC3643Token": {"src": "contracts\\fresh\\ProperERC3643Token.sol:13", "version": {"withMetadata": "410aaa30c295767f0509cd28e10601a70de331b40cdba4aed09a66681e0dba92", "withoutMetadata": "ef5b3882722d3bcf0ec4bcd0db2a56f5dfabe1c5e60c0a0f70322619997a7147", "linkedWithoutMetadata": "ef5b3882722d3bcf0ec4bcd0db2a56f5dfabe1c5e60c0a0f70322619997a7147"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,address,string)", "version()", "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>()", "complianceAddress()", "isVerified(address)", "registerIdentity(address,uint16)", "is<PERSON><PERSON><PERSON><PERSON>(address)", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(address)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(address)", "registerAndWhitelist(address,uint16)", "mint(address,uint256)", "burn(uint256)", "burnFrom(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "frozenTokensOf(address)", "availableBalanceOf(address)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "paused()", "renounceUpgrades()", "getTokenInfo()", "getAccountInfo(address)", "supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_version", "offset": 0, "slot": "0", "type": "t_string_storage", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:25"}, {"label": "_identityRegistry", "offset": 0, "slot": "1", "type": "t_address", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:26"}, {"label": "_complianceModule", "offset": 0, "slot": "2", "type": "t_address", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:27"}, {"label": "upgradesRenounced", "offset": 20, "slot": "2", "type": "t_bool", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:28"}, {"label": "_paused", "offset": 21, "slot": "2", "type": "t_bool", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:29"}, {"label": "_whitelist", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_bool)", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:32"}, {"label": "_identityVerified", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_bool)", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:33"}, {"label": "_frozenTokens", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_uint256)", "contract": "ProperERC3643Token", "src": "contracts\\fresh\\ProperERC3643Token.sol:34"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/UpgradeableERC3643Token.sol:UpgradeableERC3643Token": {"src": "contracts\\fresh\\UpgradeableERC3643Token.sol:20", "version": {"withMetadata": "15976ca06714c3c096ccf54e656471c9232187a97f7b21f5aecb801344dea5a5", "withoutMetadata": "0b06cb5a848516ee20b9b76777615d49daf94b359a742a7db54ef8e4ecda0cdd", "linkedWithoutMetadata": "0b06cb5a848516ee20b9b76777615d49daf94b359a742a7db54ef8e4ecda0cdd"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address,address,string)", "identityRegistry()", "compliance()", "version()", "isVerified(address)", "proposeUpgrade(address)", "executeUpgrade()", "cancelUpgrade()", "renounceUpgrades()", "increaseTimelock(uint256)", "forcedTransfer(address,address,uint256)", "freeze(address)", "unfreeze(address)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "is<PERSON><PERSON>zen(address)", "frozenBalanceOf(address)", "availableBalanceOf(address)", "decimals()", "mint(address,uint256)", "burn(address,uint256)", "setIdentityRegistry(address)", "setCompliance(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "0", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:38"}, {"label": "complianceModule", "offset": 0, "slot": "1", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:39"}, {"label": "tokenVersion", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:40"}, {"label": "maxSupply", "offset": 0, "slot": "3", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:41"}, {"label": "upgradesRenounced", "offset": 0, "slot": "4", "type": "t_bool", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:47"}, {"label": "upgradeTimelock", "offset": 0, "slot": "5", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:48"}, {"label": "pendingUpgradeTimestamp", "offset": 0, "slot": "6", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:49"}, {"label": "pendingImplementation", "offset": 0, "slot": "7", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:50"}, {"label": "frozen", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:56"}, {"label": "frozenBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:51", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:20", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils"], "methods": ["proxiableUUID()", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:30", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:21", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/AccessControl.sol:AccessControl": {"src": "@openzeppelin\\contracts\\access\\AccessControl.sol:49", "inherit": ["@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl": {"src": "@openzeppelin\\contracts\\access\\IAccessControl.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967": {"src": "@openzeppelin\\contracts\\interfaces\\IERC1967.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC1822.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils": {"src": "@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Utils.sol:15", "version": {"withMetadata": "988c51be94040b5bb78e6739ecf31c3539c880afc84253fa97a12de186ef7572", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon": {"src": "@openzeppelin\\contracts\\proxy\\beacon\\IBeacon.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:29", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:44"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:37"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata": {"src": "@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol:11", "inherit": ["@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20"], "libraries": [], "methods": ["name()", "symbol()", "decimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin\\contracts\\utils\\Address.sol:11", "version": {"withMetadata": "041c0d4f90bb8e17d3ab85a9968cb4f2c9ad776fac701e799df8a26338a603d7", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Errors.sol:Errors"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Context.sol:Context": {"src": "@openzeppelin\\contracts\\utils\\Context.sol:16", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Errors.sol:Errors": {"src": "@openzeppelin\\contracts\\utils\\Errors.sol:14", "version": {"withMetadata": "5f46a4584c437cd91e6e4386b93037e61fa724904275bd84b5f58da24743a61f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Pausable.sol:Pausable": {"src": "@openzeppelin\\contracts\\utils\\Pausable.sol:17", "inherit": ["@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_paused", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin\\contracts\\utils\\Pausable.sol:18"}], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot": {"src": "@openzeppelin\\contracts\\utils\\StorageSlot.sol:34", "version": {"withMetadata": "e34a460afe63f8e676880ba0751522137768df1fdd3fff06c598d9006111366b", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol:20", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:FreshERC3643Token": {"src": "contracts\\fresh\\FreshERC3643Token.sol:20", "version": {"withMetadata": "56290d296bd8e1324b8c7b3f0ddaf201ee4275a1b5c2316505b8002841907d96", "withoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c", "linkedWithoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "setIdentityRegistry(address)", "batchMint(address[],uint256[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()", "getTokenInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingIdentityRegistry)3743", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:30"}, {"label": "_decimals", "offset": 20, "slot": "0", "type": "t_uint8", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:31"}, {"label": "maxSupply", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:32"}, {"label": "_frozenBalances", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_uint256)", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingIdentityRegistry)3743": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\FreshERC3643Token.sol:10", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleFreshToken.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\SimpleFreshToken.sol:8", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleFreshToken.sol:SimpleFreshToken": {"src": "contracts\\fresh\\SimpleFreshToken.sol:16", "version": {"withMetadata": "7f2bae219a468728e2b0f40f78bd34c9c708d918caab94ebf4e604668189ad6b", "withoutMetadata": "e48c716d6ed974be157e3a6df3bc0507f75197f2659fa20a7880f646aea4295c", "linkedWithoutMetadata": "e48c716d6ed974be157e3a6df3bc0507f75197f2659fa20a7880f646aea4295c"}, "inherit": ["@openzeppelin/contracts/utils/Pausable.sol:Pausable", "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:42"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:37"}, {"label": "_roles", "offset": 0, "slot": "5", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}, {"label": "_paused", "offset": 0, "slot": "6", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin\\contracts\\utils\\Pausable.sol:18"}, {"label": "identityRegistry", "offset": 1, "slot": "6", "type": "t_contract(IWorkingIdentityRegistry)4540", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:19"}, {"label": "_decimals", "offset": 21, "slot": "6", "type": "t_uint8", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:20"}, {"label": "maxSupply", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:21"}, {"label": "_frozenBalances", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_uint256)", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:24"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IWorkingIdentityRegistry)4540": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleWorkingWrapper.sol:IWorkingUnderlyingRegistry": {"src": "contracts\\fresh\\SimpleWorkingWrapper.sol:6", "inherit": [], "libraries": [], "methods": ["isVerified(address)", "registerIdentity(address,uint16)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleWorkingWrapper.sol:SimpleWorkingWrapper": {"src": "contracts\\fresh\\SimpleWorkingWrapper.sol:15", "version": {"withMetadata": "d990147e3c99ff8283818d4f5a1d91ccaa478649b3442884cab90d199ae5cb1a", "withoutMetadata": "512c654cab20b2407d7ece86ee3a528046c79266e0789f9051baae91f5dd964d", "linkedWithoutMetadata": "512c654cab20b2407d7ece86ee3a528046c79266e0789f9051baae91f5dd964d"}, "inherit": ["@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(address,address)", "isVerified(address)", "registerIdentity(address,address,uint16)", "investorCountry(address)", "identity(address)", "addAgent(address)", "removeAgent(address)", "isAgent(address)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SimpleWorkingWrapper", "src": "contracts\\fresh\\SimpleWorkingWrapper.sol:22"}], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}, {"label": "underlyingRegistry", "offset": 0, "slot": "1", "type": "t_contract(IWorkingUnderlyingRegistry)5128", "contract": "SimpleWorkingWrapper", "src": "contracts\\fresh\\SimpleWorkingWrapper.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IWorkingUnderlyingRegistry)5128": {"label": "contract IWorkingUnderlyingRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/UpgradeableERC3643Token.sol:UpgradeableERC3643Token": {"src": "contracts\\fresh\\UpgradeableERC3643Token.sol:20", "version": {"withMetadata": "e4e277ed45558e0965f031aa6192b996e8e076bb58514b91b105953b41106cba", "withoutMetadata": "e5e4a39ba4df7398edca2d501c94aad58b75b8ae9a7a6da0615f18dc66296f71", "linkedWithoutMetadata": "e5e4a39ba4df7398edca2d501c94aad58b75b8ae9a7a6da0615f18dc66296f71"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address,address,string)", "identityRegistry()", "compliance()", "version()", "isVerified(address)", "proposeUpgrade(address)", "executeUpgrade()", "cancelUpgrade()", "renounceUpgrades()", "increaseTimelock(uint256)", "forcedTransfer(address,address,uint256)", "freeze(address)", "unfreeze(address)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "is<PERSON><PERSON>zen(address)", "frozenBalanceOf(address)", "availableBalanceOf(address)", "decimals()", "mint(address,uint256)", "burn(address,uint256)", "setIdentityRegistry(address)", "setCompliance(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityReg<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "0", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:38"}, {"label": "complianceModule", "offset": 0, "slot": "1", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:39"}, {"label": "tokenVersion", "offset": 0, "slot": "2", "type": "t_string_storage", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:40"}, {"label": "maxSupply", "offset": 0, "slot": "3", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:41"}, {"label": "upgradesRenounced", "offset": 0, "slot": "4", "type": "t_bool", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:47"}, {"label": "upgradeTimelock", "offset": 0, "slot": "5", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:48"}, {"label": "pendingUpgradeTimestamp", "offset": 0, "slot": "6", "type": "t_uint256", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:49"}, {"label": "pendingImplementation", "offset": 0, "slot": "7", "type": "t_address", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:50"}, {"label": "frozen", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_bool)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:56"}, {"label": "frozenBalances", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_uint256)", "contract": "UpgradeableERC3643Token", "src": "contracts\\fresh\\UpgradeableERC3643Token.sol:57"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/WorkingIdentityRegistryWrapper.sol:IWorkingUnderlyingRegistry": {"src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:8", "inherit": [], "libraries": [], "methods": ["isVerified(address)", "registerIdentity(address,uint16)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/WorkingIdentityRegistryWrapper.sol:WorkingIdentityRegistryWrapper": {"src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:18", "version": {"withMetadata": "0eec7fc6fe60038c67e947687e7e23d4ec263c7d27c894e195195eb76c7ee979", "withoutMetadata": "8a00a0bc1184407a97022790a0752ca04b90b804b763e4daae149d14e7b033ff", "linkedWithoutMetadata": "8a00a0bc1184407a97022790a0752ca04b90b804b763e4daae149d14e7b033ff"}, "inherit": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(address,address)", "isVerified(address)", "registerIdentity(address,address,uint16)", "investorCountry(address)", "identity(address)", "deleteIdentity(address)", "updateCountry(address,uint16)", "batchRegisterIdentity(address[],address[],uint16[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "getUnderlyingRegistry()", "updateUnderlyingRegistry(address)", "version()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "underlyingRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingUnderlyingRegistry)6377", "contract": "WorkingIdentityRegistryWrapper", "src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:25"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingUnderlyingRegistry)6377": {"label": "contract IWorkingUnderlyingRegistry", "numberOfBytes": "20"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:51", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:20", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils"], "methods": ["proxiableUUID()", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:30", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:21", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/AccessControl.sol:AccessControl": {"src": "@openzeppelin\\contracts\\access\\AccessControl.sol:49", "inherit": ["@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl": {"src": "@openzeppelin\\contracts\\access\\IAccessControl.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967": {"src": "@openzeppelin\\contracts\\interfaces\\IERC1967.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC1822.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils": {"src": "@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Utils.sol:15", "version": {"withMetadata": "988c51be94040b5bb78e6739ecf31c3539c880afc84253fa97a12de186ef7572", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon": {"src": "@openzeppelin\\contracts\\proxy\\beacon\\IBeacon.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:29", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:44"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:37"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata": {"src": "@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol:11", "inherit": ["@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20"], "libraries": [], "methods": ["name()", "symbol()", "decimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin\\contracts\\utils\\Address.sol:11", "version": {"withMetadata": "041c0d4f90bb8e17d3ab85a9968cb4f2c9ad776fac701e799df8a26338a603d7", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Errors.sol:Errors"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Context.sol:Context": {"src": "@openzeppelin\\contracts\\utils\\Context.sol:16", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Errors.sol:Errors": {"src": "@openzeppelin\\contracts\\utils\\Errors.sol:14", "version": {"withMetadata": "5f46a4584c437cd91e6e4386b93037e61fa724904275bd84b5f58da24743a61f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Pausable.sol:Pausable": {"src": "@openzeppelin\\contracts\\utils\\Pausable.sol:17", "inherit": ["@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_paused", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin\\contracts\\utils\\Pausable.sol:18"}], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot": {"src": "@openzeppelin\\contracts\\utils\\StorageSlot.sol:34", "version": {"withMetadata": "e34a460afe63f8e676880ba0751522137768df1fdd3fff06c598d9006111366b", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol:20", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:FreshERC3643Token": {"src": "contracts\\fresh\\FreshERC3643Token.sol:20", "version": {"withMetadata": "56290d296bd8e1324b8c7b3f0ddaf201ee4275a1b5c2316505b8002841907d96", "withoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c", "linkedWithoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "setIdentityRegistry(address)", "batchMint(address[],uint256[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()", "getTokenInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingIdentityRegistry)3743", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:30"}, {"label": "_decimals", "offset": 20, "slot": "0", "type": "t_uint8", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:31"}, {"label": "maxSupply", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:32"}, {"label": "_frozenBalances", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_uint256)", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingIdentityRegistry)3743": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\FreshERC3643Token.sol:10", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleFreshToken.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\SimpleFreshToken.sol:8", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleFreshToken.sol:SimpleFreshToken": {"src": "contracts\\fresh\\SimpleFreshToken.sol:16", "version": {"withMetadata": "7f2bae219a468728e2b0f40f78bd34c9c708d918caab94ebf4e604668189ad6b", "withoutMetadata": "e48c716d6ed974be157e3a6df3bc0507f75197f2659fa20a7880f646aea4295c", "linkedWithoutMetadata": "e48c716d6ed974be157e3a6df3bc0507f75197f2659fa20a7880f646aea4295c"}, "inherit": ["@openzeppelin/contracts/utils/Pausable.sol:Pausable", "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:42"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:37"}, {"label": "_roles", "offset": 0, "slot": "5", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}, {"label": "_paused", "offset": 0, "slot": "6", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin\\contracts\\utils\\Pausable.sol:18"}, {"label": "identityRegistry", "offset": 1, "slot": "6", "type": "t_contract(IWorkingIdentityRegistry)4540", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:19"}, {"label": "_decimals", "offset": 21, "slot": "6", "type": "t_uint8", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:20"}, {"label": "maxSupply", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:21"}, {"label": "_frozenBalances", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_uint256)", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:24"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IWorkingIdentityRegistry)4540": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleWorkingWrapper.sol:IWorkingUnderlyingRegistry": {"src": "contracts\\fresh\\SimpleWorkingWrapper.sol:6", "inherit": [], "libraries": [], "methods": ["isVerified(address)", "registerIdentity(address,uint16)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleWorkingWrapper.sol:SimpleWorkingWrapper": {"src": "contracts\\fresh\\SimpleWorkingWrapper.sol:15", "version": {"withMetadata": "d990147e3c99ff8283818d4f5a1d91ccaa478649b3442884cab90d199ae5cb1a", "withoutMetadata": "512c654cab20b2407d7ece86ee3a528046c79266e0789f9051baae91f5dd964d", "linkedWithoutMetadata": "512c654cab20b2407d7ece86ee3a528046c79266e0789f9051baae91f5dd964d"}, "inherit": ["@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(address,address)", "isVerified(address)", "registerIdentity(address,address,uint16)", "investorCountry(address)", "identity(address)", "addAgent(address)", "removeAgent(address)", "isAgent(address)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SimpleWorkingWrapper", "src": "contracts\\fresh\\SimpleWorkingWrapper.sol:22"}], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}, {"label": "underlyingRegistry", "offset": 0, "slot": "1", "type": "t_contract(IWorkingUnderlyingRegistry)5128", "contract": "SimpleWorkingWrapper", "src": "contracts\\fresh\\SimpleWorkingWrapper.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IWorkingUnderlyingRegistry)5128": {"label": "contract IWorkingUnderlyingRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/WorkingIdentityRegistryWrapper.sol:IWorkingUnderlyingRegistry": {"src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:8", "inherit": [], "libraries": [], "methods": ["isVerified(address)", "registerIdentity(address,uint16)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/WorkingIdentityRegistryWrapper.sol:WorkingIdentityRegistryWrapper": {"src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:18", "version": {"withMetadata": "0eec7fc6fe60038c67e947687e7e23d4ec263c7d27c894e195195eb76c7ee979", "withoutMetadata": "8a00a0bc1184407a97022790a0752ca04b90b804b763e4daae149d14e7b033ff", "linkedWithoutMetadata": "8a00a0bc1184407a97022790a0752ca04b90b804b763e4daae149d14e7b033ff"}, "inherit": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(address,address)", "isVerified(address)", "registerIdentity(address,address,uint16)", "investorCountry(address)", "identity(address)", "deleteIdentity(address)", "updateCountry(address,uint16)", "batchRegisterIdentity(address[],address[],uint16[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "getUnderlyingRegistry()", "updateUnderlyingRegistry(address)", "version()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "underlyingRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingUnderlyingRegistry)5379", "contract": "WorkingIdentityRegistryWrapper", "src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:25"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingUnderlyingRegistry)5379": {"label": "contract IWorkingUnderlyingRegistry", "numberOfBytes": "20"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:51", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:20", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils"], "methods": ["proxiableUUID()", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:30", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:21", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/AccessControl.sol:AccessControl": {"src": "@openzeppelin\\contracts\\access\\AccessControl.sol:49", "inherit": ["@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl": {"src": "@openzeppelin\\contracts\\access\\IAccessControl.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967": {"src": "@openzeppelin\\contracts\\interfaces\\IERC1967.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC1822.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils": {"src": "@openzeppelin\\contracts\\proxy\\ERC1967\\ERC1967Utils.sol:15", "version": {"withMetadata": "988c51be94040b5bb78e6739ecf31c3539c880afc84253fa97a12de186ef7572", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon": {"src": "@openzeppelin\\contracts\\proxy\\beacon\\IBeacon.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:29", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:44"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:37"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata": {"src": "@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol:11", "inherit": ["@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20"], "libraries": [], "methods": ["name()", "symbol()", "decimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin\\contracts\\utils\\Address.sol:11", "version": {"withMetadata": "041c0d4f90bb8e17d3ab85a9968cb4f2c9ad776fac701e799df8a26338a603d7", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Errors.sol:Errors"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Context.sol:Context": {"src": "@openzeppelin\\contracts\\utils\\Context.sol:16", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Errors.sol:Errors": {"src": "@openzeppelin\\contracts\\utils\\Errors.sol:14", "version": {"withMetadata": "5f46a4584c437cd91e6e4386b93037e61fa724904275bd84b5f58da24743a61f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Pausable.sol:Pausable": {"src": "@openzeppelin\\contracts\\utils\\Pausable.sol:17", "inherit": ["@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_paused", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin\\contracts\\utils\\Pausable.sol:18"}], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot": {"src": "@openzeppelin\\contracts\\utils\\StorageSlot.sol:34", "version": {"withMetadata": "e34a460afe63f8e676880ba0751522137768df1fdd3fff06c598d9006111366b", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol:20", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:FreshERC3643Token": {"src": "contracts\\fresh\\FreshERC3643Token.sol:20", "version": {"withMetadata": "56290d296bd8e1324b8c7b3f0ddaf201ee4275a1b5c2316505b8002841907d96", "withoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c", "linkedWithoutMetadata": "50bc46c580ebf430727c487590929d763707cd1f6beed7aaed0cf7ce1c4ea86c"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:ERC20Upgradeable", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "setIdentityRegistry(address)", "batchMint(address[],uint256[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()", "getTokenInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "identityRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingIdentityRegistry)3743", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:30"}, {"label": "_decimals", "offset": 20, "slot": "0", "type": "t_uint8", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:31"}, {"label": "maxSupply", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:32"}, {"label": "_frozenBalances", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_uint256)", "contract": "FreshERC3643Token", "src": "contracts\\fresh\\FreshERC3643Token.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)296_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)491_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingIdentityRegistry)3743": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/fresh/FreshERC3643Token.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\FreshERC3643Token.sol:10", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleFreshToken.sol:IWorkingIdentityRegistry": {"src": "contracts\\fresh\\SimpleFreshToken.sol:8", "inherit": [], "libraries": [], "methods": ["isVerified(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleFreshToken.sol:SimpleFreshToken": {"src": "contracts\\fresh\\SimpleFreshToken.sol:16", "version": {"withMetadata": "7f2bae219a468728e2b0f40f78bd34c9c708d918caab94ebf4e604668189ad6b", "withoutMetadata": "e48c716d6ed974be157e3a6df3bc0507f75197f2659fa20a7880f646aea4295c", "linkedWithoutMetadata": "e48c716d6ed974be157e3a6df3bc0507f75197f2659fa20a7880f646aea4295c"}, "inherit": ["@openzeppelin/contracts/utils/Pausable.sol:Pausable", "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(string,string,uint8,uint256,address,address)", "decimals()", "isVerified(address)", "availableBalanceOf(address)", "frozenBalanceOf(address)", "mint(address,uint256)", "burn(address,uint256)", "forcedTransfer(address,address,uint256)", "freezeTokens(address,uint256)", "unfreezeTokens(address,uint256)", "transfer(address,uint256)", "transferFrom(address,address,uint256)", "pause()", "unpause()", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "compliance()", "version()"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:42"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol:37"}, {"label": "_roles", "offset": 0, "slot": "5", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}, {"label": "_paused", "offset": 0, "slot": "6", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin\\contracts\\utils\\Pausable.sol:18"}, {"label": "identityRegistry", "offset": 1, "slot": "6", "type": "t_contract(IWorkingIdentityRegistry)4540", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:19"}, {"label": "_decimals", "offset": 21, "slot": "6", "type": "t_uint8", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:20"}, {"label": "maxSupply", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:21"}, {"label": "_frozenBalances", "offset": 0, "slot": "8", "type": "t_mapping(t_address,t_uint256)", "contract": "SimpleFreshToken", "src": "contracts\\fresh\\SimpleFreshToken.sol:24"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IWorkingIdentityRegistry)4540": {"label": "contract IWorkingIdentityRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleWorkingWrapper.sol:IWorkingUnderlyingRegistry": {"src": "contracts\\fresh\\SimpleWorkingWrapper.sol:6", "inherit": [], "libraries": [], "methods": ["isVerified(address)", "registerIdentity(address,uint16)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/SimpleWorkingWrapper.sol:SimpleWorkingWrapper": {"src": "contracts\\fresh\\SimpleWorkingWrapper.sol:15", "version": {"withMetadata": "d990147e3c99ff8283818d4f5a1d91ccaa478649b3442884cab90d199ae5cb1a", "withoutMetadata": "512c654cab20b2407d7ece86ee3a528046c79266e0789f9051baae91f5dd964d", "linkedWithoutMetadata": "512c654cab20b2407d7ece86ee3a528046c79266e0789f9051baae91f5dd964d"}, "inherit": ["@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(address,address)", "isVerified(address)", "registerIdentity(address,address,uint16)", "investorCountry(address)", "identity(address)", "addAgent(address)", "removeAgent(address)", "isAgent(address)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SimpleWorkingWrapper", "src": "contracts\\fresh\\SimpleWorkingWrapper.sol:22"}], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:55"}, {"label": "underlyingRegistry", "offset": 0, "slot": "1", "type": "t_contract(IWorkingUnderlyingRegistry)5128", "contract": "SimpleWorkingWrapper", "src": "contracts\\fresh\\SimpleWorkingWrapper.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IWorkingUnderlyingRegistry)5128": {"label": "contract IWorkingUnderlyingRegistry", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)1700_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)1700_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/WorkingIdentityRegistryWrapper.sol:IWorkingUnderlyingRegistry": {"src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:8", "inherit": [], "libraries": [], "methods": ["isVerified(address)", "registerIdentity(address,uint16)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/fresh/WorkingIdentityRegistryWrapper.sol:WorkingIdentityRegistryWrapper": {"src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:18", "version": {"withMetadata": "0eec7fc6fe60038c67e947687e7e23d4ec263c7d27c894e195195eb76c7ee979", "withoutMetadata": "8a00a0bc1184407a97022790a0752ca04b90b804b763e4daae149d14e7b033ff", "linkedWithoutMetadata": "8a00a0bc1184407a97022790a0752ca04b90b804b763e4daae149d14e7b033ff"}, "inherit": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(address,address)", "isVerified(address)", "registerIdentity(address,address,uint16)", "investorCountry(address)", "identity(address)", "deleteIdentity(address)", "updateCountry(address,uint16)", "batchRegisterIdentity(address[],address[],uint16[])", "addAgent(address)", "removeAgent(address)", "isAgent(address)", "getUnderlyingRegistry()", "updateUnderlyingRegistry(address)", "version()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "underlyingRegistry", "offset": 0, "slot": "0", "type": "t_contract(IWorkingUnderlyingRegistry)5379", "contract": "WorkingIdentityRegistryWrapper", "src": "contracts\\fresh\\WorkingIdentityRegistryWrapper.sol:25"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)26_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)36_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)147_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)26_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(IWorkingUnderlyingRegistry)5379": {"label": "contract IWorkingUnderlyingRegistry", "numberOfBytes": "20"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)26_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}]}