"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/debug-contract/page",{

/***/ "(app-pages-browser)/./src/utils/fallbackProvider.ts":
/*!***************************************!*\
  !*** ./src/utils/fallbackProvider.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FallbackProvider: () => (/* binding */ FallbackProvider),\n/* harmony export */   safeContractCall: () => (/* binding */ safeContractCall),\n/* harmony export */   safeContractTransaction: () => (/* binding */ safeContractTransaction)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n/**\n * Fallback-first provider that bypasses browser provider issues\n * Uses reliable RPC endpoints as primary, browser provider as secondary\n */ class FallbackProvider {\n    static async initialize() {\n        if (this.initialized) return;\n        // Initialize reliable RPC providers\n        const rpcUrls = [\n            'https://rpc-amoy.polygon.technology/',\n            'https://polygon-amoy.drpc.org',\n            'https://polygon-amoy-bor-rpc.publicnode.com'\n        ];\n        for (const url of rpcUrls){\n            try {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_0__.JsonRpcProvider(url);\n                await provider.getBlockNumber(); // Test connectivity\n                this.reliableProviders.push(provider);\n                console.log(\"✅ Reliable provider added: \".concat(url));\n            } catch (error) {\n                console.warn(\"❌ RPC failed: \".concat(url));\n            }\n        }\n        // Initialize browser provider if available\n        if ( true && window.ethereum) {\n            try {\n                this.browserProvider = new ethers__WEBPACK_IMPORTED_MODULE_1__.BrowserProvider(window.ethereum);\n                console.log('✅ Browser provider initialized');\n            } catch (error) {\n                console.warn('❌ Browser provider failed to initialize');\n            }\n        }\n        this.initialized = true;\n        console.log(\"\\uD83D\\uDD27 FallbackProvider initialized with \".concat(this.reliableProviders.length, \" reliable providers\"));\n    }\n    /**\n   * Get a provider for read-only operations (view calls)\n   * Always uses reliable RPC providers to avoid \"missing revert data\" errors\n   */ static async getReadProvider() {\n        await this.initialize();\n        if (this.reliableProviders.length === 0) {\n            throw new Error('No reliable providers available');\n        }\n        // Always use the first working reliable provider for reads\n        for (const provider of this.reliableProviders){\n            try {\n                await provider.getBlockNumber(); // Quick connectivity test\n                return provider;\n            } catch (error) {\n                console.warn('Reliable provider failed, trying next...');\n            }\n        }\n        throw new Error('All reliable providers failed');\n    }\n    /**\n   * Get a provider for write operations (transactions)\n   * Uses browser provider for signing, but with fallback options\n   */ static async getWriteProvider() {\n        await this.initialize();\n        if (!this.browserProvider) {\n            throw new Error('Browser provider not available. Please connect your wallet.');\n        }\n        return this.browserProvider;\n    }\n    /**\n   * Perform a contract call with automatic fallback\n   * Tries browser provider first, falls back to reliable provider\n   */ static async safeCall(contractAddress, abi, functionName) {\n        let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        await this.initialize();\n        // Strategy 1: Try browser provider first (for consistency with wallet)\n        if (this.browserProvider) {\n            try {\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, this.browserProvider);\n                const result = await contract[functionName](...args);\n                console.log(\"✅ Browser provider call successful: \".concat(functionName));\n                return result;\n            } catch (error) {\n                var _error_message;\n                console.warn(\"❌ Browser provider failed for \".concat(functionName, \":\"), error.message);\n                // Don't retry browser provider for \"missing revert data\" errors\n                if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('missing revert data')) {\n                    console.log('🔄 Switching to reliable provider due to missing revert data');\n                }\n            }\n        }\n        // Strategy 2: Use reliable provider as fallback\n        const readProvider = await this.getReadProvider();\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, readProvider);\n        const result = await contract[functionName](...args);\n        console.log(\"✅ Reliable provider call successful: \".concat(functionName));\n        return result;\n    }\n    /**\n   * Perform a transaction with enhanced error handling\n   */ static async safeTransaction(contractAddress, abi, functionName) {\n        let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [], overrides = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n        const writeProvider = await this.getWriteProvider();\n        const signer = await writeProvider.getSigner();\n        // Debug signer information\n        const signerAddress = await signer.getAddress();\n        console.log(\"\\uD83D\\uDD10 Transaction signer address: \".concat(signerAddress));\n        if (signerAddress === '******************************************') {\n            throw new Error('Invalid signer address (zero address). Please ensure your wallet is connected and unlocked.');\n        }\n        const contract = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(contractAddress, abi, signer);\n        // Get current gas pricing from reliable provider\n        const readProvider = await this.getReadProvider();\n        const feeData = await readProvider.getFeeData();\n        const txOverrides = {\n            gasLimit: 150000,\n            gasPrice: feeData.gasPrice ? feeData.gasPrice * 120n / 100n : ethers__WEBPACK_IMPORTED_MODULE_3__.parseUnits('50', 'gwei'),\n            ...overrides\n        };\n        console.log(\"\\uD83D\\uDD04 Sending transaction: \".concat(functionName, \" with gas price \").concat(ethers__WEBPACK_IMPORTED_MODULE_3__.formatUnits(txOverrides.gasPrice, 'gwei'), \" gwei\"));\n        try {\n            const tx = await contract[functionName](...args, txOverrides);\n            return tx;\n        } catch (error) {\n            console.error(\"❌ Transaction failed for \".concat(functionName, \":\"), error);\n            // Try to get more detailed error information\n            if (error.reason) {\n                console.error(\"Revert reason: \".concat(error.reason));\n            }\n            if (error.data) {\n                console.error(\"Error data: \".concat(error.data));\n            }\n            throw error;\n        }\n    }\n    /**\n   * Reset all providers (useful for troubleshooting)\n   */ static reset() {\n        this.reliableProviders = [];\n        this.browserProvider = null;\n        this.initialized = false;\n        console.log('🔄 FallbackProvider reset');\n    }\n}\nFallbackProvider.reliableProviders = [];\nFallbackProvider.browserProvider = null;\nFallbackProvider.initialized = false;\n/**\n * Convenience function for safe contract calls\n */ async function safeContractCall(contractAddress, abi, functionName) {\n    let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n    return FallbackProvider.safeCall(contractAddress, abi, functionName, args);\n}\n/**\n * Convenience function for safe transactions\n */ async function safeContractTransaction(contractAddress, abi, functionName) {\n    let args = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [], overrides = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n    return FallbackProvider.safeTransaction(contractAddress, abi, functionName, args, overrides);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/fallbackProvider.ts\n"));

/***/ })

});