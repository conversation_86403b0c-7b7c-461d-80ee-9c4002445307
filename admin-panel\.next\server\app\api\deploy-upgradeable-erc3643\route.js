/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/deploy-upgradeable-erc3643/route";
exports.ids = ["app/api/deploy-upgradeable-erc3643/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&page=%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&page=%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_deploy_upgradeable_erc3643_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/deploy-upgradeable-erc3643/route.ts */ \"(rsc)/./src/app/api/deploy-upgradeable-erc3643/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/deploy-upgradeable-erc3643/route\",\n        pathname: \"/api/deploy-upgradeable-erc3643\",\n        filename: \"route\",\n        bundlePath: \"app/api/deploy-upgradeable-erc3643/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\deploy-upgradeable-erc3643\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_deploy_upgradeable_erc3643_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&page=%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/deploy-upgradeable-erc3643/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/deploy-upgradeable-erc3643/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\n// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL ✅)\nconst WORKING_ERC3643_CONTRACTS = {\n    workingUnderlyingRegistry: \"0x1281a057881cbe94dc2a79561275aa6b35bf7854\",\n    workingWrapper: \"0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02\",\n    workingTokenExample: \"0x3F3e2E88542D26C22B7539b5B42328AA3e9DD303\"\n};\n// POST /api/deploy-upgradeable-erc3643 - Deploy upgradeable ERC-3643 token\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'name',\n            'symbol',\n            'decimals',\n            'maxSupply',\n            'adminAddress'\n        ];\n        for (const field of requiredFields){\n            if (body[field] === undefined || body[field] === null) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        const { name, symbol, decimals, maxSupply, adminAddress, network = 'amoy', upgradeTimelock = 30 // days\n         } = body;\n        console.log('🚀 DEPLOYING UPGRADEABLE ERC-3643 TOKEN');\n        console.log(`📋 Name: ${name}`);\n        console.log(`🏷️ Symbol: ${symbol}`);\n        console.log(`👑 Admin: ${adminAddress}`);\n        console.log(`⏰ Upgrade Timelock: ${upgradeTimelock} days`);\n        // Create deployment script with environment variables\n        const deploymentScript = `\nconst { ethers, upgrades } = require('hardhat');\n\nasync function deployUpgradeableToken() {\n  console.log('🚀 DEPLOYING UPGRADEABLE ERC-3643 TOKEN');\n  \n  try {\n    const [deployer] = await ethers.getSigners();\n    console.log('👤 Deployer:', deployer.address);\n    \n    // Token configuration\n    const tokenConfig = {\n      name: '${name}',\n      symbol: '${symbol}',\n      decimals: ${decimals},\n      maxSupply: ethers.parseUnits('${maxSupply}', ${decimals}),\n      admin: '${adminAddress}',\n      identityRegistry: '${WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry}',\n      compliance: ethers.ZeroAddress,\n      version: '1.0.0'\n    };\n\n    console.log('📦 Deploying upgradeable contract...');\n    \n    const UpgradeableERC3643Token = await ethers.getContractFactory('UpgradeableERC3643Token');\n    \n    const token = await upgrades.deployProxy(\n      UpgradeableERC3643Token,\n      [\n        tokenConfig.name,\n        tokenConfig.symbol,\n        tokenConfig.decimals,\n        tokenConfig.maxSupply,\n        tokenConfig.admin,\n        tokenConfig.identityRegistry,\n        tokenConfig.compliance,\n        tokenConfig.version\n      ],\n      {\n        initializer: 'initialize',\n        kind: 'uups'\n      }\n    );\n\n    await token.waitForDeployment();\n    const proxyAddress = await token.getAddress();\n    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);\n    \n    console.log('✅ Proxy deployed:', proxyAddress);\n    console.log('✅ Implementation deployed:', implementationAddress);\n    \n    // Verify functionality\n    const isVerified = await token.isVerified('${adminAddress}');\n    const upgradesRenounced = await token.upgradesRenounced();\n    const upgradeTimelock = await token.upgradeTimelock();\n    \n    console.log('🔍 Admin verified:', isVerified);\n    console.log('🔒 Upgrades renounced:', upgradesRenounced);\n    console.log('⏰ Upgrade timelock:', upgradeTimelock / 86400n, 'days');\n    \n    // Output JSON for parsing\n    console.log('DEPLOYMENT_RESULT:', JSON.stringify({\n      success: true,\n      proxyAddress: proxyAddress,\n      implementationAddress: implementationAddress,\n      adminVerified: isVerified,\n      upgradesRenounced: upgradesRenounced,\n      upgradeTimelock: upgradeTimelock.toString(),\n      identityRegistry: tokenConfig.identityRegistry,\n      compliance: tokenConfig.compliance\n    }));\n    \n  } catch (error) {\n    console.error('❌ Deployment failed:', error);\n    console.log('DEPLOYMENT_RESULT:', JSON.stringify({\n      success: false,\n      error: error.message\n    }));\n  }\n}\n\ndeployUpgradeableToken().catch(console.error);\n    `;\n        // Write the deployment script to a temporary file\n        const fs = (__webpack_require__(/*! fs */ \"fs\").promises);\n        const tempScriptPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), '..', 'Token', 'temp-deploy-upgradeable.js');\n        await fs.writeFile(tempScriptPath, deploymentScript);\n        console.log('📝 Created temporary deployment script');\n        // Execute the deployment\n        const tokenDir = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), '..', 'Token');\n        console.log('🔄 Executing deployment...');\n        try {\n            const { stdout, stderr } = await execAsync('npx hardhat run temp-deploy-upgradeable.js --network amoy --config hardhat.fresh.config.js', {\n                cwd: tokenDir,\n                timeout: 300000 // 5 minutes timeout\n            });\n            console.log('📊 Deployment output:', stdout);\n            if (stderr) {\n                console.log('⚠️ Deployment stderr:', stderr);\n            }\n            // Parse the deployment result\n            const resultMatch = stdout.match(/DEPLOYMENT_RESULT: (.+)/);\n            let deploymentResult = {\n                success: false,\n                error: 'Could not parse deployment result'\n            };\n            if (resultMatch) {\n                try {\n                    deploymentResult = JSON.parse(resultMatch[1]);\n                } catch (parseError) {\n                    console.error('Failed to parse deployment result:', parseError);\n                }\n            }\n            // Clean up temporary file\n            try {\n                await fs.unlink(tempScriptPath);\n            } catch (cleanupError) {\n                console.log('⚠️ Could not clean up temp file:', cleanupError.message);\n            }\n            if (deploymentResult.success) {\n                // Save the upgradeable deployment to database\n                try {\n                    const savedToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.token.create({\n                        data: {\n                            name: name,\n                            symbol: symbol,\n                            decimals: parseInt(decimals),\n                            maxSupply: maxSupply,\n                            address: deploymentResult.proxyAddress,\n                            adminAddress: adminAddress,\n                            network: network,\n                            tokenType: 'security',\n                            tokenPrice: '1.00 USD',\n                            currency: 'USD',\n                            hasKYC: true,\n                            isActive: true,\n                            deployedBy: adminAddress,\n                            selectedClaims: body.selectedClaims ? body.selectedClaims.join(',') : '1,4',\n                            deploymentNotes: `UPGRADEABLE ERC-3643 token deployed. Proxy: ${deploymentResult.proxyAddress}, Implementation: ${deploymentResult.implementationAddress}, Registry: ${deploymentResult.identityRegistry}, Upgrade Timelock: ${parseInt(deploymentResult.upgradeTimelock) / 86400} days, Upgrades Renounced: ${deploymentResult.upgradesRenounced}. Required Claims: ${body.selectedClaims ? body.selectedClaims.join(',') : '1,4'}`\n                        }\n                    });\n                    console.log('✅ Upgradeable token saved to database');\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        message: '🎉 Upgradeable ERC-3643 Token deployed successfully!',\n                        proxyAddress: deploymentResult.proxyAddress,\n                        implementationAddress: deploymentResult.implementationAddress,\n                        adminVerified: deploymentResult.adminVerified,\n                        upgradeInfo: {\n                            upgradesRenounced: deploymentResult.upgradesRenounced,\n                            upgradeTimelock: `${parseInt(deploymentResult.upgradeTimelock) / 86400} days`,\n                            canUpgrade: !deploymentResult.upgradesRenounced\n                        },\n                        databaseSaved: true,\n                        contracts: {\n                            proxy: deploymentResult.proxyAddress,\n                            implementation: deploymentResult.implementationAddress,\n                            identityRegistry: deploymentResult.identityRegistry,\n                            compliance: deploymentResult.compliance\n                        },\n                        tokenData: {\n                            name: name,\n                            symbol: symbol,\n                            decimals: decimals,\n                            maxSupply: maxSupply,\n                            adminAddress: adminAddress,\n                            network: network\n                        },\n                        explorerUrls: {\n                            proxy: `https://amoy.polygonscan.com/address/${deploymentResult.proxyAddress}`,\n                            implementation: `https://amoy.polygonscan.com/address/${deploymentResult.implementationAddress}`,\n                            identityRegistry: `https://amoy.polygonscan.com/address/${deploymentResult.identityRegistry}`\n                        },\n                        features: [\n                            '✅ Real deployed upgradeable ERC-3643 contract',\n                            '✅ UUPS proxy pattern for gas efficiency',\n                            '✅ Identity verification working',\n                            '✅ Admin properly verified',\n                            '✅ Optional upgrade renunciation',\n                            `✅ ${parseInt(deploymentResult.upgradeTimelock) / 86400}-day upgrade timelock`,\n                            '✅ Force transfer and freezing capabilities',\n                            '✅ Role-based access control',\n                            '✅ Institutional-grade security'\n                        ],\n                        upgradeManagement: {\n                            proposeUpgrade: 'Call proposeUpgrade(address) with UPGRADER_ROLE',\n                            executeUpgrade: 'Call executeUpgrade() after timelock expires',\n                            renounceUpgrades: 'Call renounceUpgrades() to permanently disable (IRREVERSIBLE)',\n                            increaseTimelock: 'Call increaseTimelock(uint256) to increase security'\n                        }\n                    });\n                } catch (dbError) {\n                    console.error('❌ Database save failed:', dbError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        message: '⚠️ Upgradeable token deployed but database save failed',\n                        proxyAddress: deploymentResult.proxyAddress,\n                        implementationAddress: deploymentResult.implementationAddress,\n                        adminVerified: deploymentResult.adminVerified,\n                        databaseSaved: false,\n                        error: dbError.message\n                    });\n                }\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Upgradeable token deployment failed',\n                    details: deploymentResult.error,\n                    deploymentOutput: stdout,\n                    deploymentErrors: stderr\n                }, {\n                    status: 500\n                });\n            }\n        } catch (execError) {\n            console.error('❌ Deployment execution failed:', execError);\n            // Clean up temporary file\n            try {\n                await fs.unlink(tempScriptPath);\n            } catch (cleanupError) {\n                console.log('⚠️ Could not clean up temp file:', cleanupError.message);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Failed to execute upgradeable deployment',\n                details: execError.message\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('❌ Upgradeable ERC-3643 deployment failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to deploy upgradeable ERC-3643 token',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/deploy-upgradeable-erc3643/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&page=%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdeploy-upgradeable-erc3643%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();