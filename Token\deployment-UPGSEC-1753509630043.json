{"success": true, "network": "hardhat", "chainId": "31337", "deployer": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "contracts": {"proxy": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "implementation": "0x5FbDB2315678afecb367f032d93F642f64180aa3", "identityRegistry": "0x1281a057881cbe94dc2a79561275aa6b35bf7854", "compliance": "0x0000000000000000000000000000000000000000"}, "tokenInfo": {"name": "Upgradeable Security Token", "symbol": "UPGSEC", "decimals": 18, "maxSupply": "1000000.0", "admin": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "version": "1.0.0"}, "upgradeInfo": {"upgradesRenounced": false, "upgradeTimelock": "30 days", "pendingUpgrade": false}, "features": ["✅ ERC-3643 Compliant", "✅ Upgradeable (UUPS Pattern)", "✅ Optional Upgrade Renunciation", "✅ Timelock Protection (30 days)", "✅ Force Transfer (Custody)", "✅ Account Freezing", "✅ Partial Token Freezing", "✅ Role-Based Access Control", "✅ Pausable", "✅ Identity Registry Integration", "✅ Institutional Grade Security"], "explorerUrls": {"proxy": "https://amoy.polygonscan.com/address/0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "implementation": "https://amoy.polygonscan.com/address/0x5FbDB2315678afecb367f032d93F642f64180aa3", "identityRegistry": "https://amoy.polygonscan.com/address/0x1281a057881cbe94dc2a79561275aa6b35bf7854"}, "nextSteps": ["1. Verify contracts on block explorer", "2. Test basic functionality (mint, transfer, freeze)", "3. Test upgrade functionality", "4. Set up governance for upgrade decisions", "5. Consider renouncing upgrades when ready for production"]}