"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/app/reliable-tokens/page.tsx":
/*!******************************************!*\
  !*** ./src/app/reliable-tokens/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReliableTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _components_AgentsList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AgentsList */ \"(app-pages-browser)/./src/components/AgentsList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Default token address from environment (fallback)\nconst DEFAULT_SECURITY_TOKEN_CORE_ADDRESS = \"0x4538047fB565D809856CBd368521CD90A07b57fB\" || 0;\nfunction ReliableTokensContent() {\n    var _tokenInfo_totalSupply, _tokenInfo_maxSupply, _tokenInfo_metadata_tokenPrice, _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata_tokenPrice1, _tokenInfo_metadata2, _tokenInfo_metadata3;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const urlTokenAddress = searchParams.get('token');\n    // Use URL parameter if provided, otherwise fall back to environment variable\n    const SECURITY_TOKEN_CORE_ADDRESS = urlTokenAddress || DEFAULT_SECURITY_TOKEN_CORE_ADDRESS;\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pauseFunctionAvailable, setPauseFunctionAvailable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('management');\n    // Whitelist management\n    const [whitelistedClients, setWhitelistedClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [whitelistLoading, setWhitelistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [whitelistError, setWhitelistError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenFromDb, setTokenFromDb] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ERC-3643 wrapper detection\n    const knownERC3643Wrappers = [\n        '******************************************',\n        '******************************************',\n        '0x6DB181232Dd52faAD98b4973bd72f24263038D01'\n    ];\n    const knownUnderlyingToken = '0xc1A404b0Fa57C4E3103C565a12A06d8197Db0924';\n    const isERC3643Wrapper = knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS);\n    const underlyingTokenAddress = isERC3643Wrapper ? knownUnderlyingToken : SECURITY_TOKEN_CORE_ADDRESS;\n    const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n    // Debug logging\n    console.log('🔍 ReliableTokensContent rendered');\n    console.log('🔍 URL token parameter:', urlTokenAddress);\n    console.log('🔍 Using token address:', SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🔍 Default address:', DEFAULT_SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🛡️ Is ERC-3643 wrapper:', isERC3643Wrapper);\n    console.log('🔗 Data address for metadata calls:', dataAddress);\n    // Load token information using fallback-first approach\n    const loadTokenInfo = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🔄 Loading token info with fallback-first approach...');\n            // Check if this is an ERC-3643 wrapper\n            const knownERC3643Wrappers = [\n                '******************************************',\n                '******************************************',\n                '0x6DB181232Dd52faAD98b4973bd72f24263038D01'\n            ];\n            const knownUnderlyingToken = '0xc1A404b0Fa57C4E3103C565a12A06d8197Db0924';\n            let isERC3643Wrapper = false;\n            let underlyingTokenAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            if (knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS)) {\n                console.log('🛡️ Detected known ERC-3643 wrapper address');\n                isERC3643Wrapper = true;\n                underlyingTokenAddress = knownUnderlyingToken;\n                console.log('🔗 Using known underlying token for data calls:', underlyingTokenAddress);\n            }\n            // For ERC-3643 wrappers, get basic info from wrapper but maxSupply/metadata from underlying\n            const basicInfoAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n            console.log('📊 Getting basic info from:', basicInfoAddress);\n            console.log('📊 Getting data (maxSupply, metadata) from:', dataAddress);\n            // Use safe contract calls that automatically handle fallbacks\n            // Core functions that should always exist\n            const [name, symbol, version, totalSupply, decimals] = await Promise.all([\n                (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'name'),\n                (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'symbol'),\n                (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'version'),\n                (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'totalSupply'),\n                (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'decimals')\n            ]);\n            // Optional functions that may not exist on all contracts\n            const paused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeOptionalContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused', false // fallback value\n            );\n            // Check if pause function is available by trying to call it\n            let pauseAvailable = false;\n            try {\n                await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                pauseAvailable = true;\n                console.log('✅ paused() function available');\n            } catch (error) {\n                pauseAvailable = false;\n                console.log('⚠️ paused() function not available');\n            }\n            setPauseFunctionAvailable(pauseAvailable);\n            // Get maxSupply and metadata from the appropriate address\n            let maxSupply;\n            let metadata;\n            try {\n                maxSupply = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'maxSupply');\n                console.log('✅ maxSupply loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ maxSupply failed, using 0:', error);\n                maxSupply = BigInt(0);\n            }\n            try {\n                metadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                console.log('✅ metadata loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ metadata failed, using defaults:', error);\n                metadata = {\n                    tokenPrice: '0',\n                    currency: 'USD',\n                    bonusTiers: ''\n                };\n            }\n            setTokenInfo({\n                name,\n                symbol,\n                version,\n                totalSupply,\n                maxSupply,\n                decimals,\n                paused,\n                metadata: {\n                    tokenPrice: metadata.tokenPrice || '0',\n                    currency: metadata.currency || 'USD',\n                    bonusTiers: metadata.bonusTiers || ''\n                }\n            });\n            console.log('✅ Token info loaded successfully');\n        } catch (error) {\n            console.error('❌ Failed to load token info:', error);\n            setError(\"Failed to load token information: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Connect wallet\n    const connectWallet = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                await window.ethereum.request({\n                    method: 'eth_requestAccounts'\n                });\n                setWalletConnected(true);\n                setSuccess('Wallet connected successfully');\n            } else {\n                setError('MetaMask not found. Please install MetaMask to use this feature.');\n            }\n        } catch (error) {\n            setError(\"Failed to connect wallet: \".concat(error.message));\n        }\n    };\n    // Toggle pause state using fallback-first approach\n    const togglePause = async ()=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            const currentPaused = tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused;\n            const isPausing = !currentPaused;\n            const functionName = isPausing ? 'pause' : 'unpause';\n            console.log(\"\\uD83D\\uDD04 \".concat(isPausing ? 'Pausing' : 'Unpausing', \" token...\"));\n            // Get real-time state to ensure accuracy (if paused function is available)\n            const realTimePaused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeOptionalContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused', currentPaused || false // fallback to current state\n            );\n            console.log('✅ Real-time paused state:', realTimePaused);\n            // Validate state change is needed\n            if (isPausing && realTimePaused) {\n                throw new Error('Token is already paused. Refreshing page data...');\n            }\n            if (!isPausing && !realTimePaused) {\n                throw new Error('Token is already unpaused. Refreshing page data...');\n            }\n            // Execute the transaction using safe transaction approach\n            const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, functionName, []);\n            console.log(\"✅ \".concat(functionName, \" transaction sent: \").concat(tx.hash));\n            // Wait for confirmation\n            const receipt = await tx.wait();\n            console.log(\"✅ \".concat(functionName, \" confirmed in block \").concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n            setSuccess(\"Token \".concat(isPausing ? 'paused' : 'unpaused', \" successfully!\"));\n            // Refresh token info\n            await loadTokenInfo();\n        } catch (error) {\n            var _error_message, _error_message1;\n            console.error(\"❌ Toggle pause failed:\", error);\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('already paused')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('already unpaused'))) {\n                setError(\"\".concat(error.message));\n                // Auto-refresh data after state conflict\n                setTimeout(async ()=>{\n                    await loadTokenInfo();\n                    setError(null);\n                }, 2000);\n            } else {\n                setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n            }\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Mint tokens using fallback-first approach\n    const mintTokens = async (amount, recipient)=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            console.log(\"\\uD83D\\uDD04 Minting \".concat(amount, \" tokens to \").concat(recipient, \"...\"));\n            // 🚨 CRITICAL SECURITY CHECK: Verify recipient is whitelisted before minting\n            console.log('🔒 SECURITY CHECK: Verifying recipient is whitelisted...');\n            try {\n                const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'isWhitelisted', [\n                    recipient\n                ]);\n                if (!isWhitelisted) {\n                    throw new Error(\"SECURITY VIOLATION: Recipient address \".concat(recipient, \" is not whitelisted. Only whitelisted addresses can receive tokens for ERC-3643 compliance.\"));\n                }\n                console.log('✅ SECURITY PASSED: Recipient is whitelisted, proceeding with mint');\n            } catch (whitelistError) {\n                console.error('❌ SECURITY CHECK FAILED:', whitelistError);\n                throw new Error(\"Security validation failed: \".concat(whitelistError.message));\n            }\n            // Convert amount based on decimals\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            const mintAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n            // Execute mint transaction\n            const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'mint', [\n                recipient,\n                mintAmount\n            ]);\n            console.log(\"✅ Mint transaction sent: \".concat(tx.hash));\n            const receipt = await tx.wait();\n            console.log(\"✅ Mint confirmed in block \".concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(recipient, \"!\"));\n            // Refresh token info\n            await loadTokenInfo();\n        } catch (error) {\n            console.error(\"❌ Mint failed:\", error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Load token from database to get the ID for whitelist queries\n    const loadTokenFromDatabase = async ()=>{\n        if (!SECURITY_TOKEN_CORE_ADDRESS) return;\n        try {\n            const response = await fetch('/api/tokens');\n            if (response.ok) {\n                const data = await response.json();\n                // Handle new API response format\n                const tokens = data.success && Array.isArray(data.tokens) ? data.tokens : Array.isArray(data) ? data : [];\n                // Find token by address\n                const token = tokens.find((t)=>t.address.toLowerCase() === SECURITY_TOKEN_CORE_ADDRESS.toLowerCase());\n                if (token) {\n                    setTokenFromDb(token);\n                    console.log('✅ Token found in database:', token);\n                } else {\n                    console.log('⚠️ Token not found in database, checking all available tokens:', tokens.map((t)=>t.address));\n                }\n            }\n        } catch (error) {\n            console.log('⚠️ Error loading token from database:', error);\n            setWhitelistError('Failed to load token from database');\n        }\n    };\n    // Load whitelisted clients for this token\n    const loadWhitelistedClients = async ()=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) {\n            setWhitelistError('Token not found in database');\n            return;\n        }\n        setWhitelistLoading(true);\n        setWhitelistError(null);\n        try {\n            console.log('🔍 Loading whitelisted clients for token ID:', tokenFromDb.id);\n            const response = await fetch(\"/api/tokens?whitelistedInvestors=true&tokenId=\".concat(tokenFromDb.id));\n            console.log('📥 Response status:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('❌ API Error:', errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📊 API Response:', data);\n            if (data.success) {\n                var _data_investors;\n                setWhitelistedClients(data.investors || []);\n                console.log('✅ Loaded whitelisted clients:', ((_data_investors = data.investors) === null || _data_investors === void 0 ? void 0 : _data_investors.length) || 0);\n            } else {\n                throw new Error(data.error || 'Failed to load whitelisted clients');\n            }\n        } catch (err) {\n            console.error('❌ Error loading whitelisted clients:', err);\n            setWhitelistError(err.message || 'Failed to load whitelisted clients');\n            setWhitelistedClients([]); // Clear the list on error\n        } finally{\n            setWhitelistLoading(false);\n        }\n    };\n    // Remove client from whitelist\n    const handleRemoveFromWhitelist = async (clientId)=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) return;\n        if (!confirm('Are you sure you want to remove this client from the whitelist?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/clients/\".concat(clientId, \"/token-approval\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: tokenFromDb.id,\n                    whitelistApproved: false,\n                    approvalStatus: 'REJECTED',\n                    notes: 'Removed from whitelist by admin'\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || 'Failed to remove from whitelist');\n            }\n            // Refresh the clients list\n            loadWhitelistedClients();\n            setSuccess('Client removed from whitelist successfully');\n        } catch (err) {\n            console.error('Error removing from whitelist:', err);\n            setError(\"Error: \".concat(err.message));\n        }\n    };\n    // Load token info on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            loadTokenInfo();\n            loadTokenFromDatabase();\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    // Load whitelist when token from DB is available and whitelist tab is active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if (tokenFromDb && activeTab === 'whitelist') {\n                loadWhitelistedClients();\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], [\n        tokenFromDb,\n        activeTab\n    ]);\n    // Check wallet connection on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if ( true && window.ethereum) {\n                window.ethereum.request({\n                    method: 'eth_accounts'\n                }).then({\n                    \"ReliableTokensContent.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setWalletConnected(true);\n                        }\n                    }\n                }[\"ReliableTokensContent.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Reliable Token Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bulletproof token management with fallback-first architecture - no more network errors!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"Connection Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-2 \".concat(walletConnected ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Wallet: \",\n                                                            walletConnected ? 'Connected' : 'Not Connected'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Network: Reliable RPC Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 mr-2\",\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"text-red-600 hover:text-red-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 mr-2\",\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSuccess(null),\n                                className: \"text-green-600 hover:text-green-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 557,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('management'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'management' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"\\uD83D\\uDEE1️ Token Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('whitelist'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'whitelist' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: [\n                                        \"\\uD83D\\uDC65 Whitelisted Clients\",\n                                        whitelistedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full\",\n                                            children: whitelistedClients.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Token Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: loadTokenInfo,\n                                            disabled: loading,\n                                            className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50\",\n                                            children: loading ? 'Loading...' : 'Refresh'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 11\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Loading token information...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.symbol\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Version\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: tokenInfo.version\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Decimals\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: tokenInfo.decimals\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Total Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_totalSupply = tokenInfo.totalSupply) === null || _tokenInfo_totalSupply === void 0 ? void 0 : _tokenInfo_totalSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_maxSupply = tokenInfo.maxSupply) === null || _tokenInfo_maxSupply === void 0 ? void 0 : _tokenInfo_maxSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                    children: tokenInfo.paused ? '⏸️ Paused' : '▶️ Active'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Token Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice = _tokenInfo_metadata.tokenPrice) === null || _tokenInfo_metadata_tokenPrice === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice.toString()) || '0',\n                                                        \".00 \",\n                                                        ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.currency) || 'USD'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No token information available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                pauseFunctionAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Pause Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Token is currently paused. Unpause to allow transfers.' : 'Token is currently active. Pause to stop all transfers.'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePause,\n                                            disabled: actionLoading || !tokenInfo,\n                                            className: \"w-full px-4 py-2 rounded-lg font-medium disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                            children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '▶️ Unpause Token' : '⏸️ Pause Token'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"mintRecipient\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"mintAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _document_getElementById, _document_getElementById1;\n                                                        const recipient = (_document_getElementById = document.getElementById('mintRecipient')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        const amount = (_document_getElementById1 = document.getElementById('mintAmount')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                        if (recipient && amount) {\n                                                            mintTokens(amount, recipient);\n                                                        } else {\n                                                            setError('Please enter both recipient address and amount');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading || !tokenInfo,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: actionLoading ? 'Processing...' : '🪙 Mint Tokens'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Whitelist Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"whitelistAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Adding to whitelist via API...');\n                                                                        const response = await fetch('/api/admin/add-to-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist add successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist add failed:', error);\n                                                                        setError(\"Failed to add to whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: \"✅ Add (API)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Removing from whitelist via API...');\n                                                                        const response = await fetch('/api/admin/remove-from-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist remove successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist remove failed:', error);\n                                                                        setError(\"Failed to remove from whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: \"❌ Remove (API)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'isWhitelisted', [\n                                                                    address\n                                                                ]);\n                                                                setSuccess(\"Address \".concat(address, \" is \").concat(isWhitelisted ? '✅ whitelisted' : '❌ not whitelisted'));\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to check whitelist: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD0D Check Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Balance Checker\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address to Check\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"balanceAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('balanceAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                const balance = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'balanceOf', [\n                                                                    address\n                                                                ]);\n                                                                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                const formattedBalance = decimals > 0 ? (Number(balance) / Math.pow(10, decimals)).toString() : (balance === null || balance === void 0 ? void 0 : balance.toString()) || '0';\n                                                                setSuccess(\"Balance: \".concat(formattedBalance, \" \").concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.symbol) || 'tokens'));\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to get balance: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Check Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Burn Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount to Burn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"burnAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 967,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"⚠️ This will permanently destroy tokens from your wallet. This action cannot be undone.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const amount = (_document_getElementById = document.getElementById('burnAmount')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (amount) {\n                                                            if (confirm(\"Are you sure you want to burn \".concat(amount, \" tokens? This action cannot be undone.\"))) {\n                                                                try {\n                                                                    setActionLoading(true);\n                                                                    const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                    const burnAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'burn', [\n                                                                        burnAmount\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Successfully burned \".concat(amount, \" tokens\"));\n                                                                    await loadTokenInfo();\n                                                                } catch (error) {\n                                                                    setError(\"Failed to burn tokens: \".concat(error.message));\n                                                                } finally{\n                                                                    setActionLoading(false);\n                                                                }\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an amount to burn');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD25 Burn Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Agent Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Agent Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1027,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"agentAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1030,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully added \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to add agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➕ Add Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully removed \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to remove agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➖ Remove Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1083,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1037,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Note: Since AGENT_MANAGER module is not registered, we can't enumerate agents\n                                                                // Instead, we'll show how to check if specific addresses have AGENT_ROLE\n                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                // Check some common addresses for demonstration\n                                                                const addressesToCheck = [\n                                                                    '0x56f3726C92B8B92a6ab71983886F91718540d888',\n                                                                    '0x1f1a17fe870f5A0EEf1274247c080478E3d0840A' // Test address\n                                                                ];\n                                                                const agentResults = [];\n                                                                for (const address of addressesToCheck){\n                                                                    try {\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            AGENT_ROLE,\n                                                                            address\n                                                                        ]);\n                                                                        if (hasRole) {\n                                                                            agentResults.push(\"✅ \".concat(address));\n                                                                        }\n                                                                    } catch (error) {\n                                                                    // Skip addresses that cause errors\n                                                                    }\n                                                                }\n                                                                if (agentResults.length === 0) {\n                                                                    setSuccess('No agents found in checked addresses. Use \"Check Role\" to verify specific addresses.');\n                                                                } else {\n                                                                    setSuccess(\"Agents found: \".concat(agentResults.join(', ')));\n                                                                }\n                                                            } catch (error) {\n                                                                setError(\"Failed to get agents: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        },\n                                                        disabled: actionLoading,\n                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                        children: \"\\uD83D\\uDC65 Check Known Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1130,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-3\",\n                                                            children: \"\\uD83D\\uDCCB Current Agents List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1193,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentsList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1192,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1022,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Role Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1201,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1205,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"roleAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1208,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1204,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"roleSelect\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"AGENT_ROLE\",\n                                                                            children: \"AGENT_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1223,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"TRANSFER_MANAGER_ROLE\",\n                                                                            children: \"TRANSFER_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1224,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"MODULE_MANAGER_ROLE\",\n                                                                            children: \"MODULE_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DEFAULT_ADMIN_ROLE\",\n                                                                            children: \"DEFAULT_ADMIN_ROLE (⚠️ Dangerous)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1226,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1219,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1215,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1203,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully granted \".concat(roleSelect, \" to \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to grant role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"✅ Grant Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1231,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully revoked \".concat(roleSelect, \" from \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to revoke role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"❌ Revoke Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1278,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        setSuccess(\"Address \".concat(address, \" \").concat(hasRole ? '✅ HAS' : '❌ DOES NOT HAVE', \" \").concat(roleSelect));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to check role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDD0D Check Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1325,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Role Descriptions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"AGENT_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can perform basic token operations\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 84\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"TRANSFER_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can manage transfers and compliance\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 96\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"MODULE_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can register/unregister modules\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 90\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DEFAULT_ADMIN_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1378,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Full admin access (use with extreme caution)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1373,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1372,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1202,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1200,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Token Metadata\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1386,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Token Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1390,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newTokenPrice\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"100.00 USD\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1393,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1389,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Bonus Tiers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1401,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newBonusTiers\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"Early: 20%, Standard: 10%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1404,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1400,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1388,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Details/Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1413,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"newTokenDetails\",\n                                                            rows: 3,\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Token type: Carbon Credits, Debt Securities, Real Estate, etc. Additional details...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1416,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1412,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Image URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1424,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"url\",\n                                                            id: \"newTokenImageUrl\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"https://example.com/token-logo.png\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1423,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1, _document_getElementById2;\n                                                                const price = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const tiers = (_document_getElementById1 = document.getElementById('newBonusTiers')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                const details = (_document_getElementById2 = document.getElementById('newTokenDetails')) === null || _document_getElementById2 === void 0 ? void 0 : _document_getElementById2.value;\n                                                                if (price || tiers || details) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Get current values if not provided\n                                                                        const currentPrice = price || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenPrice) || '';\n                                                                        const currentTiers = tiers || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.bonusTiers) || '';\n                                                                        const currentDetails = details || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenDetails) || '';\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                            currentPrice,\n                                                                            currentTiers,\n                                                                            currentDetails\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token metadata!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update metadata: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter at least one field to update');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDCDD Update Metadata\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1435,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const imageUrl = (_document_getElementById = document.getElementById('newTokenImageUrl')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (imageUrl) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenImageUrl', [\n                                                                            imageUrl\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token image!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update image: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an image URL');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDDBC️ Update Image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1476,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1434,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1385,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Quick Price Update\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1516,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Current Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1519,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice1 = _tokenInfo_metadata2.tokenPrice) === null || _tokenInfo_metadata_tokenPrice1 === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice1.toString()) || '0',\n                                                                \".00 \",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.currency) || 'USD'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1522,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1518,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"New Price (USD)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1527,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"newTokenPrice\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"150\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1530,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1526,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const newPrice = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (newPrice) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                console.log('🔄 Updating token price using fallback-first approach...');\n                                                                // Try direct updateTokenPrice first\n                                                                try {\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenPrice', [\n                                                                        newPrice\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD\"));\n                                                                } catch (directError) {\n                                                                    console.log('Direct method failed, trying fallback...');\n                                                                    // Fallback to updateTokenMetadata\n                                                                    const currentMetadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                                                                    const currentBonusTiers = currentMetadata[1];\n                                                                    const currentDetails = currentMetadata[2];\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                        newPrice,\n                                                                        currentBonusTiers,\n                                                                        currentDetails\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD (via fallback method)\"));\n                                                                }\n                                                                // Refresh token info\n                                                                await loadTokenInfo();\n                                                            } catch (error) {\n                                                                setError(\"Failed to update token price: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter a new price');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Update Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1537,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1517,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1515,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"\\uD83D\\uDEE1️ Fallback-First Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1605,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Network Resilience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1608,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Multiple RPC endpoints\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1610,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic failover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1611,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Connection testing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1612,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1609,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1607,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Error Elimination\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1616,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"missing revert data\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1618,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"Internal JSON-RPC\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1619,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Reliable contract calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1620,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1617,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1615,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ User Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1624,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Seamless operation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1626,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Clear error messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1627,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic recovery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1628,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1625,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1623,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1606,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1604,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"\\uD83D\\uDC65 Whitelisted Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1643,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Manage clients who are approved to hold \",\n                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.name) || 'this',\n                                                    \" tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1644,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1642,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: whitelistedClients.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1650,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Whitelisted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1651,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1649,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                                children: \"➕ Add Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1648,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1641,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1640,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md\",\n                            children: whitelistLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1667,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"Loading whitelisted clients...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1668,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1666,\n                                columnNumber: 17\n                            }, this) : whitelistError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-600 text-xl\",\n                                                children: \"❌\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1674,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-red-800\",\n                                                        children: \"Error Loading Whitelist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1676,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700\",\n                                                        children: whitelistError\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1677,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1675,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1673,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1672,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1671,\n                                columnNumber: 17\n                            }, this) : whitelistedClients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1684,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Whitelisted Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1685,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"No clients have been whitelisted for this token yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1686,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium\",\n                                        children: \"➕ Add First Client\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1689,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1683,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1701,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Wallet Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1704,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"KYC Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1707,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Approved Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1710,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1713,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1700,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1699,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedClients.map((client)=>{\n                                                var _client_tokenApproval;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: [\n                                                                            client.firstName,\n                                                                            \" \",\n                                                                            client.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1723,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: client.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1726,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1721,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 font-mono\",\n                                                                children: client.walletAddress\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1730,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1729,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.kycStatus === 'APPROVED' ? 'bg-green-100 text-green-800' : client.kycStatus === 'REJECTED' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                children: client.kycStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1735,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1734,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: ((_client_tokenApproval = client.tokenApproval) === null || _client_tokenApproval === void 0 ? void 0 : _client_tokenApproval.approvedAt) ? new Date(client.tokenApproval.approvedAt).toLocaleDateString() : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1745,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveFromWhitelist(client.id),\n                                                                    className: \"text-red-600 hover:text-red-900 mr-4\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1751,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/clients/\".concat(client.id),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1757,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1750,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1720,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1718,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1698,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1697,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1664,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 1638,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 497,\n        columnNumber: 5\n    }, this);\n}\n_s(ReliableTokensContent, \"fPfQsL7eZ6ZZw2fT+4Mbxc/fALE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ReliableTokensContent;\nfunction ReliableTokensPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1782,\n                        columnNumber: 9\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading token management...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1783,\n                        columnNumber: 9\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                lineNumber: 1781,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1780,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReliableTokensContent, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1786,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 1780,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ReliableTokensPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ReliableTokensContent\");\n$RefreshReg$(_c1, \"ReliableTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reliable-tokens/page.tsx\n"));

/***/ })

});