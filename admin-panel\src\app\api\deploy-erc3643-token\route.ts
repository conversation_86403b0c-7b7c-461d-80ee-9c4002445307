import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import { prisma } from '../../../lib/prisma';

// Import contract artifacts
import SecurityTokenCoreArtifact from '../../../contracts/SecurityTokenCore.json';
import ERC3643TokenWrapperArtifact from '../../../contracts/ERC3643TokenWrapper.json';

// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL ✅)
const WORKING_ERC3643_CONTRACTS = {
  // ✅ WORKING - Core functional components
  workingUnderlyingRegistry: "******************************************", // ✅ Admin verified
  workingWrapper: "******************************************",           // ✅ Admin verified
  workingTokenExample: "******************************************",      // ✅ Fully functional

  // ✅ WORKING - Supporting components
  trustedIssuersRegistry: "******************************************",
  claimTopicsRegistry: "******************************************",
  identityFactory: "******************************************",

  // ❌ BROKEN - DO NOT USE (kept for reference only)
  BROKEN_identityRegistryWrapper: "0x6CBea7Bc5032949005cA6e7c529Cecdc5e305f5c", // ❌ Broken wrapper
  BROKEN_complianceWrapper: "0xb22229354f8EEf867ee35D36fc2830278394588c"        // ❌ Broken compliance
};

const RPC_URL = process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/';
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

if (!PRIVATE_KEY) {
  console.error('CONTRACT_ADMIN_PRIVATE_KEY not set in environment variables');
}

// For now, let's skip the proxy pattern and deploy a modified version
// We'll create a simple factory approach instead

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'symbol', 'decimals', 'maxSupply', 'adminAddress'];
    for (const field of requiredFields) {
      if (body[field] === undefined || body[field] === null) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    if (!PRIVATE_KEY) {
      return NextResponse.json(
        { error: 'Server configuration error: Private key not configured' },
        { status: 500 }
      );
    }

    // Create provider and signer
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const signer = new ethers.Wallet(PRIVATE_KEY, provider);

    console.log('Deploying ERC-3643 token with params:', {
      name: body.name,
      symbol: body.symbol,
      decimals: body.decimals,
      maxSupply: body.maxSupply,
      adminAddress: body.adminAddress,
      enableERC3643: body.enableERC3643
    });

    const deploymentResult: any = {
      transactionHashes: [],
      contracts: {}
    };

    // Deploy ERC-3643 Compliant Token (always enabled now)
    console.log('🚀 Deploying ERC-3643 Compliant Token...');

    try {
      // Prepare token data
      const tokenData = {
        name: body.name,
        symbol: body.symbol,
        decimals: body.decimals,
        maxSupply: body.maxSupply,
        tokenPrice: `${body.tokenPrice || '1.00'} ${body.currency || 'USD'}`,
        bonusTiers: body.bonusTiers || 'Early: 10%, Standard: 5%, Late: 0%',
        tokenDetails: body.tokenDetails || `ERC-3643 compliant security token: ${body.name}`,
        tokenImageUrl: body.tokenImageUrl || ''
      };

      console.log('🎯 Token parameters:', tokenData);

      // Deploy the new ERC-3643 compliant token
      const erc3643Result = await deployERC3643CompliantToken(
        tokenData,
        body.adminAddress,
        signer
      );

      console.log('✅ ERC-3643 compliant token deployment result:', erc3643Result);

      // Set the primary token address to the ERC-3643 compliant token
      deploymentResult.contracts.securityTokenCore = erc3643Result.tokenWrapper;
      deploymentResult.contracts.erc3643TokenWrapper = erc3643Result.tokenWrapper;
      deploymentResult.contracts.erc3643Components = {
        tokenWrapper: erc3643Result.tokenWrapper,
        identityRegistryWrapper: erc3643Result.identityRegistryWrapper,
        complianceWrapper: erc3643Result.complianceWrapper,
        trustedIssuersRegistry: erc3643Result.trustedIssuersRegistry,
        claimTopicsRegistry: erc3643Result.claimTopicsRegistry,
        identityFactory: erc3643Result.identityFactory,
        implementationAddress: erc3643Result.implementationAddress,
        isERC3643Compliant: true
      };

      deploymentResult.transactionHashes.push(...erc3643Result.transactionHashes);
      console.log('✅ ERC-3643 compliant token deployed successfully!');
      console.log('✅ Token address:', erc3643Result.tokenWrapper);

    } catch (error) {
      console.error('❌ ERC-3643 compliant token deployment failed:', error);
      console.error('❌ Error details:', {
        name: error.name,
        message: error.message,
        code: error.code,
        reason: error.reason
      });

      // Throw the error so the user knows deployment failed
      throw new Error(`ERC-3643 compliant token deployment failed: ${error.message}`);
    }

    // Step 3: Save to database
    console.log('Saving ERC-3643 compliant token to database...');

    const primaryTokenAddress = deploymentResult.contracts.erc3643TokenWrapper;
    const tokenPriceWithCurrency = `${body.tokenPrice || '1.00'} ${body.currency || 'USD'}`;

    // Get block number from transaction hash if available
    let blockNumber = null;
    if (deploymentResult.transactionHashes && deploymentResult.transactionHashes.length > 0) {
      try {
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const receipt = await provider.getTransactionReceipt(deploymentResult.transactionHashes[0]);
        if (receipt) {
          blockNumber = receipt.blockNumber.toString();
        }
      } catch (error) {
        console.log('Could not fetch block number:', error.message);
      }
    }

    const tokenData = {
      address: primaryTokenAddress,
      name: body.name,
      symbol: body.symbol,
      decimals: parseInt(body.decimals),
      maxSupply: body.maxSupply.toString(),
      totalSupply: '0',
      tokenType: 'ERC-3643 Compliant Security Token',
      tokenPrice: tokenPriceWithCurrency,
      currency: body.currency || 'USD',
      bonusTiers: body.bonusTiers || null,
      tokenImageUrl: body.tokenImageUrl || null,
      network: 'amoy',
      hasKYC: true,
      isActive: true,
      adminAddress: body.adminAddress,
      deployedBy: body.adminAddress,
      selectedClaims: body.selectedClaims ? body.selectedClaims.join(',') : '1,4', // Default: KYC + Qualification
      deploymentNotes: `Fully ERC-3643 compliant security token with built-in identity registry and compliance module support. Includes identityRegistry(), compliance(), and onchainID() functions. Required Claims: ${body.selectedClaims ? body.selectedClaims.join(',') : '1,4'}. ERC-3643 Components: ${JSON.stringify(deploymentResult.contracts.erc3643Components)}`,
      transactionHash: deploymentResult.transactionHashes[0] || null,
      blockNumber: blockNumber
    };

    // Retry database save with better error handling
    let dbSaveSuccess = false;
    let dbSaveAttempts = 0;
    const maxDbSaveAttempts = 3;

    while (!dbSaveSuccess && dbSaveAttempts < maxDbSaveAttempts) {
      dbSaveAttempts++;

      try {
        console.log(`🗄️ Attempting to save token to database (attempt ${dbSaveAttempts}/${maxDbSaveAttempts})...`);

        // Check if token already exists first
        const existingToken = await prisma.token.findUnique({
          where: { address: primaryTokenAddress }
        });

        if (existingToken) {
          console.log('✅ Token already exists in database:', existingToken.id);
          dbSaveSuccess = true;
          break;
        }

        // Try to create the token
        console.log('📋 Token data:', JSON.stringify(tokenData, null, 2));

        const newToken = await prisma.token.create({
          data: tokenData
        });

        console.log('✅ Token saved to database successfully!');
        console.log('🆔 Database ID:', newToken.id);
        console.log('🔗 Token Address:', newToken.address);

        dbSaveSuccess = true;

      } catch (dbError: any) {
        console.error(`❌ Database save attempt ${dbSaveAttempts} failed:`, dbError.message);

        // Handle specific errors
        if (dbError.code === 'P2002') {
          console.error('❌ Unique constraint violation - token might already exist');
          console.error('❌ Conflicting field:', dbError.meta?.target);

          // If it's a unique constraint on address, the token might already exist
          if (dbError.meta?.target?.includes('address')) {
            console.log('🔍 Checking if token already exists...');
            try {
              const existingToken = await prisma.token.findUnique({
                where: { address: primaryTokenAddress }
              });
              if (existingToken) {
                console.log('✅ Token already exists in database:', existingToken.id);
                dbSaveSuccess = true;
                break;
              }
            } catch (checkError) {
              console.error('❌ Error checking existing token:', checkError.message);
            }
          }
        }

        if (dbSaveAttempts >= maxDbSaveAttempts) {
          console.error('❌ All database save attempts failed');
          console.error('❌ Final error details:', {
            name: dbError.name,
            message: dbError.message,
            code: dbError.code,
            meta: dbError.meta
          });

          // Continue anyway - the token is deployed
          console.warn('⚠️ Token deployed successfully but not saved to database');
        } else {
          console.log(`⏳ Retrying in 2 seconds...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // Return success response
    const isERC3643Deployed = !!deploymentResult.contracts.erc3643TokenWrapper;

    return NextResponse.json({
      success: true,
      message: dbSaveSuccess ?
        '🎉 ERC-3643 Compliant Security Token deployed and saved to database successfully!' :
        '🎉 ERC-3643 Compliant Security Token deployed successfully! (Database save failed - token may need to be added manually)',
      primaryTokenAddress: primaryTokenAddress,
      isERC3643Compliant: true,
      databaseSaved: dbSaveSuccess,
      contracts: deploymentResult.contracts,
      transactionHashes: deploymentResult.transactionHashes,
      tokenData: {
        name: body.name,
        symbol: body.symbol,
        decimals: body.decimals,
        primaryAddress: primaryTokenAddress,
        erc3643TokenWrapper: deploymentResult.contracts.erc3643TokenWrapper,
        erc3643Components: deploymentResult.contracts.erc3643Components,
        implementationAddress: deploymentResult.contracts.erc3643Components.implementationAddress,
        isERC3643Compliant: true,
        complianceLevel: 'Full ERC-3643 Standard',
        hasIdentityRegistry: true,
        hasComplianceModule: true,
        hasOnchainID: true,
        isUpgradeable: false // Direct deployment, not proxy for now
      },
      explorerUrls: {
        primaryToken: `https://amoy.polygonscan.com/address/${primaryTokenAddress}`,
        implementation: deploymentResult.contracts.erc3643Components.implementationAddress ?
          `https://amoy.polygonscan.com/address/${deploymentResult.contracts.erc3643Components.implementationAddress}` : null
      },
      erc3643Features: {
        identityRegistry: deploymentResult.contracts.erc3643Components.identityRegistryWrapper,
        compliance: deploymentResult.contracts.erc3643Components.complianceWrapper,
        trustedIssuersRegistry: deploymentResult.contracts.erc3643Components.trustedIssuersRegistry,
        claimTopicsRegistry: deploymentResult.contracts.erc3643Components.claimTopicsRegistry,
        identityFactory: deploymentResult.contracts.erc3643Components.identityFactory
      }
    });

  } catch (error: any) {
    console.error('Error deploying ERC-3643 token:', error);
    console.error('Error stack:', error.stack);
    console.error('Error code:', error.code);
    console.error('Error reason:', error.reason);

    // Parse common error messages
    let errorMessage = error.message;

    if (error.code === 'INSUFFICIENT_FUNDS') {
      errorMessage = 'Insufficient funds for deployment. Please ensure the admin wallet has enough MATIC.';
    } else if (error.message.includes('execution reverted')) {
      errorMessage = `Contract deployment failed: ${error.reason || error.message}`;
    } else if (error.message.includes('gas')) {
      errorMessage = 'Gas estimation failed. The network may be congested.';
    } else if (error.code === 'NETWORK_ERROR') {
      errorMessage = 'Network connection error. Please try again.';
    }

    return NextResponse.json(
      {
        error: `Failed to deploy token: ${errorMessage}`,
        details: error.message,
        reason: error.reason,
        code: error.code
      },
      { status: 500 }
    );
  }
}

// ERC-3643 Compliant Token Deployment Function
async function deployERC3643CompliantToken(
  tokenData: any,
  adminAddress: string,
  signer: ethers.Wallet
) {
  const transactionHashes: string[] = [];

  console.log('🚀 Deploying ERC-3643 Compliant Token');
  console.log('🔧 Using WORKING ERC-3643 infrastructure:', WORKING_ERC3643_CONTRACTS);

  try {
    // Create contract factory
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const wallet = new ethers.Wallet(PRIVATE_KEY!, provider);

    // Deploy the new ERC-3643 compliant SecurityTokenCore
    const SecurityTokenCore = new ethers.ContractFactory(
      SecurityTokenCoreArtifact.abi,
      SecurityTokenCoreArtifact.bytecode,
      wallet
    );

    console.log('📦 Deploying ERC-3643 compliant SecurityTokenCore...');

    // Deploy the contract directly (we'll add proxy support later)
    const deployedToken = await SecurityTokenCore.deploy();
    await deployedToken.waitForDeployment();
    const tokenAddress = await deployedToken.getAddress();

    console.log('✅ ERC-3643 compliant token deployed at:', tokenAddress);

    // Initialize the token
    const initTx = await deployedToken.initialize(
      tokenData.name,
      tokenData.symbol,
      parseInt(tokenData.decimals),
      ethers.parseUnits(tokenData.maxSupply.toString(), parseInt(tokenData.decimals)),
      adminAddress,
      tokenData.tokenPrice || "1.00",
      tokenData.bonusTiers || "No bonus tiers",
      tokenData.tokenDetails || `ERC-3643 compliant security token: ${tokenData.name}`,
      tokenData.tokenImageUrl || ""
    );

    await initTx.wait();
    console.log('✅ Token initialized successfully');

    // Test the new ERC-3643 functions
    console.log('🧪 Testing ERC-3643 compliance...');

    try {
      const identityRegistry = await deployedToken.identityRegistry();
      const compliance = await deployedToken.compliance();
      const onchainID = await deployedToken.onchainID();
      const isCompliant = await deployedToken.isERC3643Compliant();

      console.log('✅ ERC-3643 Functions Working:');
      console.log('   Identity Registry:', identityRegistry);
      console.log('   Compliance:', compliance);
      console.log('   OnchainID:', onchainID);
      console.log('   Is ERC-3643 Compliant:', isCompliant);

    } catch (error) {
      console.log('⚠️ ERC-3643 functions test failed:', error.message);
    }

    return {
      tokenWrapper: tokenAddress,
      identityRegistryWrapper: ERC3643_CONTRACTS.identityRegistryWrapper,
      complianceWrapper: ERC3643_CONTRACTS.complianceWrapper,
      trustedIssuersRegistry: ERC3643_CONTRACTS.trustedIssuersRegistry,
      claimTopicsRegistry: ERC3643_CONTRACTS.claimTopicsRegistry,
      identityFactory: ERC3643_CONTRACTS.identityFactory,
      transactionHashes: [deployedToken.deploymentTransaction()?.hash, initTx.hash].filter(Boolean),
      isERC3643Compliant: true,
      implementationAddress: tokenAddress // For now, same as token address
    };

  } catch (error) {
    console.error('❌ ERC-3643 compliant token deployment failed:', error);
    console.error('❌ Error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
      reason: error.reason,
      transaction: error.transaction
    });
    throw error; // Re-throw to be handled by caller
  }
}

// GET endpoint to check deployment status or get contract info
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'contracts') {
      // Return the current ERC-3643 contract addresses
      return NextResponse.json({
        erc3643Contracts: ERC3643_CONTRACTS,
        rpcUrl: RPC_URL,
        network: 'amoy'
      });
    }

    if (action === 'status') {
      // Check if the deployment service is ready
      const provider = new ethers.JsonRpcProvider(RPC_URL);
      const network = await provider.getNetwork();
      
      return NextResponse.json({
        ready: !!PRIVATE_KEY,
        network: {
          name: network.name,
          chainId: network.chainId.toString()
        },
        contracts: ERC3643_CONTRACTS
      });
    }

    return NextResponse.json({
      message: 'ERC-3643 Token Deployment API',
      endpoints: {
        'POST /': 'Deploy a new ERC-3643 compatible token',
        'GET /?action=contracts': 'Get ERC-3643 contract addresses',
        'GET /?action=status': 'Check deployment service status'
      }
    });

  } catch (error: any) {
    console.error('Error in GET endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
