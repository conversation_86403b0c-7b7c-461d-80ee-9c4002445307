"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-erc3643-token/page",{

/***/ "(app-pages-browser)/./src/app/create-erc3643-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-erc3643-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateERC3643TokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/factory.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract ABIs - Updated for ERC-3643 compliant tokens\nconst SecurityTokenCoreABI = [\n    \"function initialize(string memory name_, string memory symbol_, uint8 decimals_, uint256 maxSupply_, address admin_, string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_, string memory tokenImageUrl_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function maxSupply() view returns (uint256)\",\n    \"function identityRegistry() view returns (address)\",\n    \"function compliance() view returns (address)\",\n    \"function onchainID() view returns (address)\",\n    \"function isERC3643Compliant() view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst IdentityRegistryABI = [\n    \"function initialize(address admin_, address claimRegistry_) external\"\n];\nconst ComplianceABI = [\n    \"function initialize(address admin_, address identityRegistry_) external\"\n];\nconst TrustedIssuersRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function addTrustedIssuer(address trustedIssuer, uint256[] calldata claimTopics) external\"\n];\nconst ClaimTopicsRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function batchAddClaimTopics(uint256[] calldata topics) external\"\n];\nconst IdentityContractFactoryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst ERC3643TokenWrapperABI = [\n    \"function initialize(address underlyingToken_, address erc3643IdentityRegistry_, address erc3643Compliance_, address admin_, address onchainID_, string memory version_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\"\n];\nfunction CreateERC3643TokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [upgradeableDeploying, setUpgradeableDeploying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 2,\n        maxSupply: '1000000',\n        adminAddress: '',\n        tokenPrice: '100.00',\n        currency: 'USD',\n        bonusTiers: 'Early: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'ERC-3643 compliant security token with built-in identity registry and compliance modules',\n        tokenImageUrl: '',\n        enableERC3643: true,\n        initialClaimTopics: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ],\n        selectedClaims: [\n            1,\n            4\n        ] // Default: KYC + Qualification claims required\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateERC3643TokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateERC3643TokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'MATIC');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.1')) {\n                    setError('Insufficient balance. You need at least 0.1 MATIC for gas fees to deploy all contracts.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployContract = async function(contractName, bytecode, abi) {\n        let constructorArgs = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        if (!signer) throw new Error('No signer available');\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory(abi, bytecode, signer);\n        const contract = await factory.deploy(...constructorArgs);\n        await contract.waitForDeployment();\n        const address = await contract.getAddress();\n        console.log(\"\".concat(contractName, \" deployed to:\"), address);\n        return address;\n    };\n    const deployProxyContract = async (contractName, implementationAddress, initData)=>{\n        if (!signer) throw new Error('No signer available');\n        // ERC1967Proxy bytecode (simplified)\n        const proxyBytecode = \"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\";\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory([\n            \"constructor(address implementation, bytes memory data)\"\n        ], proxyBytecode, signer);\n        const proxy = await factory.deploy(implementationAddress, initData);\n        await proxy.waitForDeployment();\n        const address = await proxy.getAddress();\n        console.log(\"\".concat(contractName, \" proxy deployed to:\"), address);\n        return address;\n    };\n    const deployFullFeaturedToken = async ()=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Checking working ERC-3643 infrastructure...');\n            // First, try to deploy a real contract using our working infrastructure\n            setDeploymentStep('🚀 Deploying real ERC-3643 contract with working infrastructure...');\n            const realDeployResponse = await fetch('/api/deploy-real-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy'\n                })\n            });\n            const realResult = await realDeployResponse.json();\n            if (realResult.success) {\n                // Real deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: realResult.realTokenAddress,\n                    erc3643TokenWrapper: realResult.wrapperAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: realResult.realTokenAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: realResult.adminVerified,\n                    contracts: realResult.contracts,\n                    explorerUrls: realResult.explorerUrls,\n                    features: realResult.features,\n                    deploymentType: 'Real ERC-3643 Contract',\n                    erc3643Components: {\n                        workingWrapper: realResult.wrapperAddress,\n                        workingUnderlyingRegistry: realResult.contracts.workingUnderlyingRegistry,\n                        tokenAddress: realResult.realTokenAddress\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Real ERC-3643 Token deployed successfully using working infrastructure!\\n\\n✅ Token Address: \".concat(realResult.realTokenAddress, \"\\n✅ Wrapper Address: \").concat(realResult.wrapperAddress, \"\\n✅ Admin Verified: \").concat(realResult.adminVerified ? 'Yes' : 'No', \"\\n✅ Ready for institutional use!\\n\\n\\uD83D\\uDD17 View on Explorer: https://amoy.polygonscan.com/address/\").concat(realResult.realTokenAddress));\n                setDeploymentStep('');\n            } else {\n                // Real deployment failed, fall back to deployment plan\n                setDeploymentStep('⚠️ Real deployment failed, creating deployment plan with working infrastructure...');\n                const planResponse = await fetch('/api/deploy-fresh-token', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        decimals: formData.decimals,\n                        maxSupply: formData.maxSupply,\n                        adminAddress: formData.adminAddress,\n                        network: 'amoy'\n                    })\n                });\n                const planResult = await planResponse.json();\n                if (planResult.success) {\n                    var _planResult_systemStatus, _planResult_systemStatus1;\n                    setDeployedToken({\n                        success: true,\n                        securityTokenCore: planResult.primaryTokenAddress,\n                        erc3643TokenWrapper: planResult.contracts.workingWrapper,\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        primaryTokenAddress: planResult.primaryTokenAddress,\n                        isERC3643Compliant: true,\n                        systemStatus: planResult.systemStatus,\n                        contracts: planResult.contracts,\n                        deploymentScript: planResult.deploymentScript,\n                        explorerUrls: planResult.explorerUrls,\n                        features: planResult.features,\n                        deploymentType: 'Deployment Plan (Working Infrastructure)',\n                        erc3643Components: {\n                            workingWrapper: planResult.contracts.workingWrapper,\n                            workingUnderlyingRegistry: planResult.contracts.workingUnderlyingRegistry,\n                            planAddress: planResult.primaryTokenAddress\n                        }\n                    });\n                    setSuccess(\"\\uD83D\\uDCCB ERC-3643 Token deployment plan created successfully using working infrastructure!\\n\\n✅ Plan Address: \".concat(planResult.primaryTokenAddress, \"\\n✅ System Status: \").concat(((_planResult_systemStatus = planResult.systemStatus) === null || _planResult_systemStatus === void 0 ? void 0 : _planResult_systemStatus.systemHealthy) ? 'Healthy' : 'Issues Detected', \"\\n✅ Working Infrastructure: Ready\\n✅ Admin Verified: \").concat(((_planResult_systemStatus1 = planResult.systemStatus) === null || _planResult_systemStatus1 === void 0 ? void 0 : _planResult_systemStatus1.adminVerified) ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Use the provided deployment script to deploy the actual contract.\\n\\uD83D\\uDD17 Working Infrastructure: All components verified and functional\"));\n                    setDeploymentStep('');\n                } else {\n                    throw new Error(\"Both real deployment and plan creation failed: \".concat(planResult.error));\n                }\n            }\n        } catch (error) {\n            console.error('Error deploying token:', error);\n            setError(\"Failed to deploy token using working infrastructure: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while trying to use the verified working ERC-3643 infrastructure.\\n\\uD83D\\uDCA1 The working infrastructure includes:\\n   - Working Identity Registry: 0x1281a057881cbe94dc2a79561275aa6b35bf7854\\n   - Working Wrapper: 0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02\\n   - Admin Verification: Confirmed working\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setIsSubmitting(false);\n            setDeploymentStep('');\n        }\n    };\n    const deployUpgradeableToken = async ()=>{\n        setUpgradeableDeploying(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Deploying upgradeable ERC-3643 token with working infrastructure...');\n            const response = await fetch('/api/deploy-upgradeable-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Upgradeable deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: result.proxyAddress,\n                    erc3643TokenWrapper: result.implementationAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: result.proxyAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: result.adminVerified,\n                    contracts: result.contracts,\n                    explorerUrls: result.explorerUrls,\n                    features: result.features,\n                    deploymentType: 'Upgradeable ERC-3643 Contract',\n                    upgradeInfo: result.upgradeInfo,\n                    upgradeManagement: result.upgradeManagement,\n                    erc3643Components: {\n                        proxy: result.proxyAddress,\n                        implementation: result.implementationAddress,\n                        identityRegistry: result.contracts.identityRegistry\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Upgradeable ERC-3643 Token deployed successfully!\\n\\n✅ Proxy Address: \".concat(result.proxyAddress, \"\\n✅ Implementation: \").concat(result.implementationAddress, \"\\n✅ Admin Verified: \").concat(result.adminVerified ? 'Yes' : 'No', \"\\n✅ Upgradeable: \").concat(result.upgradeInfo.canUpgrade ? 'Yes' : 'Renounced', \"\\n✅ Upgrade Timelock: \").concat(result.upgradeInfo.upgradeTimelock, \"\\n✅ Ready for institutional use with optional upgradeability!\\n\\n\\uD83D\\uDD17 View Proxy: https://amoy.polygonscan.com/address/\").concat(result.proxyAddress, \"\\n\\uD83D\\uDD17 View Implementation: https://amoy.polygonscan.com/address/\").concat(result.implementationAddress));\n                setDeploymentStep('');\n            } else {\n                throw new Error(\"Upgradeable deployment failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Upgradeable deployment error:', error);\n            setError(\"Failed to deploy upgradeable token: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while deploying the upgradeable ERC-3643 token.\\n\\uD83D\\uDCA1 The upgradeable token includes:\\n   - UUPS proxy pattern for gas efficiency\\n   - Optional upgrade renunciation for maximum trust\\n   - 30-day timelock protection\\n   - All ERC-3643 compliance features\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setUpgradeableDeploying(false);\n            setDeploymentStep('');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployFullFeaturedToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create ERC-3643 Compliant Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create fully ERC-3643 compliant security tokens with built-in identity registry and compliance modules.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n            lineNumber: 459,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create ERC-3643 Compliant Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800\",\n                        children: \"✅ Working Infrastructure\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"✅\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"ERC-3643 Compliant Security Token with Working Infrastructure\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Deploy fully compliant ERC-3643 security tokens using verified working infrastructure:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Working Infrastructure:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Uses verified functional Identity Registry and Wrapper\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Admin Pre-Verified:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Admin address is already verified and ready to use\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Real Contract Deployment:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Attempts to deploy actual contracts first\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Fallback Plan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Creates deployment plan if real deployment fails\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Full ERC-3643 Compliance:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" All standard functions and security features\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Institutional Ready:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Freeze, force transfer, whitelist, KYC included\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"\\uD83C\\uDF89 Now using verified working ERC-3643 infrastructure!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" All components tested and functional.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 499,\n                columnNumber: 7\n            }, this),\n            isSubmitting && deploymentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-600 mr-2\",\n                            children: \"⏳\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: deploymentStep\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 528,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 538,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 548,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.securityTokenCore\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 13\n                            }, this),\n                            deployedToken.erc3643TokenWrapper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ERC-3643 Wrapper:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.erc3643TokenWrapper\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.securityTokenCore), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Full Featured Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., FFST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    \"Decimals * \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"(0=shares, 2=currency, 18=crypto)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals \",\n                                                            i === 0 ? '(whole numbers)' : i === 2 ? '(currency-like)' : i === 18 ? '(crypto-like)' : ''\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 673,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"100.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-lg mr-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"ERC-3643 Compliance Enabled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-700 mt-1\",\n                                                        children: \"All tokens are now ERC-3643 compliant by default with built-in identityRegistry(), compliance(), and onchainID() functions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                        children: isSubmitting ? '🔄 Creating ERC-3643 Compliant Token...' : '🏆 Create ERC-3643 Compliant Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: deployUpgradeableToken,\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-green-500\",\n                                        children: upgradeableDeploying ? '🔄 Creating Upgradeable ERC-3643 Token...' : '🔧 Create Upgradeable ERC-3643 Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 bg-gray-50 p-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83C\\uDFC6 Standard:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Immutable contract (maximum security, no upgrades possible)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83D\\uDD27 Upgradeable:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" UUPS proxy pattern (can be upgraded with 30-day timelock, can renounce upgrades)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 599,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-xl\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 829,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-800\",\n                                    children: \"All Features Included\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every token deployed includes: Configurable decimals, Upgradeable architecture, Role-based access control, Mint/burn capabilities, Force transfer, Address freezing, On-chain whitelist/KYC, Batch operations, Recovery mechanisms, and comprehensive events.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 font-medium text-green-700\",\n                                            children: \"Plus ERC-3643 Compliance: Built-in identityRegistry(), compliance(), onchainID() functions, Individual identity contracts, Claims-based verification, Trusted issuers registry, Compliance engine, and institutional-grade features.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 837,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 826,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n        lineNumber: 484,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateERC3643TokenPage, \"2A/VVdRtAzqizOs+YKhylSRNa+4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateERC3643TokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateERC3643TokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-erc3643-token/page.tsx\n"));

/***/ })

});