"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/app/reliable-tokens/page.tsx":
/*!******************************************!*\
  !*** ./src/app/reliable-tokens/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReliableTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _components_AgentsList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AgentsList */ \"(app-pages-browser)/./src/components/AgentsList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Default token address from environment (fallback)\nconst DEFAULT_SECURITY_TOKEN_CORE_ADDRESS = \"0x4538047fB565D809856CBd368521CD90A07b57fB\" || 0;\nfunction ReliableTokensContent() {\n    var _tokenInfo_totalSupply, _tokenInfo_maxSupply, _tokenInfo_metadata_tokenPrice, _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata_tokenPrice1, _tokenInfo_metadata2, _tokenInfo_metadata3;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const urlTokenAddress = searchParams.get('token');\n    // Use URL parameter if provided, otherwise fall back to environment variable\n    const SECURITY_TOKEN_CORE_ADDRESS = urlTokenAddress || DEFAULT_SECURITY_TOKEN_CORE_ADDRESS;\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('management');\n    // Whitelist management\n    const [whitelistedClients, setWhitelistedClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [whitelistLoading, setWhitelistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [whitelistError, setWhitelistError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenFromDb, setTokenFromDb] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ERC-3643 wrapper detection\n    const knownERC3643Wrappers = [\n        '******************************************',\n        '******************************************',\n        '******************************************'\n    ];\n    const knownUnderlyingToken = '******************************************';\n    const isERC3643Wrapper = knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS);\n    const underlyingTokenAddress = isERC3643Wrapper ? knownUnderlyingToken : SECURITY_TOKEN_CORE_ADDRESS;\n    const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n    // Debug logging\n    console.log('🔍 ReliableTokensContent rendered');\n    console.log('🔍 URL token parameter:', urlTokenAddress);\n    console.log('🔍 Using token address:', SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🔍 Default address:', DEFAULT_SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🛡️ Is ERC-3643 wrapper:', isERC3643Wrapper);\n    console.log('🔗 Data address for metadata calls:', dataAddress);\n    // Load token information using fallback-first approach\n    const loadTokenInfo = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🔄 Loading token info with fallback-first approach...');\n            // Check if this is an ERC-3643 wrapper\n            const knownERC3643Wrappers = [\n                '******************************************',\n                '******************************************',\n                '******************************************'\n            ];\n            const knownUnderlyingToken = '******************************************';\n            let isERC3643Wrapper = false;\n            let underlyingTokenAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            if (knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS)) {\n                console.log('🛡️ Detected known ERC-3643 wrapper address');\n                isERC3643Wrapper = true;\n                underlyingTokenAddress = knownUnderlyingToken;\n                console.log('🔗 Using known underlying token for data calls:', underlyingTokenAddress);\n            }\n            // For ERC-3643 wrappers, get basic info from wrapper but maxSupply/metadata from underlying\n            const basicInfoAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n            console.log('📊 Getting basic info from:', basicInfoAddress);\n            console.log('📊 Getting data (maxSupply, metadata) from:', dataAddress);\n            // Use safe contract calls that automatically handle fallbacks\n            let name, symbol, version, totalSupply, decimals, paused;\n            try {\n                [name, symbol, version, totalSupply, decimals] = await Promise.all([\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'name'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'symbol'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'version'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'totalSupply'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'decimals')\n                ]);\n                // Try to get paused status separately with error handling\n                try {\n                    paused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                } catch (pausedError) {\n                    console.log('⚠️ paused() function not available, assuming not paused:', pausedError);\n                    paused = false;\n                }\n            } catch (error) {\n                console.error('❌ Failed to load basic token info:', error);\n                throw new Error(\"Failed to load token information: \".concat(error));\n            }\n            // Get maxSupply and metadata from the appropriate address\n            let maxSupply;\n            let metadata;\n            try {\n                maxSupply = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'maxSupply');\n                console.log('✅ maxSupply loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ maxSupply failed, using 0:', error);\n                maxSupply = BigInt(0);\n            }\n            try {\n                metadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                console.log('✅ metadata loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ metadata failed, using defaults:', error);\n                metadata = {\n                    tokenPrice: '0',\n                    currency: 'USD',\n                    bonusTiers: '',\n                    tokenDetails: ''\n                };\n            }\n            setTokenInfo({\n                name: name || 'Unknown Token',\n                symbol: symbol || 'UNKNOWN',\n                version: version || '1.0',\n                totalSupply: totalSupply || BigInt(0),\n                maxSupply: maxSupply || BigInt(0),\n                decimals: decimals !== null && decimals !== void 0 ? decimals : 0,\n                paused: paused || false,\n                metadata: {\n                    tokenPrice: (metadata === null || metadata === void 0 ? void 0 : metadata.tokenPrice) || '0',\n                    currency: (metadata === null || metadata === void 0 ? void 0 : metadata.currency) || 'USD',\n                    bonusTiers: (metadata === null || metadata === void 0 ? void 0 : metadata.bonusTiers) || ''\n                }\n            });\n            console.log('✅ Token info loaded successfully');\n        } catch (error) {\n            console.error('❌ Failed to load token info:', error);\n            setError(\"Failed to load token information: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Connect wallet\n    const connectWallet = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                await window.ethereum.request({\n                    method: 'eth_requestAccounts'\n                });\n                setWalletConnected(true);\n                setSuccess('Wallet connected successfully');\n            } else {\n                setError('MetaMask not found. Please install MetaMask to use this feature.');\n            }\n        } catch (error) {\n            setError(\"Failed to connect wallet: \".concat(error.message));\n        }\n    };\n    // Toggle pause state using fallback-first approach\n    const togglePause = async ()=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            const currentPaused = tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused;\n            const isPausing = !currentPaused;\n            const functionName = isPausing ? 'pause' : 'unpause';\n            console.log(\"\\uD83D\\uDD04 \".concat(isPausing ? 'Pausing' : 'Unpausing', \" token...\"));\n            // Get real-time state to ensure accuracy (with error handling)\n            let realTimePaused;\n            try {\n                realTimePaused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                // Validate state change is needed\n                if (isPausing && realTimePaused) {\n                    throw new Error('Token is already paused. Refreshing page data...');\n                }\n                if (!isPausing && !realTimePaused) {\n                    throw new Error('Token is already unpaused. Refreshing page data...');\n                }\n            } catch (pausedError) {\n                console.log('⚠️ Cannot check paused status, proceeding with operation:', pausedError);\n            // Continue with the operation if we can't check the current state\n            }\n            // Check if the contract supports pause/unpause functions\n            try {\n                // Execute the transaction using safe transaction approach\n                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, functionName, []);\n                console.log(\"✅ \".concat(functionName, \" transaction sent: \").concat(tx.hash));\n                // Wait for confirmation\n                const receipt = await tx.wait();\n                console.log(\"✅ \".concat(functionName, \" confirmed in block \").concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n                setSuccess(\"Token \".concat(isPausing ? 'paused' : 'unpaused', \" successfully!\"));\n                // Refresh token info\n                await loadTokenInfo();\n            } catch (pauseError) {\n                console.error(\"❌ \".concat(functionName, \" function not supported:\"), pauseError);\n                throw new Error(\"This token contract does not support \".concat(functionName, \" functionality. Contract may not be pausable.\"));\n            }\n        } catch (error) {\n            var _error_message, _error_message1;\n            console.error(\"❌ Toggle pause failed:\", error);\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('already paused')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('already unpaused'))) {\n                setError(\"\".concat(error.message));\n                // Auto-refresh data after state conflict\n                setTimeout(async ()=>{\n                    await loadTokenInfo();\n                    setError(null);\n                }, 2000);\n            } else {\n                setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n            }\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Mint tokens using fallback-first approach\n    const mintTokens = async (amount, recipient)=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            console.log(\"\\uD83D\\uDD04 Minting \".concat(amount, \" tokens to \").concat(recipient, \"...\"));\n            // 🚨 CRITICAL SECURITY CHECK: Verify recipient is whitelisted before minting\n            console.log('🔒 SECURITY CHECK: Verifying recipient is whitelisted...');\n            try {\n                // First get the identity registry address using minimal ABI\n                const identityRegistryABI = [\n                    \"function identityRegistryAddress() view returns (address)\"\n                ];\n                const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                if (!identityRegistryAddress) {\n                    throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                }\n                // Then check whitelist status on the identity registry\n                const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                    \"function isWhitelisted(address account) external view returns (bool)\"\n                ], 'isWhitelisted', [\n                    recipient\n                ]);\n                if (!isWhitelisted) {\n                    throw new Error(\"SECURITY VIOLATION: Recipient address \".concat(recipient, \" is not whitelisted. Only whitelisted addresses can receive tokens for ERC-3643 compliance.\"));\n                }\n                console.log('✅ SECURITY PASSED: Recipient is whitelisted, proceeding with mint');\n            } catch (whitelistError) {\n                var _whitelistError_message, _whitelistError_message1;\n                console.error('❌ SECURITY CHECK FAILED:', whitelistError);\n                // Check if this is a contract compatibility issue\n                if (((_whitelistError_message = whitelistError.message) === null || _whitelistError_message === void 0 ? void 0 : _whitelistError_message.includes('execution reverted')) || ((_whitelistError_message1 = whitelistError.message) === null || _whitelistError_message1 === void 0 ? void 0 : _whitelistError_message1.includes('no data present'))) {\n                    console.log('⚠️ Contract does not support ERC-3643 identity registry functionality, proceeding without check');\n                    console.log('🚨 WARNING: This token may not be ERC-3643 compliant!');\n                } else {\n                    throw new Error(\"Security validation failed: \".concat(whitelistError.message));\n                }\n            }\n            // Convert amount based on decimals\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            const mintAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n            // Execute mint transaction\n            console.log('🔄 Attempting mint with params:', {\n                address: SECURITY_TOKEN_CORE_ADDRESS,\n                function: 'mint',\n                args: [\n                    recipient,\n                    mintAmount.toString()\n                ]\n            });\n            // Try with minimal ABI first\n            const mintABI = [\n                \"function mint(address to, uint256 amount) external\"\n            ];\n            const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, mintABI, 'mint', [\n                recipient,\n                mintAmount\n            ]);\n            console.log(\"✅ Mint transaction sent: \".concat(tx.hash));\n            const receipt = await tx.wait();\n            console.log(\"✅ Mint confirmed in block \".concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(recipient, \"!\"));\n            // Refresh token info\n            await loadTokenInfo();\n        } catch (error) {\n            console.error(\"❌ Mint failed:\", error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Load token from database to get the ID for whitelist queries\n    const loadTokenFromDatabase = async ()=>{\n        if (!SECURITY_TOKEN_CORE_ADDRESS) return;\n        try {\n            const response = await fetch('/api/tokens');\n            if (response.ok) {\n                const data = await response.json();\n                // Handle new API response format\n                const tokens = data.success && Array.isArray(data.tokens) ? data.tokens : Array.isArray(data) ? data : [];\n                // Find token by address\n                const token = tokens.find((t)=>t.address.toLowerCase() === SECURITY_TOKEN_CORE_ADDRESS.toLowerCase());\n                if (token) {\n                    setTokenFromDb(token);\n                    console.log('✅ Token found in database:', token);\n                } else {\n                    console.log('⚠️ Token not found in database, checking all available tokens:', tokens.map((t)=>t.address));\n                }\n            }\n        } catch (error) {\n            console.log('⚠️ Error loading token from database:', error);\n            setWhitelistError('Failed to load token from database');\n        }\n    };\n    // Load whitelisted clients for this token\n    const loadWhitelistedClients = async ()=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) {\n            setWhitelistError('Token not found in database');\n            return;\n        }\n        setWhitelistLoading(true);\n        setWhitelistError(null);\n        try {\n            console.log('🔍 Loading whitelisted clients for token ID:', tokenFromDb.id);\n            const response = await fetch(\"/api/tokens?whitelistedInvestors=true&tokenId=\".concat(tokenFromDb.id));\n            console.log('📥 Response status:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('❌ API Error:', errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📊 API Response:', data);\n            if (data.success) {\n                var _data_investors;\n                setWhitelistedClients(data.investors || []);\n                console.log('✅ Loaded whitelisted clients:', ((_data_investors = data.investors) === null || _data_investors === void 0 ? void 0 : _data_investors.length) || 0);\n            } else {\n                throw new Error(data.error || 'Failed to load whitelisted clients');\n            }\n        } catch (err) {\n            console.error('❌ Error loading whitelisted clients:', err);\n            setWhitelistError(err.message || 'Failed to load whitelisted clients');\n            setWhitelistedClients([]); // Clear the list on error\n        } finally{\n            setWhitelistLoading(false);\n        }\n    };\n    // Remove client from whitelist\n    const handleRemoveFromWhitelist = async (clientId)=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) return;\n        if (!confirm('Are you sure you want to remove this client from the whitelist?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/clients/\".concat(clientId, \"/token-approval\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: tokenFromDb.id,\n                    whitelistApproved: false,\n                    approvalStatus: 'REJECTED',\n                    notes: 'Removed from whitelist by admin'\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || 'Failed to remove from whitelist');\n            }\n            // Refresh the clients list\n            loadWhitelistedClients();\n            setSuccess('Client removed from whitelist successfully');\n        } catch (err) {\n            console.error('Error removing from whitelist:', err);\n            setError(\"Error: \".concat(err.message));\n        }\n    };\n    // Load token info on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            loadTokenInfo();\n            loadTokenFromDatabase();\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    // Load whitelist when token from DB is available and whitelist tab is active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if (tokenFromDb && activeTab === 'whitelist') {\n                loadWhitelistedClients();\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], [\n        tokenFromDb,\n        activeTab\n    ]);\n    // Check wallet connection on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if ( true && window.ethereum) {\n                window.ethereum.request({\n                    method: 'eth_accounts'\n                }).then({\n                    \"ReliableTokensContent.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setWalletConnected(true);\n                        }\n                    }\n                }[\"ReliableTokensContent.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    var _tokenInfo_decimals;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Reliable Token Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bulletproof token management with fallback-first architecture - no more network errors!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"Connection Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-2 \".concat(walletConnected ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Wallet: \",\n                                                            walletConnected ? 'Connected' : 'Not Connected'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Network: Reliable RPC Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this),\n                            !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 mr-2\",\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"text-red-600 hover:text-red-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 mr-2\",\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSuccess(null),\n                                className: \"text-green-600 hover:text-green-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('management'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'management' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"\\uD83D\\uDEE1️ Token Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('whitelist'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'whitelist' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: [\n                                        \"\\uD83D\\uDC65 Whitelisted Clients\",\n                                        whitelistedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full\",\n                                            children: whitelistedClients.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Token Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: loadTokenInfo,\n                                            disabled: loading,\n                                            className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50\",\n                                            children: loading ? 'Loading...' : 'Refresh'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 11\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Loading token information...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.name || 'Unknown'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.symbol || 'N/A'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Version\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: tokenInfo.version || '1.0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Decimals\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: (_tokenInfo_decimals = tokenInfo.decimals) !== null && _tokenInfo_decimals !== void 0 ? _tokenInfo_decimals : 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Total Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_totalSupply = tokenInfo.totalSupply) === null || _tokenInfo_totalSupply === void 0 ? void 0 : _tokenInfo_totalSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_maxSupply = tokenInfo.maxSupply) === null || _tokenInfo_maxSupply === void 0 ? void 0 : _tokenInfo_maxSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                    children: tokenInfo.paused ? '⏸️ Paused' : '▶️ Active'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Token Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice = _tokenInfo_metadata.tokenPrice) === null || _tokenInfo_metadata_tokenPrice === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice.toString()) || '0',\n                                                        \".00 \",\n                                                        ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.currency) || 'USD'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No token information available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Pause Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Token is currently paused. Unpause to allow transfers.' : 'Token is currently active. Pause to stop all transfers.'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePause,\n                                            disabled: actionLoading || !tokenInfo,\n                                            className: \"w-full px-4 py-2 rounded-lg font-medium disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                            children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '▶️ Unpause Token' : '⏸️ Pause Token'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"mintRecipient\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"mintAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _document_getElementById, _document_getElementById1;\n                                                        const recipient = (_document_getElementById = document.getElementById('mintRecipient')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        const amount = (_document_getElementById1 = document.getElementById('mintAmount')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                        if (recipient && amount) {\n                                                            mintTokens(amount, recipient);\n                                                        } else {\n                                                            setError('Please enter both recipient address and amount');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading || !tokenInfo,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: actionLoading ? 'Processing...' : '🪙 Mint Tokens'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"ERC-3643 Whitelist Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium\",\n                                                    children: \"\\uD83D\\uDEE1️ Identity Registry Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-600 text-lg mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ERC-3643 Process:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \" Adding to whitelist automatically registers an OnchainID if needed, then adds to whitelist. This ensures full ERC-3643 compliance.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"whitelistAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Adding to whitelist via API...');\n                                                                        const response = await fetch('/api/admin/add-to-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist add successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist add failed:', error);\n                                                                        setError(\"Failed to add to whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: actionLoading ? 'Processing...' : '✅ Register & Whitelist'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Removing from whitelist via API...');\n                                                                        const response = await fetch('/api/admin/remove-from-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist remove successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist remove failed:', error);\n                                                                        setError(\"Failed to remove from whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: \"❌ Remove (API)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                try {\n                                                                    // First get the identity registry address using minimal ABI\n                                                                    const identityRegistryABI = [\n                                                                        \"function identityRegistryAddress() view returns (address)\"\n                                                                    ];\n                                                                    const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                                                                    if (!identityRegistryAddress) {\n                                                                        throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                                                                    }\n                                                                    // Then check whitelist status on the identity registry\n                                                                    const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                                                                        \"function isWhitelisted(address account) external view returns (bool)\"\n                                                                    ], 'isWhitelisted', [\n                                                                        address\n                                                                    ]);\n                                                                    setSuccess(\"Address \".concat(address, \" is \").concat(isWhitelisted ? '✅ whitelisted' : '❌ not whitelisted', \" (via Identity Registry: \").concat(identityRegistryAddress.slice(0, 10), \"...)\"));\n                                                                } catch (whitelistCheckError) {\n                                                                    var _whitelistCheckError_message, _whitelistCheckError_message1;\n                                                                    // Check if this is a contract compatibility issue\n                                                                    if (((_whitelistCheckError_message = whitelistCheckError.message) === null || _whitelistCheckError_message === void 0 ? void 0 : _whitelistCheckError_message.includes('execution reverted')) || ((_whitelistCheckError_message1 = whitelistCheckError.message) === null || _whitelistCheckError_message1 === void 0 ? void 0 : _whitelistCheckError_message1.includes('no data present'))) {\n                                                                        setError('⚠️ This contract does not support ERC-3643 identity registry functionality.');\n                                                                    } else {\n                                                                        throw whitelistCheckError;\n                                                                    }\n                                                                }\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to check whitelist: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD0D Check Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Balance Checker\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address to Check\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"balanceAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('balanceAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                const balance = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'balanceOf', [\n                                                                    address\n                                                                ]);\n                                                                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                const formattedBalance = decimals > 0 ? (Number(balance || 0) / Math.pow(10, decimals)).toString() : (balance === null || balance === void 0 ? void 0 : balance.toString()) || '0';\n                                                                setSuccess(\"Balance: \".concat(formattedBalance, \" \").concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.symbol) || 'tokens'));\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to get balance: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Check Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Burn Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount to Burn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"burnAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1053,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1049,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"⚠️ This will permanently destroy tokens from your wallet. This action cannot be undone.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1060,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const amount = (_document_getElementById = document.getElementById('burnAmount')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (amount) {\n                                                            if (confirm(\"Are you sure you want to burn \".concat(amount, \" tokens? This action cannot be undone.\"))) {\n                                                                try {\n                                                                    setActionLoading(true);\n                                                                    const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                    const burnAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'burn', [\n                                                                        burnAmount\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Successfully burned \".concat(amount, \" tokens\"));\n                                                                    await loadTokenInfo();\n                                                                } catch (error) {\n                                                                    setError(\"Failed to burn tokens: \".concat(error.message));\n                                                                } finally{\n                                                                    setActionLoading(false);\n                                                                }\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an amount to burn');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD25 Burn Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Agent Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1105,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Agent Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"agentAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1112,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1108,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully added \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to add agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➕ Add Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully removed \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to remove agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➖ Remove Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1165,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Note: Since AGENT_MANAGER module is not registered, we can't enumerate agents\n                                                                // Instead, we'll show how to check if specific addresses have AGENT_ROLE\n                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                // Check some common addresses for demonstration\n                                                                const addressesToCheck = [\n                                                                    '0x56f3726C92B8B92a6ab71983886F91718540d888',\n                                                                    '0x1f1a17fe870f5A0EEf1274247c080478E3d0840A' // Test address\n                                                                ];\n                                                                const agentResults = [];\n                                                                for (const address of addressesToCheck){\n                                                                    try {\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            AGENT_ROLE,\n                                                                            address\n                                                                        ]);\n                                                                        if (hasRole) {\n                                                                            agentResults.push(\"✅ \".concat(address));\n                                                                        }\n                                                                    } catch (error) {\n                                                                    // Skip addresses that cause errors\n                                                                    }\n                                                                }\n                                                                if (agentResults.length === 0) {\n                                                                    setSuccess('No agents found in checked addresses. Use \"Check Role\" to verify specific addresses.');\n                                                                } else {\n                                                                    setSuccess(\"Agents found: \".concat(agentResults.join(', ')));\n                                                                }\n                                                            } catch (error) {\n                                                                setError(\"Failed to get agents: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        },\n                                                        disabled: actionLoading,\n                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                        children: \"\\uD83D\\uDC65 Check Known Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1212,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-3\",\n                                                            children: \"\\uD83D\\uDCCB Current Agents List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1275,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentsList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1276,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1274,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Role Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1283,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1287,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"roleAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1290,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1286,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1298,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"roleSelect\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"AGENT_ROLE\",\n                                                                            children: \"AGENT_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1305,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"TRANSFER_MANAGER_ROLE\",\n                                                                            children: \"TRANSFER_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1306,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"MODULE_MANAGER_ROLE\",\n                                                                            children: \"MODULE_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1307,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DEFAULT_ADMIN_ROLE\",\n                                                                            children: \"DEFAULT_ADMIN_ROLE (⚠️ Dangerous)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1308,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1301,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1285,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully granted \".concat(roleSelect, \" to \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to grant role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"✅ Grant Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully revoked \".concat(roleSelect, \" from \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to revoke role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"❌ Revoke Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1360,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        setSuccess(\"Address \".concat(address, \" \").concat(hasRole ? '✅ HAS' : '❌ DOES NOT HAVE', \" \").concat(roleSelect));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to check role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDD0D Check Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1312,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Role Descriptions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1456,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1456,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"AGENT_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can perform basic token operations\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 84\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"TRANSFER_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can manage transfers and compliance\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1458,\n                                                                columnNumber: 96\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"MODULE_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can register/unregister modules\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 90\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DEFAULT_ADMIN_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Full admin access (use with extreme caution)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1455,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1454,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1284,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1282,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Token Metadata\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1468,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Token Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1472,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newTokenPrice\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"100.00 USD\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1475,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1471,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Bonus Tiers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1483,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newBonusTiers\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"Early: 20%, Standard: 10%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1486,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1482,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1470,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Details/Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1495,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"newTokenDetails\",\n                                                            rows: 3,\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Token type: Carbon Credits, Debt Securities, Real Estate, etc. Additional details...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1498,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1494,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Image URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1506,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"url\",\n                                                            id: \"newTokenImageUrl\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"https://example.com/token-logo.png\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1509,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1505,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1, _document_getElementById2;\n                                                                const price = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const tiers = (_document_getElementById1 = document.getElementById('newBonusTiers')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                const details = (_document_getElementById2 = document.getElementById('newTokenDetails')) === null || _document_getElementById2 === void 0 ? void 0 : _document_getElementById2.value;\n                                                                if (price || tiers || details) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Get current values if not provided\n                                                                        const currentPrice = price || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenPrice) || '';\n                                                                        const currentTiers = tiers || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.bonusTiers) || '';\n                                                                        const currentDetails = details || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenDetails) || '';\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                            currentPrice,\n                                                                            currentTiers,\n                                                                            currentDetails\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token metadata!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update metadata: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter at least one field to update');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDCDD Update Metadata\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1517,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const imageUrl = (_document_getElementById = document.getElementById('newTokenImageUrl')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (imageUrl) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenImageUrl', [\n                                                                            imageUrl\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token image!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update image: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an image URL');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDDBC️ Update Image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1558,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1516,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1469,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1467,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Quick Price Update\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1598,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Current Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice1 = _tokenInfo_metadata2.tokenPrice) === null || _tokenInfo_metadata_tokenPrice1 === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice1.toString()) || '0',\n                                                                \".00 \",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.currency) || 'USD'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1604,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1600,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"New Price (USD)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1609,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"newTokenPrice\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"150\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1612,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1608,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const newPrice = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (newPrice) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                console.log('🔄 Updating token price using fallback-first approach...');\n                                                                // Try direct updateTokenPrice first\n                                                                try {\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenPrice', [\n                                                                        newPrice\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD\"));\n                                                                } catch (directError) {\n                                                                    console.log('Direct method failed, trying fallback...');\n                                                                    // Fallback to updateTokenMetadata\n                                                                    const currentMetadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                                                                    const currentBonusTiers = currentMetadata[1];\n                                                                    const currentDetails = currentMetadata[2];\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                        newPrice,\n                                                                        currentBonusTiers,\n                                                                        currentDetails\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD (via fallback method)\"));\n                                                                }\n                                                                // Refresh token info\n                                                                await loadTokenInfo();\n                                                            } catch (error) {\n                                                                setError(\"Failed to update token price: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter a new price');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Update Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1619,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1599,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1597,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"\\uD83D\\uDEE1️ Fallback-First Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1687,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Network Resilience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1690,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Multiple RPC endpoints\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1692,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic failover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1693,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Connection testing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1694,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1691,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1689,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Error Elimination\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1698,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"missing revert data\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1700,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"Internal JSON-RPC\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1701,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Reliable contract calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1702,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1699,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1697,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ User Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1706,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Seamless operation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1708,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Clear error messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1709,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic recovery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1710,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1707,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1705,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1688,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1686,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"\\uD83D\\uDC65 Whitelisted Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1725,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Manage clients who are approved to hold \",\n                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.name) || 'this',\n                                                    \" tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1726,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1724,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: whitelistedClients.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1732,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Whitelisted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1733,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1731,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                                children: \"➕ Add Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1735,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1730,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1723,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1722,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md\",\n                            children: whitelistLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1749,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"Loading whitelisted clients...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1750,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1748,\n                                columnNumber: 17\n                            }, this) : whitelistError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-600 text-xl\",\n                                                children: \"❌\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1756,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-red-800\",\n                                                        children: \"Error Loading Whitelist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1758,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700\",\n                                                        children: whitelistError\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1759,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1757,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1755,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1754,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1753,\n                                columnNumber: 17\n                            }, this) : whitelistedClients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1766,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Whitelisted Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"No clients have been whitelisted for this token yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1768,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium\",\n                                        children: \"➕ Add First Client\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1771,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1765,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1783,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Wallet Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1786,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"KYC Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1789,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Approved Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1792,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1795,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1782,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1781,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedClients.map((client)=>{\n                                                var _client_tokenApproval;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: [\n                                                                            client.firstName,\n                                                                            \" \",\n                                                                            client.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1805,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: client.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1808,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1804,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1803,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 font-mono\",\n                                                                children: client.walletAddress\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1812,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1811,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.kycStatus === 'APPROVED' ? 'bg-green-100 text-green-800' : client.kycStatus === 'REJECTED' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                children: client.kycStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1817,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1816,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: ((_client_tokenApproval = client.tokenApproval) === null || _client_tokenApproval === void 0 ? void 0 : _client_tokenApproval.approvedAt) ? new Date(client.tokenApproval.approvedAt).toLocaleDateString() : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1827,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveFromWhitelist(client.id),\n                                                                    className: \"text-red-600 hover:text-red-900 mr-4\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1833,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/clients/\".concat(client.id),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1839,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1832,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1802,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1800,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1780,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1779,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1746,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 1720,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 540,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 539,\n        columnNumber: 5\n    }, this);\n}\n_s(ReliableTokensContent, \"mZycVj3G2vFi5jAx3YzfoFENy3g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ReliableTokensContent;\nfunction ReliableTokensPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1864,\n                        columnNumber: 9\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading token management...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1865,\n                        columnNumber: 9\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                lineNumber: 1863,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1862,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReliableTokensContent, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1868,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 1862,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ReliableTokensPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ReliableTokensContent\");\n$RefreshReg$(_c1, \"ReliableTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reliable-tokens/page.tsx\n"));

/***/ })

});