"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/app/reliable-tokens/page.tsx":
/*!******************************************!*\
  !*** ./src/app/reliable-tokens/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReliableTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _components_AgentsList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AgentsList */ \"(app-pages-browser)/./src/components/AgentsList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default token address from environment (fallback)\nconst DEFAULT_SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\" || 0;\nfunction ReliableTokensContent() {\n    var _tokenInfo_totalSupply, _tokenInfo_maxSupply, _tokenInfo_metadata_tokenPrice, _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata_tokenPrice1, _tokenInfo_metadata2, _tokenInfo_metadata3;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const urlTokenAddress = searchParams.get('token');\n    // Use URL parameter if provided, otherwise fall back to environment variable\n    const SECURITY_TOKEN_CORE_ADDRESS = urlTokenAddress || DEFAULT_SECURITY_TOKEN_CORE_ADDRESS;\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('management');\n    // Whitelist management\n    const [whitelistedClients, setWhitelistedClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [whitelistLoading, setWhitelistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [whitelistError, setWhitelistError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenFromDb, setTokenFromDb] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ERC-3643 wrapper detection\n    const knownERC3643Wrappers = [\n        '******************************************',\n        '******************************************',\n        '******************************************'\n    ];\n    const knownUnderlyingToken = '******************************************';\n    const isERC3643Wrapper = knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS);\n    const underlyingTokenAddress = isERC3643Wrapper ? knownUnderlyingToken : SECURITY_TOKEN_CORE_ADDRESS;\n    const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n    // Debug logging\n    console.log('🔍 ReliableTokensContent rendered');\n    console.log('🔍 URL token parameter:', urlTokenAddress);\n    console.log('🔍 Using token address:', SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🔍 Default address:', DEFAULT_SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🛡️ Is ERC-3643 wrapper:', isERC3643Wrapper);\n    console.log('🔗 Data address for metadata calls:', dataAddress);\n    // Load token information using fallback-first approach\n    const loadTokenInfo = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🔄 Loading token info with fallback-first approach...');\n            // Check if this is an ERC-3643 wrapper\n            const knownERC3643Wrappers = [\n                '******************************************',\n                '******************************************',\n                '******************************************'\n            ];\n            const knownUnderlyingToken = '******************************************';\n            let isERC3643Wrapper = false;\n            let underlyingTokenAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            if (knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS)) {\n                console.log('🛡️ Detected known ERC-3643 wrapper address');\n                isERC3643Wrapper = true;\n                underlyingTokenAddress = knownUnderlyingToken;\n                console.log('🔗 Using known underlying token for data calls:', underlyingTokenAddress);\n            }\n            // For ERC-3643 wrappers, get basic info from wrapper but maxSupply/metadata from underlying\n            const basicInfoAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n            console.log('📊 Getting basic info from:', basicInfoAddress);\n            console.log('📊 Getting data (maxSupply, metadata) from:', dataAddress);\n            // Use safe contract calls that automatically handle fallbacks\n            let name, symbol, version, totalSupply, decimals, paused;\n            try {\n                [name, symbol, version, totalSupply, decimals] = await Promise.all([\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'name'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'symbol'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'version'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'totalSupply'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'decimals')\n                ]);\n                // Try to get paused status separately with error handling\n                try {\n                    paused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                } catch (pausedError) {\n                    console.log('⚠️ paused() function not available, assuming not paused:', pausedError);\n                    paused = false;\n                }\n            } catch (error) {\n                console.error('❌ Failed to load basic token info:', error);\n                throw new Error(\"Failed to load token information: \".concat(error));\n            }\n            // Get maxSupply and metadata from the appropriate address\n            let maxSupply;\n            let metadata;\n            try {\n                maxSupply = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'maxSupply');\n                console.log('✅ maxSupply loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ maxSupply failed, using 0:', error);\n                maxSupply = BigInt(0);\n            }\n            try {\n                metadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                console.log('✅ metadata loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ metadata failed, using defaults:', error);\n                metadata = {\n                    tokenPrice: '0',\n                    currency: 'USD',\n                    bonusTiers: '',\n                    tokenDetails: ''\n                };\n            }\n            setTokenInfo({\n                name: name || 'Unknown Token',\n                symbol: symbol || 'UNKNOWN',\n                version: version || '1.0',\n                totalSupply: totalSupply || BigInt(0),\n                maxSupply: maxSupply || BigInt(0),\n                decimals: decimals !== null && decimals !== void 0 ? decimals : 0,\n                paused: paused || false,\n                metadata: {\n                    tokenPrice: (metadata === null || metadata === void 0 ? void 0 : metadata.tokenPrice) || '0',\n                    currency: (metadata === null || metadata === void 0 ? void 0 : metadata.currency) || 'USD',\n                    bonusTiers: (metadata === null || metadata === void 0 ? void 0 : metadata.bonusTiers) || ''\n                }\n            });\n            console.log('✅ Token info loaded successfully');\n        } catch (error) {\n            console.error('❌ Failed to load token info:', error);\n            setError(\"Failed to load token information: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Connect wallet\n    const connectWallet = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                await window.ethereum.request({\n                    method: 'eth_requestAccounts'\n                });\n                setWalletConnected(true);\n                setSuccess('Wallet connected successfully');\n            } else {\n                setError('MetaMask not found. Please install MetaMask to use this feature.');\n            }\n        } catch (error) {\n            setError(\"Failed to connect wallet: \".concat(error.message));\n        }\n    };\n    // Debug wallet and transaction setup\n    const debugWalletConnection = async ()=>{\n        try {\n            setError(null);\n            setSuccess(null);\n            console.log('🔍 DEBUGGING WALLET CONNECTION...');\n            if ( false || !window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            // Check accounts\n            const accounts = await window.ethereum.request({\n                method: 'eth_accounts'\n            });\n            console.log('Connected accounts:', accounts);\n            // Check network\n            const chainId = await window.ethereum.request({\n                method: 'eth_chainId'\n            });\n            console.log('Current chain ID:', chainId, '(Expected: 0x13882 for Polygon Amoy)');\n            // Test provider creation\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const signerAddress = await signer.getAddress();\n            console.log('Signer address:', signerAddress);\n            // Test simple contract call (view function)\n            const viewABI = [\n                \"function name() view returns (string)\"\n            ];\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, viewABI, provider);\n            const tokenName = await contract.name();\n            console.log('Token name (view call):', tokenName);\n            // Test contract with signer (but don't execute)\n            const mintABI = [\n                \"function mint(address to, uint256 amount) external\"\n            ];\n            const contractWithSigner = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, mintABI, signer);\n            // Try gas estimation (this is where it might fail)\n            try {\n                const gasEstimate = await contractWithSigner.mint.estimateGas(signerAddress, 1);\n                console.log('✅ Gas estimation successful:', gasEstimate.toString());\n                setSuccess(\"✅ Wallet connection is working! Gas estimate: \".concat(gasEstimate.toString()));\n            } catch (gasError) {\n                console.error('❌ Gas estimation failed:', gasError);\n                setError(\"Gas estimation failed: \".concat(gasError.message));\n            }\n        } catch (error) {\n            console.error('❌ Debug failed:', error);\n            setError(\"Debug failed: \".concat(error.message));\n        }\n    };\n    // Toggle pause state using fallback-first approach\n    const togglePause = async ()=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            const currentPaused = tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused;\n            const isPausing = !currentPaused;\n            const functionName = isPausing ? 'pause' : 'unpause';\n            console.log(\"\\uD83D\\uDD04 \".concat(isPausing ? 'Pausing' : 'Unpausing', \" token...\"));\n            // Get real-time state to ensure accuracy (with error handling)\n            let realTimePaused;\n            try {\n                realTimePaused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                // Validate state change is needed\n                if (isPausing && realTimePaused) {\n                    throw new Error('Token is already paused. Refreshing page data...');\n                }\n                if (!isPausing && !realTimePaused) {\n                    throw new Error('Token is already unpaused. Refreshing page data...');\n                }\n            } catch (pausedError) {\n                console.log('⚠️ Cannot check paused status, proceeding with operation:', pausedError);\n            // Continue with the operation if we can't check the current state\n            }\n            // Check if the contract supports pause/unpause functions\n            try {\n                // Execute the transaction using safe transaction approach\n                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, functionName, []);\n                console.log(\"✅ \".concat(functionName, \" transaction sent: \").concat(tx.hash));\n                // Wait for confirmation\n                const receipt = await tx.wait();\n                console.log(\"✅ \".concat(functionName, \" confirmed in block \").concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n                setSuccess(\"Token \".concat(isPausing ? 'paused' : 'unpaused', \" successfully!\"));\n                // Refresh token info\n                await loadTokenInfo();\n            } catch (pauseError) {\n                console.error(\"❌ \".concat(functionName, \" function not supported:\"), pauseError);\n                throw new Error(\"This token contract does not support \".concat(functionName, \" functionality. Contract may not be pausable.\"));\n            }\n        } catch (error) {\n            var _error_message, _error_message1;\n            console.error(\"❌ Toggle pause failed:\", error);\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('already paused')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('already unpaused'))) {\n                setError(\"\".concat(error.message));\n                // Auto-refresh data after state conflict\n                setTimeout(async ()=>{\n                    await loadTokenInfo();\n                    setError(null);\n                }, 2000);\n            } else {\n                setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n            }\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Mint tokens using fallback-first approach\n    const mintTokens = async (amount, recipient)=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            console.log(\"\\uD83D\\uDD04 Minting \".concat(amount, \" tokens to \").concat(recipient, \"...\"));\n            // 🚨 CRITICAL SECURITY CHECK: Verify recipient is whitelisted before minting\n            console.log('🔒 SECURITY CHECK: Verifying recipient is whitelisted...');\n            try {\n                // First get the identity registry address using minimal ABI\n                const identityRegistryABI = [\n                    \"function identityRegistryAddress() view returns (address)\"\n                ];\n                const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                if (!identityRegistryAddress) {\n                    throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                }\n                // Then check whitelist status on the identity registry\n                const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                    \"function isWhitelisted(address account) external view returns (bool)\"\n                ], 'isWhitelisted', [\n                    recipient\n                ]);\n                if (!isWhitelisted) {\n                    throw new Error(\"SECURITY VIOLATION: Recipient address \".concat(recipient, \" is not whitelisted. Only whitelisted addresses can receive tokens for ERC-3643 compliance.\"));\n                }\n                console.log('✅ SECURITY PASSED: Recipient is whitelisted, proceeding with mint');\n            } catch (whitelistError) {\n                var _whitelistError_message, _whitelistError_message1;\n                console.error('❌ SECURITY CHECK FAILED:', whitelistError);\n                // Check if this is a contract compatibility issue\n                if (((_whitelistError_message = whitelistError.message) === null || _whitelistError_message === void 0 ? void 0 : _whitelistError_message.includes('execution reverted')) || ((_whitelistError_message1 = whitelistError.message) === null || _whitelistError_message1 === void 0 ? void 0 : _whitelistError_message1.includes('no data present'))) {\n                    console.log('⚠️ Contract does not support ERC-3643 identity registry functionality, proceeding without check');\n                    console.log('🚨 WARNING: This token may not be ERC-3643 compliant!');\n                } else {\n                    throw new Error(\"Security validation failed: \".concat(whitelistError.message));\n                }\n            }\n            // Validate and convert amount\n            console.log('🔍 Amount validation:', {\n                rawAmount: amount,\n                amountType: typeof amount,\n                tokenInfoDecimals: tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals,\n                parsedAmount: parseFloat(amount)\n            });\n            const parsedAmount = parseFloat(amount);\n            if (isNaN(parsedAmount) || parsedAmount <= 0) {\n                throw new Error(\"Invalid amount: \".concat(amount, \". Please enter a positive number.\"));\n            }\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            // Convert to BigInt safely\n            let mintAmount;\n            if (decimals > 0) {\n                // For tokens with decimals, multiply by 10^decimals\n                // Convert to string first to avoid floating point precision issues\n                const decimalMultiplier = Math.pow(10, decimals);\n                const scaledAmount = Math.floor(parsedAmount * decimalMultiplier);\n                mintAmount = BigInt(scaledAmount);\n            } else {\n                // For tokens without decimals, use whole number\n                mintAmount = BigInt(Math.floor(parsedAmount));\n            }\n            if (mintAmount <= 0n) {\n                throw new Error(\"Calculated mint amount is invalid: \".concat(mintAmount.toString()));\n            }\n            // Execute mint transaction\n            console.log('🔄 Attempting mint with params:', {\n                address: SECURITY_TOKEN_CORE_ADDRESS,\n                function: 'mint',\n                args: [\n                    recipient,\n                    mintAmount.toString()\n                ],\n                recipient: recipient,\n                amount: amount,\n                mintAmount: mintAmount.toString(),\n                decimals: decimals,\n                mintAmountType: typeof mintAmount\n            });\n            // Try with minimal ABI first\n            const mintABI = [\n                \"function mint(address to, uint256 amount) external\"\n            ];\n            const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, mintABI, 'mint', [\n                recipient,\n                mintAmount\n            ]);\n            console.log(\"✅ Mint transaction sent: \".concat(tx.hash));\n            const receipt = await tx.wait();\n            console.log(\"✅ Mint confirmed in block \".concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(recipient, \"!\"));\n            // Refresh token info\n            await loadTokenInfo();\n        } catch (error) {\n            console.error(\"❌ Mint failed:\", error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Load token from database to get the ID for whitelist queries\n    const loadTokenFromDatabase = async ()=>{\n        if (!SECURITY_TOKEN_CORE_ADDRESS) return;\n        try {\n            const response = await fetch('/api/tokens');\n            if (response.ok) {\n                const data = await response.json();\n                // Handle new API response format\n                const tokens = data.success && Array.isArray(data.tokens) ? data.tokens : Array.isArray(data) ? data : [];\n                // Find token by address\n                const token = tokens.find((t)=>t.address.toLowerCase() === SECURITY_TOKEN_CORE_ADDRESS.toLowerCase());\n                if (token) {\n                    setTokenFromDb(token);\n                    console.log('✅ Token found in database:', token);\n                } else {\n                    console.log('⚠️ Token not found in database, checking all available tokens:', tokens.map((t)=>t.address));\n                }\n            }\n        } catch (error) {\n            console.log('⚠️ Error loading token from database:', error);\n            setWhitelistError('Failed to load token from database');\n        }\n    };\n    // Load whitelisted clients for this token\n    const loadWhitelistedClients = async ()=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) {\n            setWhitelistError('Token not found in database');\n            return;\n        }\n        setWhitelistLoading(true);\n        setWhitelistError(null);\n        try {\n            console.log('🔍 Loading whitelisted clients for token ID:', tokenFromDb.id);\n            const response = await fetch(\"/api/tokens?whitelistedInvestors=true&tokenId=\".concat(tokenFromDb.id));\n            console.log('📥 Response status:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('❌ API Error:', errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📊 API Response:', data);\n            if (data.success) {\n                var _data_investors;\n                setWhitelistedClients(data.investors || []);\n                console.log('✅ Loaded whitelisted clients:', ((_data_investors = data.investors) === null || _data_investors === void 0 ? void 0 : _data_investors.length) || 0);\n            } else {\n                throw new Error(data.error || 'Failed to load whitelisted clients');\n            }\n        } catch (err) {\n            console.error('❌ Error loading whitelisted clients:', err);\n            setWhitelistError(err.message || 'Failed to load whitelisted clients');\n            setWhitelistedClients([]); // Clear the list on error\n        } finally{\n            setWhitelistLoading(false);\n        }\n    };\n    // Remove client from whitelist\n    const handleRemoveFromWhitelist = async (clientId)=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) return;\n        if (!confirm('Are you sure you want to remove this client from the whitelist?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/clients/\".concat(clientId, \"/token-approval\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: tokenFromDb.id,\n                    whitelistApproved: false,\n                    approvalStatus: 'REJECTED',\n                    notes: 'Removed from whitelist by admin'\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || 'Failed to remove from whitelist');\n            }\n            // Refresh the clients list\n            loadWhitelistedClients();\n            setSuccess('Client removed from whitelist successfully');\n        } catch (err) {\n            console.error('Error removing from whitelist:', err);\n            setError(\"Error: \".concat(err.message));\n        }\n    };\n    // Load token info on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            loadTokenInfo();\n            loadTokenFromDatabase();\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    // Load whitelist when token from DB is available and whitelist tab is active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if (tokenFromDb && activeTab === 'whitelist') {\n                loadWhitelistedClients();\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], [\n        tokenFromDb,\n        activeTab\n    ]);\n    // Check wallet connection on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if ( true && window.ethereum) {\n                window.ethereum.request({\n                    method: 'eth_accounts'\n                }).then({\n                    \"ReliableTokensContent.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setWalletConnected(true);\n                        }\n                    }\n                }[\"ReliableTokensContent.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    var _tokenInfo_decimals;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Reliable Token Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bulletproof token management with fallback-first architecture - no more network errors!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"Connection Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-2 \".concat(walletConnected ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Wallet: \",\n                                                            walletConnected ? 'Connected' : 'Not Connected'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Network: Reliable RPC Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\",\n                                        children: \"Connect Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, this),\n                                    walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: debugWalletConnection,\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg\",\n                                        children: \"Debug Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 636,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 mr-2\",\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"text-red-600 hover:text-red-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 676,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 mr-2\",\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSuccess(null),\n                                className: \"text-green-600 hover:text-green-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('management'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'management' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"\\uD83D\\uDEE1️ Token Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('whitelist'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'whitelist' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: [\n                                        \"\\uD83D\\uDC65 Whitelisted Clients\",\n                                        whitelistedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full\",\n                                            children: whitelistedClients.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 710,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Token Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: loadTokenInfo,\n                                            disabled: loading,\n                                            className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50\",\n                                            children: loading ? 'Loading...' : 'Refresh'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 11\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Loading token information...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 13\n                                }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.name || 'Unknown'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.symbol || 'N/A'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Version\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: tokenInfo.version || '1.0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Decimals\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: (_tokenInfo_decimals = tokenInfo.decimals) !== null && _tokenInfo_decimals !== void 0 ? _tokenInfo_decimals : 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Total Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_totalSupply = tokenInfo.totalSupply) === null || _tokenInfo_totalSupply === void 0 ? void 0 : _tokenInfo_totalSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_maxSupply = tokenInfo.maxSupply) === null || _tokenInfo_maxSupply === void 0 ? void 0 : _tokenInfo_maxSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                    children: tokenInfo.paused ? '⏸️ Paused' : '▶️ Active'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Token Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice = _tokenInfo_metadata.tokenPrice) === null || _tokenInfo_metadata_tokenPrice === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice.toString()) || '0',\n                                                        \".00 \",\n                                                        ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.currency) || 'USD'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No token information available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 746,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Pause Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Token is currently paused. Unpause to allow transfers.' : 'Token is currently active. Pause to stop all transfers.'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePause,\n                                            disabled: actionLoading || !tokenInfo,\n                                            className: \"w-full px-4 py-2 rounded-lg font-medium disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                            children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '▶️ Unpause Token' : '⏸️ Pause Token'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"mintRecipient\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"mintAmount\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        const recipientInput = document.getElementById('mintRecipient');\n                                                        const amountInput = document.getElementById('mintAmount');\n                                                        const recipient = recipientInput === null || recipientInput === void 0 ? void 0 : recipientInput.value;\n                                                        const amount = amountInput === null || amountInput === void 0 ? void 0 : amountInput.value;\n                                                        console.log('🔍 Form Debug:', {\n                                                            recipientInput: recipientInput,\n                                                            amountInput: amountInput,\n                                                            recipientValue: recipient,\n                                                            amountValue: amount,\n                                                            amountInputValue: amountInput === null || amountInput === void 0 ? void 0 : amountInput.value,\n                                                            amountInputValueNumber: amountInput === null || amountInput === void 0 ? void 0 : amountInput.valueAsNumber\n                                                        });\n                                                        if (recipient && amount) {\n                                                            mintTokens(amount, recipient);\n                                                        } else {\n                                                            setError('Please enter both recipient address and amount');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading || !tokenInfo,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: actionLoading ? 'Processing...' : '🪙 Mint Tokens'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"ERC-3643 Whitelist Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium\",\n                                                    children: \"\\uD83D\\uDEE1️ Identity Registry Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-600 text-lg mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ERC-3643 Process:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \" Adding to whitelist automatically registers an OnchainID if needed, then adds to whitelist. This ensures full ERC-3643 compliance.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"whitelistAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Adding to whitelist via API...');\n                                                                        const response = await fetch('/api/admin/add-to-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist add successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist add failed:', error);\n                                                                        setError(\"Failed to add to whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: actionLoading ? 'Processing...' : '✅ Register & Whitelist'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Removing from whitelist via API...');\n                                                                        const response = await fetch('/api/admin/remove-from-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist remove successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist remove failed:', error);\n                                                                        setError(\"Failed to remove from whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: \"❌ Remove (API)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                try {\n                                                                    // First get the identity registry address using minimal ABI\n                                                                    const identityRegistryABI = [\n                                                                        \"function identityRegistryAddress() view returns (address)\"\n                                                                    ];\n                                                                    const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                                                                    if (!identityRegistryAddress) {\n                                                                        throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                                                                    }\n                                                                    // Then check whitelist status on the identity registry\n                                                                    const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                                                                        \"function isWhitelisted(address account) external view returns (bool)\"\n                                                                    ], 'isWhitelisted', [\n                                                                        address\n                                                                    ]);\n                                                                    setSuccess(\"Address \".concat(address, \" is \").concat(isWhitelisted ? '✅ whitelisted' : '❌ not whitelisted', \" (via Identity Registry: \").concat(identityRegistryAddress.slice(0, 10), \"...)\"));\n                                                                } catch (whitelistCheckError) {\n                                                                    var _whitelistCheckError_message, _whitelistCheckError_message1;\n                                                                    // Check if this is a contract compatibility issue\n                                                                    if (((_whitelistCheckError_message = whitelistCheckError.message) === null || _whitelistCheckError_message === void 0 ? void 0 : _whitelistCheckError_message.includes('execution reverted')) || ((_whitelistCheckError_message1 = whitelistCheckError.message) === null || _whitelistCheckError_message1 === void 0 ? void 0 : _whitelistCheckError_message1.includes('no data present'))) {\n                                                                        setError('⚠️ This contract does not support ERC-3643 identity registry functionality.');\n                                                                    } else {\n                                                                        throw whitelistCheckError;\n                                                                    }\n                                                                }\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to check whitelist: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD0D Check Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 810,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Balance Checker\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address to Check\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"balanceAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('balanceAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                const balance = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'balanceOf', [\n                                                                    address\n                                                                ]);\n                                                                const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                const formattedBalance = decimals > 0 ? (Number(balance || 0) / Math.pow(10, decimals)).toString() : (balance === null || balance === void 0 ? void 0 : balance.toString()) || '0';\n                                                                setSuccess(\"Balance: \".concat(formattedBalance, \" \").concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.symbol) || 'tokens'));\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to get balance: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Check Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Burn Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1156,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount to Burn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"burnAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"⚠️ This will permanently destroy tokens from your wallet. This action cannot be undone.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const amount = (_document_getElementById = document.getElementById('burnAmount')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (amount) {\n                                                            if (confirm(\"Are you sure you want to burn \".concat(amount, \" tokens? This action cannot be undone.\"))) {\n                                                                try {\n                                                                    setActionLoading(true);\n                                                                    const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                    const burnAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'burn', [\n                                                                        burnAmount\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Successfully burned \".concat(amount, \" tokens\"));\n                                                                    await loadTokenInfo();\n                                                                } catch (error) {\n                                                                    setError(\"Failed to burn tokens: \".concat(error.message));\n                                                                } finally{\n                                                                    setActionLoading(false);\n                                                                }\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an amount to burn');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD25 Burn Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1172,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1157,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1155,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Agent Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1214,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Agent Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1218,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"agentAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1221,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1217,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully added \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to add agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➕ Add Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx.wait();\n                                                                                setSuccess(\"Successfully removed \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to remove agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➖ Remove Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1274,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Note: Since AGENT_MANAGER module is not registered, we can't enumerate agents\n                                                                // Instead, we'll show how to check if specific addresses have AGENT_ROLE\n                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                // Check some common addresses for demonstration\n                                                                const addressesToCheck = [\n                                                                    '0x56f3726C92B8B92a6ab71983886F91718540d888',\n                                                                    '0x1f1a17fe870f5A0EEf1274247c080478E3d0840A' // Test address\n                                                                ];\n                                                                const agentResults = [];\n                                                                for (const address of addressesToCheck){\n                                                                    try {\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            AGENT_ROLE,\n                                                                            address\n                                                                        ]);\n                                                                        if (hasRole) {\n                                                                            agentResults.push(\"✅ \".concat(address));\n                                                                        }\n                                                                    } catch (error) {\n                                                                    // Skip addresses that cause errors\n                                                                    }\n                                                                }\n                                                                if (agentResults.length === 0) {\n                                                                    setSuccess('No agents found in checked addresses. Use \"Check Role\" to verify specific addresses.');\n                                                                } else {\n                                                                    setSuccess(\"Agents found: \".concat(agentResults.join(', ')));\n                                                                }\n                                                            } catch (error) {\n                                                                setError(\"Failed to get agents: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        },\n                                                        disabled: actionLoading,\n                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                        children: \"\\uD83D\\uDC65 Check Known Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1322,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1321,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-3\",\n                                                            children: \"\\uD83D\\uDCCB Current Agents List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1384,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentsList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1385,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1383,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1215,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1213,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Role Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1392,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1396,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"roleAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1395,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1407,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"roleSelect\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"AGENT_ROLE\",\n                                                                            children: \"AGENT_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1414,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"TRANSFER_MANAGER_ROLE\",\n                                                                            children: \"TRANSFER_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1415,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"MODULE_MANAGER_ROLE\",\n                                                                            children: \"MODULE_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1416,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DEFAULT_ADMIN_ROLE\",\n                                                                            children: \"DEFAULT_ADMIN_ROLE (⚠️ Dangerous)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1417,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1410,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1406,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1394,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully granted \".concat(roleSelect, \" to \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to grant role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"✅ Grant Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess(\"Successfully revoked \".concat(roleSelect, \" from \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to revoke role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"❌ Revoke Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1469,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        setSuccess(\"Address \".concat(address, \" \").concat(hasRole ? '✅ HAS' : '❌ DOES NOT HAVE', \" \").concat(roleSelect));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to check role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDD0D Check Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1421,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Role Descriptions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1565,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1565,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"AGENT_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can perform basic token operations\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1566,\n                                                                columnNumber: 84\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"TRANSFER_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1567,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can manage transfers and compliance\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1567,\n                                                                columnNumber: 96\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"MODULE_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1568,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can register/unregister modules\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1568,\n                                                                columnNumber: 90\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DEFAULT_ADMIN_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1569,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Full admin access (use with extreme caution)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1564,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1563,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1393,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1391,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Token Metadata\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1577,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Token Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1581,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newTokenPrice\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"100.00 USD\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1584,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1580,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Bonus Tiers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1592,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newBonusTiers\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"Early: 20%, Standard: 10%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1595,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1591,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1579,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Details/Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1604,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"newTokenDetails\",\n                                                            rows: 3,\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Token type: Carbon Credits, Debt Securities, Real Estate, etc. Additional details...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1607,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1603,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Image URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1615,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"url\",\n                                                            id: \"newTokenImageUrl\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"https://example.com/token-logo.png\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1618,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1614,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1, _document_getElementById2;\n                                                                const price = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const tiers = (_document_getElementById1 = document.getElementById('newBonusTiers')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                const details = (_document_getElementById2 = document.getElementById('newTokenDetails')) === null || _document_getElementById2 === void 0 ? void 0 : _document_getElementById2.value;\n                                                                if (price || tiers || details) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Get current values if not provided\n                                                                        const currentPrice = price || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenPrice) || '';\n                                                                        const currentTiers = tiers || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.bonusTiers) || '';\n                                                                        const currentDetails = details || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenDetails) || '';\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                            currentPrice,\n                                                                            currentTiers,\n                                                                            currentDetails\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token metadata!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update metadata: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter at least one field to update');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDCDD Update Metadata\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1626,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const imageUrl = (_document_getElementById = document.getElementById('newTokenImageUrl')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (imageUrl) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenImageUrl', [\n                                                                            imageUrl\n                                                                        ]);\n                                                                        await tx.wait();\n                                                                        setSuccess('Successfully updated token image!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update image: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an image URL');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDDBC️ Update Image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1667,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1625,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1578,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1576,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Quick Price Update\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1707,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Current Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1710,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice1 = _tokenInfo_metadata2.tokenPrice) === null || _tokenInfo_metadata_tokenPrice1 === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice1.toString()) || '0',\n                                                                \".00 \",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.currency) || 'USD'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1713,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1709,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"New Price (USD)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1718,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"newTokenPrice\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"150\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1721,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1717,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const newPrice = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (newPrice) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                console.log('🔄 Updating token price using fallback-first approach...');\n                                                                // Try direct updateTokenPrice first\n                                                                try {\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenPrice', [\n                                                                        newPrice\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD\"));\n                                                                } catch (directError) {\n                                                                    console.log('Direct method failed, trying fallback...');\n                                                                    // Fallback to updateTokenMetadata\n                                                                    const currentMetadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                                                                    const currentBonusTiers = currentMetadata[1];\n                                                                    const currentDetails = currentMetadata[2];\n                                                                    const tx = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                        newPrice,\n                                                                        currentBonusTiers,\n                                                                        currentDetails\n                                                                    ]);\n                                                                    await tx.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD (via fallback method)\"));\n                                                                }\n                                                                // Refresh token info\n                                                                await loadTokenInfo();\n                                                            } catch (error) {\n                                                                setError(\"Failed to update token price: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter a new price');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Update Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1728,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1708,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1706,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1091,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"\\uD83D\\uDEE1️ Fallback-First Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1796,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Network Resilience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1799,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Multiple RPC endpoints\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1801,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic failover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1802,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Connection testing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1803,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1800,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1798,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Error Elimination\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1807,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"missing revert data\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1809,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"Internal JSON-RPC\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1810,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Reliable contract calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1811,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1808,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1806,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ User Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1815,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Seamless operation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1817,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Clear error messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1818,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic recovery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1819,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1816,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1814,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1797,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1795,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"\\uD83D\\uDC65 Whitelisted Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1834,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Manage clients who are approved to hold \",\n                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.name) || 'this',\n                                                    \" tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1835,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1833,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: whitelistedClients.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1841,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Whitelisted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1842,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1840,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                                children: \"➕ Add Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1844,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1839,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1832,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1831,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md\",\n                            children: whitelistLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1858,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"Loading whitelisted clients...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1859,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1857,\n                                columnNumber: 17\n                            }, this) : whitelistError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-600 text-xl\",\n                                                children: \"❌\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1865,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-red-800\",\n                                                        children: \"Error Loading Whitelist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1867,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700\",\n                                                        children: whitelistError\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1868,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1866,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1864,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1863,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1862,\n                                columnNumber: 17\n                            }, this) : whitelistedClients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1875,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Whitelisted Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1876,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"No clients have been whitelisted for this token yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1877,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium\",\n                                        children: \"➕ Add First Client\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1880,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1874,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1892,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Wallet Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1895,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"KYC Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1898,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Approved Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1901,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1904,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1891,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1890,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedClients.map((client)=>{\n                                                var _client_tokenApproval;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: [\n                                                                            client.firstName,\n                                                                            \" \",\n                                                                            client.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1914,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: client.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1917,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1913,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1912,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 font-mono\",\n                                                                children: client.walletAddress\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1921,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1920,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.kycStatus === 'APPROVED' ? 'bg-green-100 text-green-800' : client.kycStatus === 'REJECTED' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                children: client.kycStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1926,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1925,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: ((_client_tokenApproval = client.tokenApproval) === null || _client_tokenApproval === void 0 ? void 0 : _client_tokenApproval.approvedAt) ? new Date(client.tokenApproval.approvedAt).toLocaleDateString() : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1936,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveFromWhitelist(client.id),\n                                                                    className: \"text-red-600 hover:text-red-900 mr-4\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1942,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/clients/\".concat(client.id),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1948,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1941,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1911,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1909,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1889,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1888,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1855,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 1829,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 624,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 623,\n        columnNumber: 5\n    }, this);\n}\n_s(ReliableTokensContent, \"mZycVj3G2vFi5jAx3YzfoFENy3g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ReliableTokensContent;\nfunction ReliableTokensPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1973,\n                        columnNumber: 9\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading token management...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 1974,\n                        columnNumber: 9\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                lineNumber: 1972,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1971,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReliableTokensContent, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1977,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 1971,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ReliableTokensPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ReliableTokensContent\");\n$RefreshReg$(_c1, \"ReliableTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reliable-tokens/page.tsx\n"));

/***/ })

});