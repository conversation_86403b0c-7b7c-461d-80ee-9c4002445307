require("@nomicfoundation/hardhat-toolbox");
require("@openzeppelin/hardhat-upgrades");

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    compilers: [
      {
        version: "0.8.20",
        settings: {
          optimizer: {
            enabled: true,
            runs: 200,
          },
        },
      },
      {
        version: "0.8.22",
        settings: {
          optimizer: {
            enabled: true,
            runs: 200,
          },
        },
      }
    ]
  },
  networks: {
    amoy: {
      url: "https://rpc-amoy.polygon.technology/",
      accounts: ["94692a030d151928530ddf7ae9f1ff07ad43d187b96135c8953fac16183619c1"],
      chainId: 80002,
    },
  },
  paths: {
    sources: "./contracts/fresh", // Only compile fresh contracts
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts"
  }
};
