'use client';

import { useState, useEffect } from 'react';

// WORKING ERC-3643 INFRASTRUCTURE (VERIFIED FUNCTIONAL ✅)
const WORKING_ERC3643_CONTRACTS = {
  workingUnderlyingRegistry: "0x1281a057881cbe94dc2a79561275aa6b35bf7854",
  workingWrapper: "0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02",
  workingTokenExample: "0x3F3e2E88542D26C22B7539b5B42328AA3e9DD303"
};

interface Token {
  id: string;
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  maxSupply: string;
  totalSupply: string;
  adminAddress: string;
  network: string;
  tokenType: string;
  hasKYC: boolean;
  isActive: boolean;
  deploymentNotes?: string;
  createdAt: string;
}

interface DeploymentForm {
  name: string;
  symbol: string;
  decimals: number;
  maxSupply: string;
  adminAddress: string;
}

export default function WorkingERC3643Page() {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [loading, setLoading] = useState(true);
  const [deploying, setDeploying] = useState(false);
  const [realDeploying, setRealDeploying] = useState(false);
  const [showDeployForm, setShowDeployForm] = useState(false);
  const [showRealDeployForm, setShowRealDeployForm] = useState(false);
  const [deploymentResult, setDeploymentResult] = useState<any>(null);
  const [form, setForm] = useState<DeploymentForm>({
    name: '',
    symbol: '',
    decimals: 18,
    maxSupply: '1000000',
    adminAddress: '******************************************'
  });

  useEffect(() => {
    fetchTokens();
  }, []);

  const fetchTokens = async () => {
    try {
      const response = await fetch('/api/tokens');
      const data = await response.json();
      
      if (data.success) {
        // Filter for working ERC-3643 tokens (those with deployment notes mentioning working infrastructure)
        const workingTokens = data.tokens.filter((token: Token) => 
          token.deploymentNotes?.includes('Fresh Working ERC-3643') ||
          token.deploymentNotes?.includes('working infrastructure') ||
          token.symbol.includes('FRESH') ||
          token.symbol.includes('WORK')
        );
        setTokens(workingTokens);
      }
    } catch (error) {
      console.error('Failed to fetch tokens:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeploy = async (e: React.FormEvent) => {
    e.preventDefault();
    setDeploying(true);
    setDeploymentResult(null);

    try {
      const response = await fetch('/api/deploy-fresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const result = await response.json();
      setDeploymentResult(result);

      if (result.success) {
        // Refresh tokens list
        await fetchTokens();
        // Reset form
        setForm({
          name: '',
          symbol: '',
          decimals: 18,
          maxSupply: '1000000',
          adminAddress: '******************************************'
        });
        setShowDeployForm(false);
      }
    } catch (error) {
      console.error('Deployment failed:', error);
      setDeploymentResult({
        success: false,
        error: 'Failed to deploy token',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setDeploying(false);
    }
  };

  const handleRealDeploy = async (e: React.FormEvent) => {
    e.preventDefault();
    setRealDeploying(true);
    setDeploymentResult(null);

    try {
      const response = await fetch('/api/deploy-real-erc3643', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const result = await response.json();
      setDeploymentResult(result);

      if (result.success) {
        // Refresh tokens list
        await fetchTokens();
        // Reset form
        setForm({
          name: '',
          symbol: '',
          decimals: 18,
          maxSupply: '1000000',
          adminAddress: '******************************************'
        });
        setShowRealDeployForm(false);
      }
    } catch (error) {
      console.error('Real deployment failed:', error);
      setDeploymentResult({
        success: false,
        error: 'Failed to deploy real token',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setRealDeploying(false);
    }
  };

  const getExplorerUrl = (address: string) => {
    return `https://amoy.polygonscan.com/address/${address}`;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading working ERC-3643 tokens...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Working ERC-3643 Tokens</h1>
        <p className="text-gray-600 mb-4">
          ERC-3643 compliant security tokens using verified working infrastructure
        </p>
        
        {/* System Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold flex items-center gap-2 text-green-600">
              ✅ System Status: HEALTHY
            </h3>
            <p className="text-gray-600 text-sm">
              All ERC-3643 infrastructure components are functional
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="font-medium">Working Registry</span>
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                ✅ VERIFIED
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="font-medium">Working Wrapper</span>
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                ✅ FUNCTIONAL
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="font-medium">Admin Verified</span>
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                ✅ READY
              </span>
            </div>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Working Registry:</strong> {WORKING_ERC3643_CONTRACTS.workingUnderlyingRegistry}</p>
            <p><strong>Working Wrapper:</strong> {WORKING_ERC3643_CONTRACTS.workingWrapper}</p>
            <p><strong>Example Token:</strong> {WORKING_ERC3643_CONTRACTS.workingTokenExample}</p>
          </div>
        </div>

        {/* Important Note */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-800 mb-2">📋 Token Deployment Plans</h3>
          <p className="text-blue-700 text-sm">
            The tokens shown below are <strong>deployment plans</strong> saved in the database.
            To deploy the actual contracts, use the provided Hardhat deployment scripts from the API responses.
          </p>
        </div>

        {/* Deploy New Token Button */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Token Plans ({tokens.length})</h2>
          <div className="flex gap-2">
            <button
              onClick={() => setShowDeployForm(!showDeployForm)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              ➕ Create Token Plan
            </button>
            <button
              onClick={() => setShowRealDeployForm(!showRealDeployForm)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              🚀 Deploy Real Contract
            </button>
          </div>
        </div>

        {/* Deployment Form */}
        {showDeployForm && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold">Deploy Fresh ERC-3643 Token</h3>
              <p className="text-gray-600 text-sm">
                Deploy a new ERC-3643 compliant token using the working infrastructure
              </p>
            </div>
            <form onSubmit={handleDeploy} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Token Name</label>
                  <input
                    id="name"
                    type="text"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    placeholder="My Security Token"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-1">Token Symbol</label>
                  <input
                    id="symbol"
                    type="text"
                    value={form.symbol}
                    onChange={(e) => setForm({ ...form, symbol: e.target.value.toUpperCase() })}
                    placeholder="MST"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-1">Decimals</label>
                  <input
                    id="decimals"
                    type="number"
                    value={form.decimals}
                    onChange={(e) => setForm({ ...form, decimals: parseInt(e.target.value) })}
                    min="0"
                    max="18"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="maxSupply" className="block text-sm font-medium text-gray-700 mb-1">Max Supply</label>
                  <input
                    id="maxSupply"
                    type="text"
                    value={form.maxSupply}
                    onChange={(e) => setForm({ ...form, maxSupply: e.target.value })}
                    placeholder="1000000"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div className="md:col-span-2">
                  <label htmlFor="adminAddress" className="block text-sm font-medium text-gray-700 mb-1">Admin Address</label>
                  <input
                    id="adminAddress"
                    type="text"
                    value={form.adminAddress}
                    onChange={(e) => setForm({ ...form, adminAddress: e.target.value })}
                    placeholder="0x..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  type="submit"
                  disabled={deploying}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
                >
                  {deploying ? 'Deploying...' : 'Deploy Token'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowDeployForm(false)}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Real Deployment Form */}
        {showRealDeployForm && (
          <div className="bg-green-50 border border-green-200 rounded-lg shadow-md p-6 mb-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-green-800">🚀 Deploy Real ERC-3643 Contract</h3>
              <p className="text-green-700 text-sm">
                Deploy an actual ERC-3643 contract to the blockchain using the working infrastructure
              </p>
              <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                ⚠️ <strong>Warning:</strong> This will deploy a real contract and consume gas fees
              </div>
            </div>
            <form onSubmit={handleRealDeploy} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="realName" className="block text-sm font-medium text-gray-700 mb-1">Token Name</label>
                  <input
                    id="realName"
                    type="text"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    placeholder="My Real Security Token"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="realSymbol" className="block text-sm font-medium text-gray-700 mb-1">Token Symbol</label>
                  <input
                    id="realSymbol"
                    type="text"
                    value={form.symbol}
                    onChange={(e) => setForm({ ...form, symbol: e.target.value.toUpperCase() })}
                    placeholder="REAL"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="realDecimals" className="block text-sm font-medium text-gray-700 mb-1">Decimals</label>
                  <input
                    id="realDecimals"
                    type="number"
                    value={form.decimals}
                    onChange={(e) => setForm({ ...form, decimals: parseInt(e.target.value) })}
                    min="0"
                    max="18"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="realMaxSupply" className="block text-sm font-medium text-gray-700 mb-1">Max Supply</label>
                  <input
                    id="realMaxSupply"
                    type="text"
                    value={form.maxSupply}
                    onChange={(e) => setForm({ ...form, maxSupply: e.target.value })}
                    placeholder="1000000"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
                <div className="md:col-span-2">
                  <label htmlFor="realAdminAddress" className="block text-sm font-medium text-gray-700 mb-1">Admin Address</label>
                  <input
                    id="realAdminAddress"
                    type="text"
                    value={form.adminAddress}
                    onChange={(e) => setForm({ ...form, adminAddress: e.target.value })}
                    placeholder="0x..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  type="submit"
                  disabled={realDeploying}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors flex items-center gap-2"
                >
                  {realDeploying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Deploying Real Contract...
                    </>
                  ) : (
                    '🚀 Deploy Real Contract'
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowRealDeployForm(false)}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Deployment Result */}
        {deploymentResult && (
          <div className={`mb-6 p-4 rounded-lg border ${deploymentResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            {deploymentResult.success ? (
              <div>
                <p className="font-medium text-green-800">✅ Token deployment plan created successfully!</p>
                <p className="text-sm text-green-700 mt-1">
                  Token saved to database. Use the provided deployment script to deploy the actual contract.
                </p>
              </div>
            ) : (
              <div>
                <p className="font-medium text-red-800">❌ Deployment failed</p>
                <p className="text-sm text-red-700 mt-1">{deploymentResult.error}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Tokens Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tokens.map((token) => (
          <div key={token.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold">{token.name}</h3>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                  ERC-3643
                </span>
              </div>
              <p className="text-gray-600 text-sm">
                {token.symbol} • {token.network.toUpperCase()}
              </p>
            </div>

            <div className="space-y-2 text-sm mb-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Max Supply:</span>
                <span className="font-medium">{parseInt(token.maxSupply).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Decimals:</span>
                <span className="font-medium">{token.decimals}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium capitalize">{token.tokenType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">KYC Required:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${token.hasKYC ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                  {token.hasKYC ? "Yes" : "No"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${token.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                  {token.isActive ? "Active" : "Inactive"}
                </span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">
                  Created: {new Date(token.createdAt).toLocaleDateString()}
                </span>
                <div className="flex gap-2">
                  {token.deploymentNotes?.includes('Fresh Working ERC-3643') ? (
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                      📋 Plan Created
                    </span>
                  ) : (
                    <button
                      onClick={() => window.open(getExplorerUrl(token.address), '_blank')}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded text-xs transition-colors flex items-center gap-1"
                    >
                      🔗 Explorer
                    </button>
                  )}
                </div>
              </div>
              {token.deploymentNotes?.includes('Fresh Working ERC-3643') && (
                <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
                  💡 <strong>Next Step:</strong> Use the deployment script to deploy the actual contract
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {tokens.length === 0 && (
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <p className="text-gray-500 mb-4">No working ERC-3643 token plans found</p>
          <button
            onClick={() => setShowDeployForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Create Your First Token Plan
          </button>
        </div>
      )}
    </div>
  );
}
