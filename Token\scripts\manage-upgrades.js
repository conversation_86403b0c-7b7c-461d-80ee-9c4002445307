const { ethers, upgrades } = require('hardhat');

async function manageUpgrades() {
  console.log('🔧 UPGRADE MANAGEMENT TOOL');
  console.log('='.repeat(80));

  const [signer] = await ethers.getSigners();
  console.log('👤 Signer:', signer.address);
  console.log('');

  // Get proxy address from command line or environment
  const proxyAddress = process.env.PROXY_ADDRESS || process.argv[2];
  const action = process.env.ACTION || process.argv[3];

  if (!proxyAddress) {
    console.log('❌ Please provide proxy address:');
    console.log('   node scripts/manage-upgrades.js <PROXY_ADDRESS> <ACTION>');
    console.log('');
    console.log('Available actions:');
    console.log('   status       - Check upgrade status');
    console.log('   propose      - Propose new upgrade');
    console.log('   execute      - Execute pending upgrade');
    console.log('   cancel       - Cancel pending upgrade');
    console.log('   renounce     - Permanently disable upgrades');
    console.log('   timelock     - Increase timelock period');
    return;
  }

  console.log(`📍 Proxy Address: ${proxyAddress}`);
  console.log(`🎯 Action: ${action || 'status'}`);
  console.log('');

  try {
    // Connect to the proxy
    const UpgradeableERC3643Token = await ethers.getContractFactory('UpgradeableERC3643Token');
    const token = UpgradeableERC3643Token.attach(proxyAddress);

    // Check if signer has upgrader role
    const UPGRADER_ROLE = await token.UPGRADER_ROLE();
    const DEFAULT_ADMIN_ROLE = await token.DEFAULT_ADMIN_ROLE();
    const hasUpgraderRole = await token.hasRole(UPGRADER_ROLE, signer.address);
    const hasAdminRole = await token.hasRole(DEFAULT_ADMIN_ROLE, signer.address);

    console.log('🔍 PERMISSION CHECK:');
    console.log(`   Has UPGRADER_ROLE: ${hasUpgraderRole}`);
    console.log(`   Has DEFAULT_ADMIN_ROLE: ${hasAdminRole}`);
    console.log('');

    switch (action) {
      case 'status':
      default:
        await checkUpgradeStatus(token);
        break;
      
      case 'propose':
        if (!hasUpgraderRole) {
          console.log('❌ You do not have UPGRADER_ROLE');
          return;
        }
        await proposeUpgrade(token);
        break;
      
      case 'execute':
        if (!hasUpgraderRole) {
          console.log('❌ You do not have UPGRADER_ROLE');
          return;
        }
        await executeUpgrade(token);
        break;
      
      case 'cancel':
        if (!hasUpgraderRole) {
          console.log('❌ You do not have UPGRADER_ROLE');
          return;
        }
        await cancelUpgrade(token);
        break;
      
      case 'renounce':
        if (!hasAdminRole) {
          console.log('❌ You do not have DEFAULT_ADMIN_ROLE');
          return;
        }
        await renounceUpgrades(token);
        break;
      
      case 'timelock':
        if (!hasAdminRole) {
          console.log('❌ You do not have DEFAULT_ADMIN_ROLE');
          return;
        }
        await increaseTimelock(token);
        break;
      
      default:
        console.log('❌ Unknown action:', action);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function checkUpgradeStatus(token) {
  console.log('📊 UPGRADE STATUS');
  console.log('-'.repeat(60));

  try {
    const upgradesRenounced = await token.upgradesRenounced();
    const upgradeTimelock = await token.upgradeTimelock();
    const pendingImplementation = await token.pendingImplementation();
    const pendingUpgradeTimestamp = await token.pendingUpgradeTimestamp();

    console.log(`🔒 Upgrades Renounced: ${upgradesRenounced}`);
    console.log(`⏰ Upgrade Timelock: ${upgradeTimelock / 86400} days`);
    console.log(`📋 Pending Implementation: ${pendingImplementation}`);
    
    if (pendingImplementation !== ethers.ZeroAddress) {
      const now = Math.floor(Date.now() / 1000);
      const timeLeft = Number(pendingUpgradeTimestamp) - now;
      
      console.log(`⏳ Pending Upgrade Timestamp: ${new Date(Number(pendingUpgradeTimestamp) * 1000).toLocaleString()}`);
      
      if (timeLeft > 0) {
        console.log(`⏳ Time until executable: ${Math.floor(timeLeft / 86400)} days, ${Math.floor((timeLeft % 86400) / 3600)} hours`);
      } else {
        console.log('✅ Upgrade ready to execute!');
      }
    } else {
      console.log('ℹ️ No pending upgrade');
    }

    // Get current implementation
    const currentImplementation = await upgrades.erc1967.getImplementationAddress(await token.getAddress());
    console.log(`🔧 Current Implementation: ${currentImplementation}`);

  } catch (error) {
    console.error('❌ Error checking status:', error.message);
  }
}

async function proposeUpgrade(token) {
  console.log('📝 PROPOSING UPGRADE');
  console.log('-'.repeat(60));

  const newImplementationAddress = process.env.NEW_IMPLEMENTATION || process.argv[4];
  
  if (!newImplementationAddress) {
    console.log('❌ Please provide new implementation address:');
    console.log('   node scripts/manage-upgrades.js <PROXY> propose <NEW_IMPLEMENTATION>');
    return;
  }

  try {
    console.log(`🎯 New Implementation: ${newImplementationAddress}`);
    
    // Validate implementation address
    const code = await ethers.provider.getCode(newImplementationAddress);
    if (code === '0x') {
      console.log('❌ No contract code found at implementation address');
      return;
    }

    console.log('📤 Proposing upgrade...');
    const tx = await token.proposeUpgrade(newImplementationAddress);
    console.log(`📋 Transaction hash: ${tx.hash}`);
    
    const receipt = await tx.wait();
    console.log('✅ Upgrade proposed successfully!');
    
    // Get the timelock info
    const upgradeTimelock = await token.upgradeTimelock();
    const executeAfter = Math.floor(Date.now() / 1000) + Number(upgradeTimelock);
    
    console.log(`⏰ Executable after: ${new Date(executeAfter * 1000).toLocaleString()}`);
    console.log(`⏳ Timelock period: ${upgradeTimelock / 86400} days`);

  } catch (error) {
    console.error('❌ Error proposing upgrade:', error.message);
  }
}

async function executeUpgrade(token) {
  console.log('🚀 EXECUTING UPGRADE');
  console.log('-'.repeat(60));

  try {
    const pendingImplementation = await token.pendingImplementation();
    
    if (pendingImplementation === ethers.ZeroAddress) {
      console.log('❌ No pending upgrade to execute');
      return;
    }

    console.log(`🎯 Executing upgrade to: ${pendingImplementation}`);
    
    const tx = await token.executeUpgrade();
    console.log(`📋 Transaction hash: ${tx.hash}`);
    
    const receipt = await tx.wait();
    console.log('✅ Upgrade executed successfully!');
    
    // Verify the upgrade
    const newImplementation = await upgrades.erc1967.getImplementationAddress(await token.getAddress());
    console.log(`🔧 New Implementation: ${newImplementation}`);

  } catch (error) {
    console.error('❌ Error executing upgrade:', error.message);
  }
}

async function cancelUpgrade(token) {
  console.log('❌ CANCELING UPGRADE');
  console.log('-'.repeat(60));

  try {
    const pendingImplementation = await token.pendingImplementation();
    
    if (pendingImplementation === ethers.ZeroAddress) {
      console.log('❌ No pending upgrade to cancel');
      return;
    }

    console.log(`🎯 Canceling upgrade to: ${pendingImplementation}`);
    
    const tx = await token.cancelUpgrade();
    console.log(`📋 Transaction hash: ${tx.hash}`);
    
    const receipt = await tx.wait();
    console.log('✅ Upgrade canceled successfully!');

  } catch (error) {
    console.error('❌ Error canceling upgrade:', error.message);
  }
}

async function renounceUpgrades(token) {
  console.log('🔒 RENOUNCING UPGRADES (PERMANENT)');
  console.log('-'.repeat(60));

  console.log('⚠️ WARNING: This action is IRREVERSIBLE!');
  console.log('⚠️ After renouncing, the contract can NEVER be upgraded again.');
  console.log('');

  // In a real scenario, you'd want additional confirmation
  const confirm = process.env.CONFIRM_RENOUNCE || process.argv[4];
  
  if (confirm !== 'CONFIRM') {
    console.log('❌ To confirm, run:');
    console.log('   node scripts/manage-upgrades.js <PROXY> renounce CONFIRM');
    return;
  }

  try {
    console.log('🔒 Renouncing all future upgrades...');
    
    const tx = await token.renounceUpgrades();
    console.log(`📋 Transaction hash: ${tx.hash}`);
    
    const receipt = await tx.wait();
    console.log('✅ Upgrades renounced successfully!');
    console.log('🔒 This contract is now permanently immutable.');

  } catch (error) {
    console.error('❌ Error renouncing upgrades:', error.message);
  }
}

async function increaseTimelock(token) {
  console.log('⏰ INCREASING TIMELOCK');
  console.log('-'.repeat(60));

  const newTimelock = process.env.NEW_TIMELOCK || process.argv[4];
  
  if (!newTimelock) {
    console.log('❌ Please provide new timelock in seconds:');
    console.log('   node scripts/manage-upgrades.js <PROXY> timelock <SECONDS>');
    console.log('');
    console.log('Examples:');
    console.log('   2592000  = 30 days');
    console.log('   7776000  = 90 days');
    console.log('   31536000 = 365 days');
    return;
  }

  try {
    const currentTimelock = await token.upgradeTimelock();
    const newTimelockBN = ethers.getBigInt(newTimelock);
    
    console.log(`⏰ Current Timelock: ${currentTimelock / 86400n} days`);
    console.log(`⏰ New Timelock: ${newTimelockBN / 86400n} days`);
    
    if (newTimelockBN <= currentTimelock) {
      console.log('❌ New timelock must be greater than current timelock');
      return;
    }

    console.log('⏰ Increasing timelock...');
    
    const tx = await token.increaseTimelock(newTimelockBN);
    console.log(`📋 Transaction hash: ${tx.hash}`);
    
    const receipt = await tx.wait();
    console.log('✅ Timelock increased successfully!');

  } catch (error) {
    console.error('❌ Error increasing timelock:', error.message);
  }
}

// Run if called directly
if (require.main === module) {
  manageUpgrades()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { manageUpgrades };
