const { ethers } = require('hardhat');

// Configuration
const WRAPPER_ADDRESS = '******************************************';
const UNDERLYING_REGISTRY = '******************************************';
const ADMIN_ADDRESS = '******************************************';

async function testWrapper() {
  console.log('🧪 TESTING WRAPPER FUNCTIONALITY');
  console.log('='.repeat(50));
  console.log('');

  try {
    const [deployer] = await ethers.getSigners();
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`🆔 Wrapper: ${WRAPPER_ADDRESS}`);
    console.log(`🔗 Underlying Registry: ${UNDERLYING_REGISTRY}`);
    console.log(`👑 Admin: ${ADMIN_ADDRESS}`);
    console.log('');

    // Test underlying registry directly
    console.log('🔍 TESTING UNDERLYING REGISTRY DIRECTLY');
    console.log('-'.repeat(40));
    
    const underlyingABI = [
      "function isVerified(address userAddress) external view returns (bool)"
    ];
    
    const underlying = new ethers.Contract(UNDERLYING_REGISTRY, underlyingABI, deployer);
    
    try {
      const underlyingResult = await underlying.isVerified(ADMIN_ADDRESS);
      console.log(`✅ Underlying registry result: ${underlyingResult}`);
    } catch (underlyingError) {
      console.log(`❌ Underlying registry failed: ${underlyingError.message}`);
      
      // Try with call static
      try {
        const callResult = await deployer.provider.call({
          to: UNDERLYING_REGISTRY,
          data: underlying.interface.encodeFunctionData('isVerified', [ADMIN_ADDRESS])
        });
        console.log(`📊 Raw call result: ${callResult}`);
        
        if (callResult === '0x') {
          console.log('❌ Underlying registry returns empty data');
        } else {
          const decoded = underlying.interface.decodeFunctionResult('isVerified', callResult);
          console.log(`✅ Decoded result: ${decoded[0]}`);
        }
      } catch (callError) {
        console.log(`❌ Raw call also failed: ${callError.message}`);
      }
    }
    console.log('');

    // Test wrapper
    console.log('🔍 TESTING WRAPPER');
    console.log('-'.repeat(40));
    
    const wrapperABI = [
      "function isVerified(address userAddress) external view returns (bool)",
      "function underlyingRegistry() external view returns (address)"
    ];
    
    const wrapper = new ethers.Contract(WRAPPER_ADDRESS, wrapperABI, deployer);
    
    // Check wrapper's underlying registry
    try {
      const wrapperUnderlying = await wrapper.underlyingRegistry();
      console.log(`🔗 Wrapper's underlying registry: ${wrapperUnderlying}`);
      console.log(`🔍 Matches expected: ${wrapperUnderlying.toLowerCase() === UNDERLYING_REGISTRY.toLowerCase()}`);
    } catch (error) {
      console.log(`❌ Could not get wrapper's underlying registry: ${error.message}`);
    }
    
    // Test wrapper isVerified
    try {
      const wrapperResult = await wrapper.isVerified(ADMIN_ADDRESS);
      console.log(`✅ Wrapper result: ${wrapperResult}`);
    } catch (wrapperError) {
      console.log(`❌ Wrapper failed: ${wrapperError.message}`);
      
      // Try with call static
      try {
        const callResult = await deployer.provider.call({
          to: WRAPPER_ADDRESS,
          data: wrapper.interface.encodeFunctionData('isVerified', [ADMIN_ADDRESS])
        });
        console.log(`📊 Wrapper raw call result: ${callResult}`);
        
        if (callResult === '0x') {
          console.log('❌ Wrapper returns empty data');
        } else {
          const decoded = wrapper.interface.decodeFunctionResult('isVerified', callResult);
          console.log(`✅ Wrapper decoded result: ${decoded[0]}`);
        }
      } catch (callError) {
        console.log(`❌ Wrapper raw call also failed: ${callError.message}`);
      }
    }

    console.log('');
    console.log('🎯 DIAGNOSIS:');
    console.log('If both underlying registry and wrapper return empty data (0x),');
    console.log('it means the contracts are reverting without a reason string.');
    console.log('This suggests the underlying registry is still broken.');
    console.log('');
    console.log('💡 SOLUTION:');
    console.log('We need to create a completely independent identity system');
    console.log('that doesn\'t rely on the broken underlying registry.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Execute test
testWrapper().catch(console.error);
