"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-erc3643-token/page",{

/***/ "(app-pages-browser)/./src/app/create-erc3643-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-erc3643-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateERC3643TokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/factory.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract ABIs - Updated for ERC-3643 compliant tokens\nconst SecurityTokenCoreABI = [\n    \"function initialize(string memory name_, string memory symbol_, uint8 decimals_, uint256 maxSupply_, address admin_, string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_, string memory tokenImageUrl_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\",\n    \"function totalSupply() view returns (uint256)\",\n    \"function maxSupply() view returns (uint256)\",\n    \"function identityRegistry() view returns (address)\",\n    \"function compliance() view returns (address)\",\n    \"function onchainID() view returns (address)\",\n    \"function isERC3643Compliant() view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst IdentityRegistryABI = [\n    \"function initialize(address admin_, address claimRegistry_) external\"\n];\nconst ComplianceABI = [\n    \"function initialize(address admin_, address identityRegistry_) external\"\n];\nconst TrustedIssuersRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function addTrustedIssuer(address trustedIssuer, uint256[] calldata claimTopics) external\"\n];\nconst ClaimTopicsRegistryABI = [\n    \"function initialize(address admin_) external\",\n    \"function batchAddClaimTopics(uint256[] calldata topics) external\"\n];\nconst IdentityContractFactoryABI = [\n    \"function initialize(address admin_) external\"\n];\nconst ERC3643TokenWrapperABI = [\n    \"function initialize(address underlyingToken_, address erc3643IdentityRegistry_, address erc3643Compliance_, address admin_, address onchainID_, string memory version_) external\",\n    \"function name() view returns (string)\",\n    \"function symbol() view returns (string)\",\n    \"function decimals() view returns (uint8)\"\n];\n// Available claim types for token compliance requirements\nconst AVAILABLE_CLAIMS = [\n    {\n        id: 1,\n        name: 'KYC Claim',\n        description: 'Know Your Customer verification',\n        required: true\n    },\n    {\n        id: 2,\n        name: 'AML Claim',\n        description: 'Anti-Money Laundering compliance',\n        required: false\n    },\n    {\n        id: 3,\n        name: 'Identity Claim',\n        description: 'Identity verification',\n        required: false\n    },\n    {\n        id: 4,\n        name: 'Qualification Claim',\n        description: 'Investor qualification status',\n        required: true\n    },\n    {\n        id: 5,\n        name: 'Accreditation Claim',\n        description: 'Accredited investor status',\n        required: false\n    },\n    {\n        id: 6,\n        name: 'Residence Claim',\n        description: 'Residence verification',\n        required: false\n    },\n    {\n        id: 7,\n        name: 'Token Issuer Claim',\n        description: 'Token issuer authorization',\n        required: false\n    }\n];\nfunction CreateERC3643TokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [upgradeableDeploying, setUpgradeableDeploying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 2,\n        maxSupply: '1000000',\n        adminAddress: '',\n        tokenPrice: '100.00',\n        currency: 'USD',\n        bonusTiers: 'Early: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'ERC-3643 compliant security token with built-in identity registry and compliance modules',\n        tokenImageUrl: '',\n        enableERC3643: true,\n        initialClaimTopics: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ],\n        selectedClaims: [\n            1,\n            4\n        ] // Default: KYC + Qualification claims required\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateERC3643TokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateERC3643TokenPage.useEffect\"], []);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'MATIC');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.1')) {\n                    setError('Insufficient balance. You need at least 0.1 MATIC for gas fees to deploy all contracts.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployContract = async function(contractName, bytecode, abi) {\n        let constructorArgs = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        if (!signer) throw new Error('No signer available');\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory(abi, bytecode, signer);\n        const contract = await factory.deploy(...constructorArgs);\n        await contract.waitForDeployment();\n        const address = await contract.getAddress();\n        console.log(\"\".concat(contractName, \" deployed to:\"), address);\n        return address;\n    };\n    const deployProxyContract = async (contractName, implementationAddress, initData)=>{\n        if (!signer) throw new Error('No signer available');\n        // ERC1967Proxy bytecode (simplified)\n        const proxyBytecode = \"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\";\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.ContractFactory([\n            \"constructor(address implementation, bytes memory data)\"\n        ], proxyBytecode, signer);\n        const proxy = await factory.deploy(implementationAddress, initData);\n        await proxy.waitForDeployment();\n        const address = await proxy.getAddress();\n        console.log(\"\".concat(contractName, \" proxy deployed to:\"), address);\n        return address;\n    };\n    const deployFullFeaturedToken = async ()=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Checking working ERC-3643 infrastructure...');\n            // First, try to deploy a real contract using our working infrastructure\n            setDeploymentStep('🚀 Deploying real ERC-3643 contract with working infrastructure...');\n            const realDeployResponse = await fetch('/api/deploy-real-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy'\n                })\n            });\n            const realResult = await realDeployResponse.json();\n            if (realResult.success) {\n                // Real deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: realResult.realTokenAddress,\n                    erc3643TokenWrapper: realResult.wrapperAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: realResult.realTokenAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: realResult.adminVerified,\n                    contracts: realResult.contracts,\n                    explorerUrls: realResult.explorerUrls,\n                    features: realResult.features,\n                    deploymentType: 'Real ERC-3643 Contract',\n                    erc3643Components: {\n                        workingWrapper: realResult.wrapperAddress,\n                        workingUnderlyingRegistry: realResult.contracts.workingUnderlyingRegistry,\n                        tokenAddress: realResult.realTokenAddress\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Real ERC-3643 Token deployed successfully using working infrastructure!\\n\\n✅ Token Address: \".concat(realResult.realTokenAddress, \"\\n✅ Wrapper Address: \").concat(realResult.wrapperAddress, \"\\n✅ Admin Verified: \").concat(realResult.adminVerified ? 'Yes' : 'No', \"\\n✅ Ready for institutional use!\\n\\n\\uD83D\\uDD17 View on Explorer: https://amoy.polygonscan.com/address/\").concat(realResult.realTokenAddress));\n                setDeploymentStep('');\n            } else {\n                // Real deployment failed, fall back to deployment plan\n                setDeploymentStep('⚠️ Real deployment failed, creating deployment plan with working infrastructure...');\n                const planResponse = await fetch('/api/deploy-fresh-token', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        decimals: formData.decimals,\n                        maxSupply: formData.maxSupply,\n                        adminAddress: formData.adminAddress,\n                        network: 'amoy'\n                    })\n                });\n                const planResult = await planResponse.json();\n                if (planResult.success) {\n                    var _planResult_systemStatus, _planResult_systemStatus1;\n                    setDeployedToken({\n                        success: true,\n                        securityTokenCore: planResult.primaryTokenAddress,\n                        erc3643TokenWrapper: planResult.contracts.workingWrapper,\n                        name: formData.name,\n                        symbol: formData.symbol,\n                        primaryTokenAddress: planResult.primaryTokenAddress,\n                        isERC3643Compliant: true,\n                        systemStatus: planResult.systemStatus,\n                        contracts: planResult.contracts,\n                        deploymentScript: planResult.deploymentScript,\n                        explorerUrls: planResult.explorerUrls,\n                        features: planResult.features,\n                        deploymentType: 'Deployment Plan (Working Infrastructure)',\n                        erc3643Components: {\n                            workingWrapper: planResult.contracts.workingWrapper,\n                            workingUnderlyingRegistry: planResult.contracts.workingUnderlyingRegistry,\n                            planAddress: planResult.primaryTokenAddress\n                        }\n                    });\n                    setSuccess(\"\\uD83D\\uDCCB ERC-3643 Token deployment plan created successfully using working infrastructure!\\n\\n✅ Plan Address: \".concat(planResult.primaryTokenAddress, \"\\n✅ System Status: \").concat(((_planResult_systemStatus = planResult.systemStatus) === null || _planResult_systemStatus === void 0 ? void 0 : _planResult_systemStatus.systemHealthy) ? 'Healthy' : 'Issues Detected', \"\\n✅ Working Infrastructure: Ready\\n✅ Admin Verified: \").concat(((_planResult_systemStatus1 = planResult.systemStatus) === null || _planResult_systemStatus1 === void 0 ? void 0 : _planResult_systemStatus1.adminVerified) ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Use the provided deployment script to deploy the actual contract.\\n\\uD83D\\uDD17 Working Infrastructure: All components verified and functional\"));\n                    setDeploymentStep('');\n                } else {\n                    throw new Error(\"Both real deployment and plan creation failed: \".concat(planResult.error));\n                }\n            }\n        } catch (error) {\n            console.error('Error deploying token:', error);\n            setError(\"Failed to deploy token using working infrastructure: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while trying to use the verified working ERC-3643 infrastructure.\\n\\uD83D\\uDCA1 The working infrastructure includes:\\n   - Working Identity Registry: 0x1281a057881cbe94dc2a79561275aa6b35bf7854\\n   - Working Wrapper: 0x2221efb82f13BF88FE09f4A5A89C40677bAAAa02\\n   - Admin Verification: Confirmed working\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setIsSubmitting(false);\n            setDeploymentStep('');\n        }\n    };\n    const deployUpgradeableToken = async ()=>{\n        setUpgradeableDeploying(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        try {\n            setDeploymentStep('🔍 Deploying upgradeable ERC-3643 token with working infrastructure...');\n            const response = await fetch('/api/deploy-upgradeable-erc3643', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    decimals: formData.decimals,\n                    maxSupply: formData.maxSupply,\n                    adminAddress: formData.adminAddress,\n                    network: 'amoy'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // Upgradeable deployment succeeded\n                setDeployedToken({\n                    success: true,\n                    securityTokenCore: result.proxyAddress,\n                    erc3643TokenWrapper: result.implementationAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    primaryTokenAddress: result.proxyAddress,\n                    isERC3643Compliant: true,\n                    adminVerified: result.adminVerified,\n                    contracts: result.contracts,\n                    explorerUrls: result.explorerUrls,\n                    features: result.features,\n                    deploymentType: 'Upgradeable ERC-3643 Contract',\n                    upgradeInfo: result.upgradeInfo,\n                    upgradeManagement: result.upgradeManagement,\n                    erc3643Components: {\n                        proxy: result.proxyAddress,\n                        implementation: result.implementationAddress,\n                        identityRegistry: result.contracts.identityRegistry\n                    }\n                });\n                setSuccess(\"\\uD83C\\uDF89 Upgradeable ERC-3643 Token deployed successfully!\\n\\n✅ Proxy Address: \".concat(result.proxyAddress, \"\\n✅ Implementation: \").concat(result.implementationAddress, \"\\n✅ Admin Verified: \").concat(result.adminVerified ? 'Yes' : 'No', \"\\n✅ Upgradeable: \").concat(result.upgradeInfo.canUpgrade ? 'Yes' : 'Renounced', \"\\n✅ Upgrade Timelock: \").concat(result.upgradeInfo.upgradeTimelock, \"\\n✅ Ready for institutional use with optional upgradeability!\\n\\n\\uD83D\\uDD17 View Proxy: https://amoy.polygonscan.com/address/\").concat(result.proxyAddress, \"\\n\\uD83D\\uDD17 View Implementation: https://amoy.polygonscan.com/address/\").concat(result.implementationAddress));\n                setDeploymentStep('');\n            } else {\n                throw new Error(\"Upgradeable deployment failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Upgradeable deployment error:', error);\n            setError(\"Failed to deploy upgradeable token: \".concat(error.message, \"\\n\\n\\uD83D\\uDD0D This error occurred while deploying the upgradeable ERC-3643 token.\\n\\uD83D\\uDCA1 The upgradeable token includes:\\n   - UUPS proxy pattern for gas efficiency\\n   - Optional upgrade renunciation for maximum trust\\n   - 30-day timelock protection\\n   - All ERC-3643 compliance features\\n\\nPlease try again or contact support if the issue persists.\"));\n        } finally{\n            setUpgradeableDeploying(false);\n            setDeploymentStep('');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployFullFeaturedToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create ERC-3643 Compliant Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create fully ERC-3643 compliant security tokens with built-in identity registry and compliance modules.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n            lineNumber: 470,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create ERC-3643 Compliant Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800\",\n                        children: \"✅ Working Infrastructure\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"✅\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"ERC-3643 Compliant Security Token with Working Infrastructure\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Deploy fully compliant ERC-3643 security tokens using verified working infrastructure:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Working Infrastructure:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Uses verified functional Identity Registry and Wrapper\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Admin Pre-Verified:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Admin address is already verified and ready to use\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Real Contract Deployment:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Attempts to deploy actual contracts first\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Fallback Plan:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Creates deployment plan if real deployment fails\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Full ERC-3643 Compliance:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" All standard functions and security features\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"✅ Institutional Ready:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Freeze, force transfer, whitelist, KYC included\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"\\uD83C\\uDF89 Now using verified working ERC-3643 infrastructure!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" All components tested and functional.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, this),\n            isSubmitting && deploymentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-600 mr-2\",\n                            children: \"⏳\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: deploymentStep\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 539,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 549,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 559,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.securityTokenCore\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this),\n                            deployedToken.erc3643TokenWrapper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ERC-3643 Wrapper:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.erc3643TokenWrapper\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.securityTokenCore), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 569,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Full Featured Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., FFST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    \"Decimals * \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"(0=shares, 2=currency, 18=crypto)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals \",\n                                                            i === 0 ? '(whole numbers)' : i === 2 ? '(currency-like)' : i === 18 ? '(crypto-like)' : ''\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"100.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 778,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                        children: \"\\uD83D\\uDCCB Required Claims for Token Compliance\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-700 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"\\uD83D\\uDD17 Shared Compliance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" Claims are issued to client OnchainIDs and can be shared across multiple tokens. Select which claims investors must have to hold this token.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: AVAILABLE_CLAIMS.map((claim)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-start space-x-3 p-2 bg-white rounded border hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: formData.selectedClaims.includes(claim.id),\n                                                                onChange: (e)=>{\n                                                                    const newClaims = e.target.checked ? [\n                                                                        ...formData.selectedClaims,\n                                                                        claim.id\n                                                                    ] : formData.selectedClaims.filter((id)=>id !== claim.id);\n                                                                    setFormData({\n                                                                        ...formData,\n                                                                        selectedClaims: newClaims\n                                                                    });\n                                                                },\n                                                                disabled: claim.required,\n                                                                className: \"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                                children: claim.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                                lineNumber: 816,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            claim.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\",\n                                                                                children: \"Required\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                                lineNumber: 818,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                        lineNumber: 815,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 mt-1\",\n                                                                        children: claim.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, claim.id, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-yellow-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Selected Claims:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" \",\n                                                        formData.selectedClaims.length > 0 ? AVAILABLE_CLAIMS.filter((c)=>formData.selectedClaims.includes(c.id)).map((c)=>c.name).join(', ') : 'None selected'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 794,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-lg mr-2\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-green-800\",\n                                                        children: \"ERC-3643 Compliance Enabled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-700 mt-1\",\n                                                        children: \"All tokens are now ERC-3643 compliant by default with built-in identityRegistry(), compliance(), and onchainID() functions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                        children: isSubmitting ? '🔄 Creating ERC-3643 Compliant Token...' : '🏆 Create ERC-3643 Compliant Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: deployUpgradeableToken,\n                                        disabled: isSubmitting || upgradeableDeploying,\n                                        className: \"w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50 border-2 border-green-500\",\n                                        children: upgradeableDeploying ? '🔄 Creating Upgradeable ERC-3643 Token...' : '🔧 Create Upgradeable ERC-3643 Token'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 bg-gray-50 p-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83C\\uDFC6 Standard:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Immutable contract (maximum security, no upgrades possible)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"\\uD83D\\uDD27 Upgradeable:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" UUPS proxy pattern (can be upgraded with 30-day timelock, can renounce upgrades)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 852,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 610,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 text-xl\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 887,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-800\",\n                                    children: \"All Features Included\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every token deployed includes: Configurable decimals, Upgradeable architecture, Role-based access control, Mint/burn capabilities, Force transfer, Address freezing, On-chain whitelist/KYC, Batch operations, Recovery mechanisms, and comprehensive events.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 font-medium text-green-700\",\n                                            children: \"Plus ERC-3643 Compliance: Built-in identityRegistry(), compliance(), onchainID() functions, Individual identity contracts, Claims-based verification, Trusted issuers registry, Compliance engine, and institutional-grade features.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                            lineNumber: 890,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                    lineNumber: 886,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n                lineNumber: 885,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-erc3643-token\\\\page.tsx\",\n        lineNumber: 495,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateERC3643TokenPage, \"2A/VVdRtAzqizOs+YKhylSRNa+4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateERC3643TokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateERC3643TokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-erc3643-token/page.tsx\n"));

/***/ })

});