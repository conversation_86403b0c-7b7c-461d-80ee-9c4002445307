{"_format": "hh-sol-artifact-1", "contractName": "SimpleFreshToken", "sourceName": "contracts/fresh/SimpleFreshToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "uint256", "name": "maxSupply_", "type": "uint256"}, {"internalType": "address", "name": "_identityRegistry", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Burn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ForcedTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensFrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensUnfrozen", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "AGENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "availableBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "compliance", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "forcedTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "freezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "frozenBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "identityRegistry", "outputs": [{"internalType": "contract IWorkingIdentityRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unfreezeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}