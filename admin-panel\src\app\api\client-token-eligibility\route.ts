import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { ethers } from 'ethers';

// Available claim types
const CLAIM_TYPES = {
  KYC_CLAIM: 1,
  AML_CLAIM: 2,
  IDENTITY_CLAIM: 3,
  QUALIFICATION_CLAIM: 4,
  ACCREDITATION_CLAIM: 5,
  RESIDENCE_CLAIM: 6,
  TOKEN_ISSUER_CLAIM: 7
};

const CLAIM_NAMES = {
  1: 'KYC Claim',
  2: 'AML Claim',
  3: 'Identity Claim',
  4: 'Qualification Claim',
  5: 'Accreditation Claim',
  6: 'Residence Claim',
  7: 'Token Issuer Claim'
};

// GET /api/client-token-eligibility - Check which tokens a client is eligible for
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const walletAddress = searchParams.get('walletAddress');
    const onchainIdAddress = searchParams.get('onchainIdAddress');

    if (!clientId && !walletAddress && !onchainIdAddress) {
      return NextResponse.json(
        { error: 'Either clientId, walletAddress, or onchainIdAddress is required' },
        { status: 400 }
      );
    }

    // Find client
    let client = null;
    if (clientId) {
      client = await prisma.client.findUnique({
        where: { id: clientId }
      });
    } else if (walletAddress) {
      client = await prisma.client.findUnique({
        where: { walletAddress }
      });
    } else if (onchainIdAddress) {
      client = await prisma.client.findUnique({
        where: { onchainIdAddress }
      });
    }

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Get client's claims from blockchain (if they have an OnchainID)
    let clientClaims: number[] = [];
    if (client.onchainIdAddress) {
      try {
        clientClaims = await getClientClaimsFromBlockchain(client.onchainIdAddress);
      } catch (error) {
        console.warn('Could not fetch claims from blockchain:', error);
        // Fall back to database-based claims
        clientClaims = await getClientClaimsFromDatabase(client.id);
      }
    } else {
      // Use database-based claims
      clientClaims = await getClientClaimsFromDatabase(client.id);
    }

    // Get all tokens and their claim requirements
    const tokens = await prisma.token.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        selectedClaims: true,
        tokenPrice: true,
        currency: true,
        network: true,
        tokenType: true
      }
    });

    // Check eligibility for each token
    const eligibilityResults = tokens.map(token => {
      const requiredClaims = token.selectedClaims 
        ? token.selectedClaims.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
        : [1, 4]; // Default: KYC + Qualification

      const hasAllRequiredClaims = requiredClaims.every(claimId => clientClaims.includes(claimId));
      const missingClaims = requiredClaims.filter(claimId => !clientClaims.includes(claimId));

      return {
        token: {
          id: token.id,
          name: token.name,
          symbol: token.symbol,
          address: token.address,
          tokenPrice: token.tokenPrice,
          currency: token.currency,
          network: token.network,
          tokenType: token.tokenType
        },
        eligible: hasAllRequiredClaims,
        requiredClaims: requiredClaims.map(id => ({
          id,
          name: CLAIM_NAMES[id as keyof typeof CLAIM_NAMES] || `Claim ${id}`,
          hasIt: clientClaims.includes(id)
        })),
        missingClaims: missingClaims.map(id => ({
          id,
          name: CLAIM_NAMES[id as keyof typeof CLAIM_NAMES] || `Claim ${id}`
        }))
      };
    });

    const eligibleTokens = eligibilityResults.filter(result => result.eligible);
    const ineligibleTokens = eligibilityResults.filter(result => !result.eligible);

    return NextResponse.json({
      success: true,
      client: {
        id: client.id,
        name: `${client.firstName} ${client.lastName}`,
        walletAddress: client.walletAddress,
        onchainIdAddress: client.onchainIdAddress,
        kycStatus: client.kycStatus
      },
      clientClaims: clientClaims.map(id => ({
        id,
        name: CLAIM_NAMES[id as keyof typeof CLAIM_NAMES] || `Claim ${id}`
      })),
      eligibility: {
        totalTokens: tokens.length,
        eligibleCount: eligibleTokens.length,
        ineligibleCount: ineligibleTokens.length,
        eligibleTokens,
        ineligibleTokens
      }
    });

  } catch (error) {
    console.error('Error checking client token eligibility:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to get client claims from blockchain
async function getClientClaimsFromBlockchain(onchainIdAddress: string): Promise<number[]> {
  const provider = new ethers.JsonRpcProvider(
    process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/'
  );

  const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS || process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS;
  if (!claimRegistryAddress) {
    throw new Error('Claim registry not configured');
  }

  const claimRegistryABI = [
    "function hasValidClaim(address subject, uint256 claimType) external view returns (bool)"
  ];

  const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, provider);
  const claims: number[] = [];

  // Check each claim type
  for (const claimId of Object.values(CLAIM_TYPES)) {
    try {
      const hasValidClaim = await claimRegistry.hasValidClaim(onchainIdAddress, claimId);
      if (hasValidClaim) {
        claims.push(claimId);
      }
    } catch (error) {
      console.warn(`Could not check claim ${claimId} for ${onchainIdAddress}:`, error);
    }
  }

  return claims;
}

// Helper function to get client claims from database (fallback)
async function getClientClaimsFromDatabase(clientId: string): Promise<number[]> {
  const client = await prisma.client.findUnique({
    where: { id: clientId }
  });

  if (!client) return [];

  const claims: number[] = [];

  // Basic claims based on client status
  if (client.kycStatus === 'APPROVED') {
    claims.push(CLAIM_TYPES.KYC_CLAIM);
  }

  if (client.isWhitelisted) {
    claims.push(CLAIM_TYPES.QUALIFICATION_CLAIM);
  }

  // You can add more logic here based on your business rules
  // For example, check qualification progress, specific fields, etc.

  return claims;
}

// POST /api/client-token-eligibility - Batch check eligibility for multiple clients
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { clientIds, walletAddresses } = body;

    if (!clientIds && !walletAddresses) {
      return NextResponse.json(
        { error: 'Either clientIds or walletAddresses array is required' },
        { status: 400 }
      );
    }

    const identifiers = clientIds || walletAddresses;
    const results = [];

    for (const identifier of identifiers) {
      try {
        const searchParams = new URLSearchParams();
        if (clientIds) {
          searchParams.set('clientId', identifier);
        } else {
          searchParams.set('walletAddress', identifier);
        }

        // Reuse the GET logic
        const mockRequest = new Request(`http://localhost/api/client-token-eligibility?${searchParams}`);
        const response = await GET(mockRequest as any);
        const data = await response.json();

        results.push({
          identifier,
          success: response.status === 200,
          data: response.status === 200 ? data : null,
          error: response.status !== 200 ? data.error : null
        });
      } catch (error) {
        results.push({
          identifier,
          success: false,
          data: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      results
    });

  } catch (error) {
    console.error('Error batch checking client token eligibility:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
