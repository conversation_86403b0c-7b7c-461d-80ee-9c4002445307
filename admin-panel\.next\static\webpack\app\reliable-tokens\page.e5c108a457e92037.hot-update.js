"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reliable-tokens/page",{

/***/ "(app-pages-browser)/./src/app/reliable-tokens/page.tsx":
/*!******************************************!*\
  !*** ./src/app/reliable-tokens/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReliableTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var _utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/fallbackProvider */ \"(app-pages-browser)/./src/utils/fallbackProvider.ts\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _components_AgentsList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AgentsList */ \"(app-pages-browser)/./src/components/AgentsList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Default token address from environment (fallback)\nconst DEFAULT_SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\" || 0;\nfunction ReliableTokensContent() {\n    var _tokenInfo_totalSupply, _tokenInfo_maxSupply, _tokenInfo_metadata_tokenPrice, _tokenInfo_metadata, _tokenInfo_metadata1, _tokenInfo_metadata_tokenPrice1, _tokenInfo_metadata2, _tokenInfo_metadata3;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const urlTokenAddress = searchParams.get('token');\n    // Use URL parameter if provided, otherwise fall back to environment variable\n    const SECURITY_TOKEN_CORE_ADDRESS = urlTokenAddress || DEFAULT_SECURITY_TOKEN_CORE_ADDRESS;\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('management');\n    // Whitelist management\n    const [whitelistedClients, setWhitelistedClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [whitelistLoading, setWhitelistLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [whitelistError, setWhitelistError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokenFromDb, setTokenFromDb] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ERC-3643 wrapper detection\n    const knownERC3643Wrappers = [\n        '******************************************',\n        '******************************************',\n        '******************************************'\n    ];\n    const knownUnderlyingToken = '******************************************';\n    const isERC3643Wrapper = knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS);\n    const underlyingTokenAddress = isERC3643Wrapper ? knownUnderlyingToken : SECURITY_TOKEN_CORE_ADDRESS;\n    const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n    // Debug logging\n    console.log('🔍 ReliableTokensContent rendered');\n    console.log('🔍 URL token parameter:', urlTokenAddress);\n    console.log('🔍 Using token address:', SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🔍 Default address:', DEFAULT_SECURITY_TOKEN_CORE_ADDRESS);\n    console.log('🛡️ Is ERC-3643 wrapper:', isERC3643Wrapper);\n    console.log('🔗 Data address for metadata calls:', dataAddress);\n    // Load token information using fallback-first approach\n    const loadTokenInfo = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🔄 Loading token info with fallback-first approach...');\n            // Check if this is an ERC-3643 wrapper\n            const knownERC3643Wrappers = [\n                '******************************************',\n                '******************************************',\n                '******************************************'\n            ];\n            const knownUnderlyingToken = '******************************************';\n            let isERC3643Wrapper = false;\n            let underlyingTokenAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            if (knownERC3643Wrappers.includes(SECURITY_TOKEN_CORE_ADDRESS)) {\n                console.log('🛡️ Detected known ERC-3643 wrapper address');\n                isERC3643Wrapper = true;\n                underlyingTokenAddress = knownUnderlyingToken;\n                console.log('🔗 Using known underlying token for data calls:', underlyingTokenAddress);\n            }\n            // For ERC-3643 wrappers, get basic info from wrapper but maxSupply/metadata from underlying\n            const basicInfoAddress = SECURITY_TOKEN_CORE_ADDRESS;\n            const dataAddress = isERC3643Wrapper ? underlyingTokenAddress : SECURITY_TOKEN_CORE_ADDRESS;\n            console.log('📊 Getting basic info from:', basicInfoAddress);\n            console.log('📊 Getting data (maxSupply, metadata) from:', dataAddress);\n            // Use safe contract calls that automatically handle fallbacks\n            let name, symbol, version, totalSupply, decimals, paused;\n            try {\n                [name, symbol, version, totalSupply, decimals] = await Promise.all([\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'name'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'symbol'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'version'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'totalSupply'),\n                    (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'decimals')\n                ]);\n                // Try to get paused status separately with error handling\n                try {\n                    paused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(basicInfoAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                } catch (pausedError) {\n                    console.log('⚠️ paused() function not available, assuming not paused:', pausedError);\n                    paused = false;\n                }\n            } catch (error) {\n                console.error('❌ Failed to load basic token info:', error);\n                throw new Error(\"Failed to load token information: \".concat(error));\n            }\n            // Get maxSupply and metadata from the appropriate address\n            let maxSupply;\n            let metadata;\n            try {\n                maxSupply = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'maxSupply');\n                console.log('✅ maxSupply loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ maxSupply failed, using 0:', error);\n                maxSupply = BigInt(0);\n            }\n            try {\n                metadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                console.log('✅ metadata loaded from:', dataAddress);\n            } catch (error) {\n                console.log('⚠️ metadata failed, using defaults:', error);\n                metadata = {\n                    tokenPrice: '0',\n                    currency: 'USD',\n                    bonusTiers: '',\n                    tokenDetails: ''\n                };\n            }\n            setTokenInfo({\n                name: name || 'Unknown Token',\n                symbol: symbol || 'UNKNOWN',\n                version: version || '1.0',\n                totalSupply: totalSupply || BigInt(0),\n                maxSupply: maxSupply || BigInt(0),\n                decimals: decimals !== null && decimals !== void 0 ? decimals : 0,\n                paused: paused || false,\n                metadata: {\n                    tokenPrice: (metadata === null || metadata === void 0 ? void 0 : metadata.tokenPrice) || '0',\n                    currency: (metadata === null || metadata === void 0 ? void 0 : metadata.currency) || 'USD',\n                    bonusTiers: (metadata === null || metadata === void 0 ? void 0 : metadata.bonusTiers) || ''\n                }\n            });\n            console.log('✅ Token info loaded successfully');\n        } catch (error) {\n            console.error('❌ Failed to load token info:', error);\n            setError(\"Failed to load token information: \".concat(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Connect wallet\n    const connectWallet = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                await window.ethereum.request({\n                    method: 'eth_requestAccounts'\n                });\n                setWalletConnected(true);\n                setSuccess('Wallet connected successfully');\n            } else {\n                setError('MetaMask not found. Please install MetaMask to use this feature.');\n            }\n        } catch (error) {\n            setError(\"Failed to connect wallet: \".concat(error.message));\n        }\n    };\n    // Debug wallet and transaction setup\n    const debugWalletConnection = async ()=>{\n        try {\n            setError(null);\n            setSuccess(null);\n            console.log('🔍 DEBUGGING WALLET CONNECTION...');\n            if ( false || !window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            // Check accounts\n            const accounts = await window.ethereum.request({\n                method: 'eth_accounts'\n            });\n            console.log('Connected accounts:', accounts);\n            // Check network\n            const chainId = await window.ethereum.request({\n                method: 'eth_chainId'\n            });\n            console.log('Current chain ID:', chainId, '(Expected: 0x13882 for Polygon Amoy)');\n            // Test provider creation\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            const signerAddress = await signer.getAddress();\n            console.log('Signer address:', signerAddress);\n            // Test simple contract call (view function)\n            const viewABI = [\n                \"function name() view returns (string)\"\n            ];\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, viewABI, provider);\n            const tokenName = await contract.name();\n            console.log('Token name (view call):', tokenName);\n            // Test contract with signer (but don't execute)\n            const mintABI = [\n                \"function mint(address to, uint256 amount) external\"\n            ];\n            const contractWithSigner = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, mintABI, signer);\n            // Try gas estimation (this is where it might fail)\n            try {\n                const gasEstimate = await contractWithSigner.mint.estimateGas(signerAddress, 1);\n                console.log('✅ Gas estimation successful:', gasEstimate.toString());\n                setSuccess(\"✅ Wallet connection is working! Gas estimate: \".concat(gasEstimate.toString()));\n            } catch (gasError) {\n                console.error('❌ Gas estimation failed:', gasError);\n                setError(\"Gas estimation failed: \".concat(gasError.message));\n            }\n        } catch (error) {\n            console.error('❌ Debug failed:', error);\n            setError(\"Debug failed: \".concat(error.message));\n        }\n    };\n    // Toggle pause state using fallback-first approach\n    const togglePause = async ()=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            const currentPaused = tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused;\n            const isPausing = !currentPaused;\n            const functionName = isPausing ? 'pause' : 'unpause';\n            console.log(\"\\uD83D\\uDD04 \".concat(isPausing ? 'Pausing' : 'Unpausing', \" token...\"));\n            // Get real-time state to ensure accuracy (with error handling)\n            let realTimePaused;\n            try {\n                realTimePaused = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'paused');\n                // Validate state change is needed\n                if (isPausing && realTimePaused) {\n                    throw new Error('Token is already paused. Refreshing page data...');\n                }\n                if (!isPausing && !realTimePaused) {\n                    throw new Error('Token is already unpaused. Refreshing page data...');\n                }\n            } catch (pausedError) {\n                console.log('⚠️ Cannot check paused status, proceeding with operation:', pausedError);\n            // Continue with the operation if we can't check the current state\n            }\n            // Check if the contract supports pause/unpause functions\n            try {\n                // Execute the transaction using safe transaction approach\n                const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, functionName, []);\n                console.log(\"✅ \".concat(functionName, \" transaction sent: \").concat(tx1.hash));\n                // Wait for confirmation\n                const receipt = await tx1.wait();\n                console.log(\"✅ \".concat(functionName, \" confirmed in block \").concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n                setSuccess(\"Token \".concat(isPausing ? 'paused' : 'unpaused', \" successfully!\"));\n                // Refresh token info\n                await loadTokenInfo();\n            } catch (pauseError) {\n                console.error(\"❌ \".concat(functionName, \" function not supported:\"), pauseError);\n                throw new Error(\"This token contract does not support \".concat(functionName, \" functionality. Contract may not be pausable.\"));\n            }\n        } catch (error) {\n            var _error_message, _error_message1;\n            console.error(\"❌ Toggle pause failed:\", error);\n            if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('already paused')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('already unpaused'))) {\n                setError(\"\".concat(error.message));\n                // Auto-refresh data after state conflict\n                setTimeout(async ()=>{\n                    await loadTokenInfo();\n                    setError(null);\n                }, 2000);\n            } else {\n                setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n            }\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Mint tokens using fallback-first approach\n    const mintTokens = async (amount, recipient)=>{\n        if (!walletConnected) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        try {\n            setActionLoading(true);\n            setError(null);\n            setSuccess(null);\n            console.log(\"\\uD83D\\uDD04 Minting \".concat(amount, \" tokens to \").concat(recipient, \"...\"));\n            // 🚨 CRITICAL SECURITY CHECK: Verify recipient is whitelisted before minting\n            console.log('🔒 SECURITY CHECK: Verifying recipient is whitelisted...');\n            try {\n                // First get the identity registry address using minimal ABI\n                const identityRegistryABI = [\n                    \"function identityRegistryAddress() view returns (address)\"\n                ];\n                const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                if (!identityRegistryAddress) {\n                    throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                }\n                // Then check whitelist status on the identity registry\n                const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                    \"function isWhitelisted(address account) external view returns (bool)\"\n                ], 'isWhitelisted', [\n                    recipient\n                ]);\n                if (!isWhitelisted) {\n                    throw new Error(\"SECURITY VIOLATION: Recipient address \".concat(recipient, \" is not whitelisted. Only whitelisted addresses can receive tokens for ERC-3643 compliance.\"));\n                }\n                console.log('✅ SECURITY PASSED: Recipient is whitelisted, proceeding with mint');\n            } catch (whitelistError) {\n                var _whitelistError_message, _whitelistError_message1;\n                console.error('❌ SECURITY CHECK FAILED:', whitelistError);\n                // Check if this is a contract compatibility issue\n                if (((_whitelistError_message = whitelistError.message) === null || _whitelistError_message === void 0 ? void 0 : _whitelistError_message.includes('execution reverted')) || ((_whitelistError_message1 = whitelistError.message) === null || _whitelistError_message1 === void 0 ? void 0 : _whitelistError_message1.includes('no data present'))) {\n                    console.log('⚠️ Contract does not support ERC-3643 identity registry functionality, proceeding without check');\n                    console.log('🚨 WARNING: This token may not be ERC-3643 compliant!');\n                } else {\n                    throw new Error(\"Security validation failed: \".concat(whitelistError.message));\n                }\n            }\n            // Validate and convert amount\n            console.log('🔍 Amount validation:', {\n                rawAmount: amount,\n                amountType: typeof amount,\n                tokenInfoDecimals: tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals,\n                parsedAmount: parseFloat(amount)\n            });\n            const parsedAmount = parseFloat(amount);\n            if (isNaN(parsedAmount) || parsedAmount <= 0) {\n                throw new Error(\"Invalid amount: \".concat(amount, \". Please enter a positive number.\"));\n            }\n            const decimals = Number((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n            // Convert to BigInt safely\n            let mintAmount;\n            if (decimals > 0) {\n                // For tokens with decimals, multiply by 10^decimals\n                // Convert to string first to avoid floating point precision issues\n                const decimalMultiplier = Math.pow(10, decimals);\n                const scaledAmount = Math.floor(parsedAmount * decimalMultiplier);\n                mintAmount = BigInt(scaledAmount);\n            } else {\n                // For tokens without decimals, use whole number\n                mintAmount = BigInt(Math.floor(parsedAmount));\n            }\n            if (mintAmount <= 0n) {\n                throw new Error(\"Calculated mint amount is invalid: \".concat(mintAmount.toString()));\n            }\n            // Execute mint transaction\n            console.log('🔄 Attempting mint with params:', {\n                address: SECURITY_TOKEN_CORE_ADDRESS,\n                function: 'mint',\n                args: [\n                    recipient,\n                    mintAmount.toString()\n                ],\n                recipient: recipient,\n                amount: amount,\n                mintAmount: mintAmount.toString(),\n                decimals: decimals,\n                mintAmountType: typeof mintAmount\n            });\n            // Try direct browser provider approach for UUPS proxy\n            console.log('🔄 Attempting direct browser provider transaction for UUPS proxy...');\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                const signer = await provider.getSigner();\n                // Verify signer\n                const signerAddress = await signer.getAddress();\n                console.log('🔐 Signer address:', signerAddress);\n                if (signerAddress !== '******************************************') {\n                    throw new Error(\"Wrong signer address. Expected: ******************************************, Got: \".concat(signerAddress));\n                }\n                // Use minimal ABI with direct browser provider\n                const mintABI = [\n                    \"function mint(address to, uint256 amount) external\"\n                ];\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_8__.Contract(SECURITY_TOKEN_CORE_ADDRESS, mintABI, signer);\n                // Try with manual gas settings for UUPS proxy\n                const tx1 = await contract.mint(recipient, mintAmount, {\n                    gasLimit: 200000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_9__.parseUnits('50', 'gwei') // Fixed gas price\n                });\n                console.log('✅ Direct browser provider transaction sent:', tx1.hash);\n                // Wait for confirmation\n                const receipt = await tx1.wait();\n                if (receipt.status === 1) {\n                    setSuccess(\"✅ Successfully minted \".concat(amount, \" tokens to \").concat(recipient, \"! Transaction: \").concat(tx1.hash));\n                    await loadTokenInfo();\n                } else {\n                    setError(\"❌ Transaction failed. Hash: \".concat(tx1.hash));\n                }\n            } else {\n                throw new Error('MetaMask not available');\n            }\n            console.log(\"✅ Mint transaction sent: \".concat(tx.hash));\n            const receipt = await tx.wait();\n            console.log(\"✅ Mint confirmed in block \".concat(receipt === null || receipt === void 0 ? void 0 : receipt.blockNumber));\n            setSuccess(\"Successfully minted \".concat(amount, \" tokens to \").concat(recipient, \"!\"));\n            // Refresh token info\n            await loadTokenInfo();\n        } catch (error) {\n            console.error(\"❌ Mint failed:\", error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Load token from database to get the ID for whitelist queries\n    const loadTokenFromDatabase = async ()=>{\n        if (!SECURITY_TOKEN_CORE_ADDRESS) return;\n        try {\n            const response = await fetch('/api/tokens');\n            if (response.ok) {\n                const data = await response.json();\n                // Handle new API response format\n                const tokens = data.success && Array.isArray(data.tokens) ? data.tokens : Array.isArray(data) ? data : [];\n                // Find token by address\n                const token = tokens.find((t)=>t.address.toLowerCase() === SECURITY_TOKEN_CORE_ADDRESS.toLowerCase());\n                if (token) {\n                    setTokenFromDb(token);\n                    console.log('✅ Token found in database:', token);\n                } else {\n                    console.log('⚠️ Token not found in database, checking all available tokens:', tokens.map((t)=>t.address));\n                }\n            }\n        } catch (error) {\n            console.log('⚠️ Error loading token from database:', error);\n            setWhitelistError('Failed to load token from database');\n        }\n    };\n    // Load whitelisted clients for this token\n    const loadWhitelistedClients = async ()=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) {\n            setWhitelistError('Token not found in database');\n            return;\n        }\n        setWhitelistLoading(true);\n        setWhitelistError(null);\n        try {\n            console.log('🔍 Loading whitelisted clients for token ID:', tokenFromDb.id);\n            const response = await fetch(\"/api/tokens?whitelistedInvestors=true&tokenId=\".concat(tokenFromDb.id));\n            console.log('📥 Response status:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('❌ API Error:', errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📊 API Response:', data);\n            if (data.success) {\n                var _data_investors;\n                setWhitelistedClients(data.investors || []);\n                console.log('✅ Loaded whitelisted clients:', ((_data_investors = data.investors) === null || _data_investors === void 0 ? void 0 : _data_investors.length) || 0);\n            } else {\n                throw new Error(data.error || 'Failed to load whitelisted clients');\n            }\n        } catch (err) {\n            console.error('❌ Error loading whitelisted clients:', err);\n            setWhitelistError(err.message || 'Failed to load whitelisted clients');\n            setWhitelistedClients([]); // Clear the list on error\n        } finally{\n            setWhitelistLoading(false);\n        }\n    };\n    // Remove client from whitelist\n    const handleRemoveFromWhitelist = async (clientId)=>{\n        if (!(tokenFromDb === null || tokenFromDb === void 0 ? void 0 : tokenFromDb.id)) return;\n        if (!confirm('Are you sure you want to remove this client from the whitelist?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/clients/\".concat(clientId, \"/token-approval\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: tokenFromDb.id,\n                    whitelistApproved: false,\n                    approvalStatus: 'REJECTED',\n                    notes: 'Removed from whitelist by admin'\n                })\n            });\n            if (!response.ok) {\n                const data = await response.json();\n                throw new Error(data.error || 'Failed to remove from whitelist');\n            }\n            // Refresh the clients list\n            loadWhitelistedClients();\n            setSuccess('Client removed from whitelist successfully');\n        } catch (err) {\n            console.error('Error removing from whitelist:', err);\n            setError(\"Error: \".concat(err.message));\n        }\n    };\n    // Load token info on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            loadTokenInfo();\n            loadTokenFromDatabase();\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    // Load whitelist when token from DB is available and whitelist tab is active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if (tokenFromDb && activeTab === 'whitelist') {\n                loadWhitelistedClients();\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], [\n        tokenFromDb,\n        activeTab\n    ]);\n    // Check wallet connection on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReliableTokensContent.useEffect\": ()=>{\n            if ( true && window.ethereum) {\n                window.ethereum.request({\n                    method: 'eth_accounts'\n                }).then({\n                    \"ReliableTokensContent.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setWalletConnected(true);\n                        }\n                    }\n                }[\"ReliableTokensContent.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"ReliableTokensContent.useEffect\"], []);\n    var _tokenInfo_decimals;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Reliable Token Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bulletproof token management with fallback-first architecture - no more network errors!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: \"Connection Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-2 \".concat(walletConnected ? 'bg-green-500' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"Wallet: \",\n                                                            walletConnected ? 'Connected' : 'Not Connected'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full bg-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Network: Reliable RPC Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\",\n                                        children: \"Connect Wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, this),\n                                    walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: debugWalletConnection,\n                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg\",\n                                        children: \"Debug Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 mr-2\",\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setError(null),\n                                className: \"text-red-600 hover:text-red-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 704,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 mr-2\",\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSuccess(null),\n                                className: \"text-green-600 hover:text-green-800 text-xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 721,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('management'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'management' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"\\uD83D\\uDEE1️ Token Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('whitelist'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'whitelist' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: [\n                                        \"\\uD83D\\uDC65 Whitelisted Clients\",\n                                        whitelistedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full\",\n                                            children: whitelistedClients.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 738,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Token Information\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: loadTokenInfo,\n                                            disabled: loading,\n                                            className: \"bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50\",\n                                            children: loading ? 'Loading...' : 'Refresh'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 11\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Loading token information...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 13\n                                }, this) : tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.name || 'Unknown'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: tokenInfo.symbol || 'N/A'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Version\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: tokenInfo.version || '1.0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Decimals\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: (_tokenInfo_decimals = tokenInfo.decimals) !== null && _tokenInfo_decimals !== void 0 ? _tokenInfo_decimals : 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Total Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_totalSupply = tokenInfo.totalSupply) === null || _tokenInfo_totalSupply === void 0 ? void 0 : _tokenInfo_totalSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Supply\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: ((_tokenInfo_maxSupply = tokenInfo.maxSupply) === null || _tokenInfo_maxSupply === void 0 ? void 0 : _tokenInfo_maxSupply.toString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                    children: tokenInfo.paused ? '⏸️ Paused' : '▶️ Active'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Token Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((_tokenInfo_metadata = tokenInfo.metadata) === null || _tokenInfo_metadata === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice = _tokenInfo_metadata.tokenPrice) === null || _tokenInfo_metadata_tokenPrice === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice.toString()) || '0',\n                                                        \".00 \",\n                                                        ((_tokenInfo_metadata1 = tokenInfo.metadata) === null || _tokenInfo_metadata1 === void 0 ? void 0 : _tokenInfo_metadata1.currency) || 'USD'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No token information available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Pause Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Token is currently paused. Unpause to allow transfers.' : 'Token is currently active. Pause to stop all transfers.'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: togglePause,\n                                            disabled: actionLoading || !tokenInfo,\n                                            className: \"w-full px-4 py-2 rounded-lg font-medium disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                            children: actionLoading ? 'Processing...' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '▶️ Unpause Token' : '⏸️ Pause Token'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 847,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"mintRecipient\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"mintAmount\",\n                                                            min: \"0\",\n                                                            step: \"0.01\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        const recipientInput = document.getElementById('mintRecipient');\n                                                        const amountInput = document.getElementById('mintAmount');\n                                                        const recipient = recipientInput === null || recipientInput === void 0 ? void 0 : recipientInput.value;\n                                                        const amount = amountInput === null || amountInput === void 0 ? void 0 : amountInput.value;\n                                                        console.log('🔍 Form Debug:', {\n                                                            recipientInput: recipientInput,\n                                                            amountInput: amountInput,\n                                                            recipientValue: recipient,\n                                                            amountValue: amount,\n                                                            amountInputValue: amountInput === null || amountInput === void 0 ? void 0 : amountInput.value,\n                                                            amountInputValueNumber: amountInput === null || amountInput === void 0 ? void 0 : amountInput.valueAsNumber\n                                                        });\n                                                        if (recipient && amount) {\n                                                            mintTokens(amount, recipient);\n                                                        } else {\n                                                            setError('Please enter both recipient address and amount');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading || !tokenInfo,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: actionLoading ? 'Processing...' : '🪙 Mint Tokens'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 867,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"ERC-3643 Whitelist Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium\",\n                                                    children: \"\\uD83D\\uDEE1️ Identity Registry Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 927,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-600 text-lg mr-2\",\n                                                        children: \"ℹ️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ERC-3643 Process:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \" Adding to whitelist automatically registers an OnchainID if needed, then adds to whitelist. This ensures full ERC-3643 compliance.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"whitelistAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Adding to whitelist via API...');\n                                                                        const response = await fetch('/api/admin/add-to-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist add successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist add failed:', error);\n                                                                        setError(\"Failed to add to whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: actionLoading ? 'Processing...' : '✅ Register & Whitelist'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (address) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        console.log('🔄 Removing from whitelist via API...');\n                                                                        const response = await fetch('/api/admin/remove-from-whitelist', {\n                                                                            method: 'POST',\n                                                                            headers: {\n                                                                                'Content-Type': 'application/json'\n                                                                            },\n                                                                            body: JSON.stringify({\n                                                                                tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                                                                                userAddress: address\n                                                                            })\n                                                                        });\n                                                                        const result = await response.json();\n                                                                        if (!response.ok) {\n                                                                            throw new Error(result.error || 'API request failed');\n                                                                        }\n                                                                        setSuccess(\"✅ API Success: \".concat(result.message, \" (Tx: \").concat(result.txHash, \")\"));\n                                                                        console.log('✅ Whitelist remove successful:', result);\n                                                                    } catch (error) {\n                                                                        console.error('❌ Whitelist remove failed:', error);\n                                                                        setError(\"Failed to remove from whitelist: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an address');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                            children: \"❌ Remove (API)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('whitelistAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                try {\n                                                                    // First get the identity registry address using minimal ABI\n                                                                    const identityRegistryABI = [\n                                                                        \"function identityRegistryAddress() view returns (address)\"\n                                                                    ];\n                                                                    const identityRegistryAddress = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, identityRegistryABI, 'identityRegistryAddress', []);\n                                                                    if (!identityRegistryAddress) {\n                                                                        throw new Error('No identity registry found - token may not be ERC-3643 compliant');\n                                                                    }\n                                                                    // Then check whitelist status on the identity registry\n                                                                    const isWhitelisted = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(identityRegistryAddress, [\n                                                                        \"function isWhitelisted(address account) external view returns (bool)\"\n                                                                    ], 'isWhitelisted', [\n                                                                        address\n                                                                    ]);\n                                                                    setSuccess(\"Address \".concat(address, \" is \").concat(isWhitelisted ? '✅ whitelisted' : '❌ not whitelisted', \" (via Identity Registry: \").concat(identityRegistryAddress.slice(0, 10), \"...)\"));\n                                                                } catch (whitelistCheckError) {\n                                                                    var _whitelistCheckError_message, _whitelistCheckError_message1;\n                                                                    // Check if this is a contract compatibility issue\n                                                                    if (((_whitelistCheckError_message = whitelistCheckError.message) === null || _whitelistCheckError_message === void 0 ? void 0 : _whitelistCheckError_message.includes('execution reverted')) || ((_whitelistCheckError_message1 = whitelistCheckError.message) === null || _whitelistCheckError_message1 === void 0 ? void 0 : _whitelistCheckError_message1.includes('no data present'))) {\n                                                                        setError('⚠️ This contract does not support ERC-3643 identity registry functionality.');\n                                                                    } else {\n                                                                        throw whitelistCheckError;\n                                                                    }\n                                                                }\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to check whitelist: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD0D Check Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Balance Checker\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address to Check\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"balanceAddress\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"0x...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1128,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const address = (_document_getElementById = document.getElementById('balanceAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (address) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Validate address format to prevent ENS resolution issues\n                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                }\n                                                                const balance = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'balanceOf', [\n                                                                    address\n                                                                ]);\n                                                                const decimals = Number((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n                                                                const formattedBalance = decimals > 0 ? (Number(balance || 0) / Math.pow(10, decimals)).toString() : (balance === null || balance === void 0 ? void 0 : balance.toString()) || '0';\n                                                                setSuccess(\"Balance: \".concat(formattedBalance, \" \").concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.symbol) || 'tokens'));\n                                                            } catch (error) {\n                                                                // Handle ENS-related errors specifically\n                                                                if (error.message.includes('ENS') || error.message.includes('getEnsAddress') || error.message.includes('UNSUPPORTED_OPERATION')) {\n                                                                    setError('ENS names are not supported on Polygon Amoy. Please use a valid 0x address format.');\n                                                                } else {\n                                                                    setError(\"Failed to get balance: \".concat(error.message));\n                                                                }\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an address');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Check Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1123,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1121,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Burn Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Amount to Burn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1187,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"burnAmount\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1190,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1186,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"⚠️ This will permanently destroy tokens from your wallet. This action cannot be undone.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1197,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const amount = (_document_getElementById = document.getElementById('burnAmount')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (amount) {\n                                                            if (confirm(\"Are you sure you want to burn \".concat(amount, \" tokens? This action cannot be undone.\"))) {\n                                                                try {\n                                                                    setActionLoading(true);\n                                                                    const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n                                                                    const burnAmount = decimals > 0 ? BigInt(amount) * 10n ** BigInt(decimals) : BigInt(amount);\n                                                                    const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'burn', [\n                                                                        burnAmount\n                                                                    ]);\n                                                                    await tx1.wait();\n                                                                    setSuccess(\"Successfully burned \".concat(amount, \" tokens\"));\n                                                                    await loadTokenInfo();\n                                                                } catch (error) {\n                                                                    setError(\"Failed to burn tokens: \".concat(error.message));\n                                                                } finally{\n                                                                    setActionLoading(false);\n                                                                }\n                                                            }\n                                                        } else {\n                                                            setError('Please enter an amount to burn');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDD25 Burn Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1185,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Agent Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Agent Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1246,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"agentAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1249,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1245,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx1.wait();\n                                                                                setSuccess(\"Successfully added \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to add agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➕ Add Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1257,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: async ()=>{\n                                                                        var _document_getElementById;\n                                                                        const address = (_document_getElementById = document.getElementById('agentAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                        if (address) {\n                                                                            try {\n                                                                                setActionLoading(true);\n                                                                                setError(null);\n                                                                                setSuccess(null);\n                                                                                // Validate address format\n                                                                                if (!address.startsWith('0x') || address.length !== 42) {\n                                                                                    throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                                }\n                                                                                // Get AGENT_ROLE hash and use direct role management\n                                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                                const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                                    AGENT_ROLE,\n                                                                                    address\n                                                                                ]);\n                                                                                await tx1.wait();\n                                                                                setSuccess(\"Successfully removed \".concat(address, \" as agent!\"));\n                                                                            } catch (error) {\n                                                                                setError(\"Failed to remove agent: \".concat(error.message));\n                                                                            } finally{\n                                                                                setActionLoading(false);\n                                                                            }\n                                                                        } else {\n                                                                            setError('Please enter an agent address');\n                                                                        }\n                                                                    },\n                                                                    disabled: actionLoading,\n                                                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                                    children: \"➖ Remove Agent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1302,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            try {\n                                                                setActionLoading(true);\n                                                                setError(null);\n                                                                setSuccess(null);\n                                                                // Note: Since AGENT_MANAGER module is not registered, we can't enumerate agents\n                                                                // Instead, we'll show how to check if specific addresses have AGENT_ROLE\n                                                                const AGENT_ROLE = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'AGENT_ROLE', []);\n                                                                // Check some common addresses for demonstration\n                                                                const addressesToCheck = [\n                                                                    '******************************************',\n                                                                    '0x1f1a17fe870f5A0EEf1274247c080478E3d0840A' // Test address\n                                                                ];\n                                                                const agentResults = [];\n                                                                for (const address of addressesToCheck){\n                                                                    try {\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            AGENT_ROLE,\n                                                                            address\n                                                                        ]);\n                                                                        if (hasRole) {\n                                                                            agentResults.push(\"✅ \".concat(address));\n                                                                        }\n                                                                    } catch (error) {\n                                                                    // Skip addresses that cause errors\n                                                                    }\n                                                                }\n                                                                if (agentResults.length === 0) {\n                                                                    setSuccess('No agents found in checked addresses. Use \"Check Role\" to verify specific addresses.');\n                                                                } else {\n                                                                    setSuccess(\"Agents found: \".concat(agentResults.join(', ')));\n                                                                }\n                                                            } catch (error) {\n                                                                setError(\"Failed to get agents: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        },\n                                                        disabled: actionLoading,\n                                                        className: \"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                        children: \"\\uD83D\\uDC65 Check Known Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-3\",\n                                                            children: \"\\uD83D\\uDCCB Current Agents List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1412,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentsList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            tokenAddress: SECURITY_TOKEN_CORE_ADDRESS\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1413,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1411,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1243,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1241,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Role Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1420,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1424,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"roleAddress\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"0x...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1427,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1423,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1435,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"roleSelect\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"AGENT_ROLE\",\n                                                                            children: \"AGENT_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1442,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"TRANSFER_MANAGER_ROLE\",\n                                                                            children: \"TRANSFER_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1443,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"MODULE_MANAGER_ROLE\",\n                                                                            children: \"MODULE_MANAGER_ROLE\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1444,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"DEFAULT_ADMIN_ROLE\",\n                                                                            children: \"DEFAULT_ADMIN_ROLE (⚠️ Dangerous)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1445,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1438,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1434,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1422,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'grantRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx1.wait();\n                                                                        setSuccess(\"Successfully granted \".concat(roleSelect, \" to \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to grant role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"✅ Grant Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1450,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'revokeRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        await tx1.wait();\n                                                                        setSuccess(\"Successfully revoked \".concat(roleSelect, \" from \").concat(address, \"!\"));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to revoke role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"❌ Revoke Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1497,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1;\n                                                                const address = (_document_getElementById = document.getElementById('roleAddress')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const roleSelect = (_document_getElementById1 = document.getElementById('roleSelect')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                if (address && roleSelect) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Validate address format\n                                                                        if (!address.startsWith('0x') || address.length !== 42) {\n                                                                            throw new Error('Please enter a valid Ethereum address (0x... format)');\n                                                                        }\n                                                                        // Get role hash\n                                                                        const roleHash = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, roleSelect, []);\n                                                                        const hasRole = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'hasRole', [\n                                                                            roleHash,\n                                                                            address\n                                                                        ]);\n                                                                        setSuccess(\"Address \".concat(address, \" \").concat(hasRole ? '✅ HAS' : '❌ DOES NOT HAVE', \" \").concat(roleSelect));\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to check role: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter both address and select a role');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDD0D Check Role\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1544,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1449,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"⚠️ Role Descriptions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1593,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1593,\n                                                                columnNumber: 57\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"AGENT_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1594,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can perform basic token operations\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1594,\n                                                                columnNumber: 84\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"TRANSFER_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1595,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can manage transfers and compliance\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1595,\n                                                                columnNumber: 96\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"MODULE_MANAGER_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1596,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Can register/unregister modules\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1596,\n                                                                columnNumber: 90\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DEFAULT_ADMIN_ROLE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1597,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \": Full admin access (use with extreme caution)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1592,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1591,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1421,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Token Metadata\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1605,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Token Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1609,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newTokenPrice\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"100.00 USD\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1612,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1608,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: \"Bonus Tiers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1620,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"newBonusTiers\",\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    placeholder: \"Early: 20%, Standard: 10%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1623,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1619,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1607,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Details/Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1632,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"newTokenDetails\",\n                                                            rows: 3,\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"Token type: Carbon Credits, Debt Securities, Real Estate, etc. Additional details...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1635,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1631,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Token Image URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1643,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"url\",\n                                                            id: \"newTokenImageUrl\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"https://example.com/token-logo.png\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1646,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1642,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById, _document_getElementById1, _document_getElementById2;\n                                                                const price = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                const tiers = (_document_getElementById1 = document.getElementById('newBonusTiers')) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n                                                                const details = (_document_getElementById2 = document.getElementById('newTokenDetails')) === null || _document_getElementById2 === void 0 ? void 0 : _document_getElementById2.value;\n                                                                if (price || tiers || details) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        // Get current values if not provided\n                                                                        const currentPrice = price || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenPrice) || '';\n                                                                        const currentTiers = tiers || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.bonusTiers) || '';\n                                                                        const currentDetails = details || (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.tokenDetails) || '';\n                                                                        const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                            currentPrice,\n                                                                            currentTiers,\n                                                                            currentDetails\n                                                                        ]);\n                                                                        await tx1.wait();\n                                                                        setSuccess('Successfully updated token metadata!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update metadata: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter at least one field to update');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDCDD Update Metadata\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1654,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: async ()=>{\n                                                                var _document_getElementById;\n                                                                const imageUrl = (_document_getElementById = document.getElementById('newTokenImageUrl')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                                if (imageUrl) {\n                                                                    try {\n                                                                        setActionLoading(true);\n                                                                        setError(null);\n                                                                        setSuccess(null);\n                                                                        const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenImageUrl', [\n                                                                            imageUrl\n                                                                        ]);\n                                                                        await tx1.wait();\n                                                                        setSuccess('Successfully updated token image!');\n                                                                        await loadTokenInfo(); // Refresh token info\n                                                                    } catch (error) {\n                                                                        setError(\"Failed to update image: \".concat(error.message));\n                                                                    } finally{\n                                                                        setActionLoading(false);\n                                                                    }\n                                                                } else {\n                                                                    setError('Please enter an image URL');\n                                                                }\n                                                            },\n                                                            disabled: actionLoading,\n                                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                            children: \"\\uD83D\\uDDBC️ Update Image\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1695,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1653,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1606,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1604,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Quick Price Update\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1735,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Current Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1738,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata2 = tokenInfo.metadata) === null || _tokenInfo_metadata2 === void 0 ? void 0 : (_tokenInfo_metadata_tokenPrice1 = _tokenInfo_metadata2.tokenPrice) === null || _tokenInfo_metadata_tokenPrice1 === void 0 ? void 0 : _tokenInfo_metadata_tokenPrice1.toString()) || '0',\n                                                                \".00 \",\n                                                                (tokenInfo === null || tokenInfo === void 0 ? void 0 : (_tokenInfo_metadata3 = tokenInfo.metadata) === null || _tokenInfo_metadata3 === void 0 ? void 0 : _tokenInfo_metadata3.currency) || 'USD'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1741,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"New Price (USD)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1746,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            id: \"newTokenPrice\",\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                            placeholder: \"150\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1749,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1745,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        var _document_getElementById;\n                                                        const newPrice = (_document_getElementById = document.getElementById('newTokenPrice')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n                                                        if (newPrice) {\n                                                            try {\n                                                                setActionLoading(true);\n                                                                console.log('🔄 Updating token price using fallback-first approach...');\n                                                                // Try direct updateTokenPrice first\n                                                                try {\n                                                                    const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenPrice', [\n                                                                        newPrice\n                                                                    ]);\n                                                                    await tx1.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD\"));\n                                                                } catch (directError) {\n                                                                    console.log('Direct method failed, trying fallback...');\n                                                                    // Fallback to updateTokenMetadata\n                                                                    const currentMetadata = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractCall)(dataAddress, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'getTokenMetadata');\n                                                                    const currentBonusTiers = currentMetadata[1];\n                                                                    const currentDetails = currentMetadata[2];\n                                                                    const tx1 = await (0,_utils_fallbackProvider__WEBPACK_IMPORTED_MODULE_4__.safeContractTransaction)(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__.abi, 'updateTokenMetadata', [\n                                                                        newPrice,\n                                                                        currentBonusTiers,\n                                                                        currentDetails\n                                                                    ]);\n                                                                    await tx1.wait();\n                                                                    setSuccess(\"Token price updated to $\".concat(newPrice, \".00 USD (via fallback method)\"));\n                                                                }\n                                                                // Refresh token info\n                                                                await loadTokenInfo();\n                                                            } catch (error) {\n                                                                setError(\"Failed to update token price: \".concat(error.message));\n                                                            } finally{\n                                                                setActionLoading(false);\n                                                            }\n                                                        } else {\n                                                            setError('Please enter a new price');\n                                                        }\n                                                    },\n                                                    disabled: actionLoading,\n                                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50\",\n                                                    children: \"\\uD83D\\uDCB0 Update Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1756,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1734,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded-lg p-6 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"\\uD83D\\uDEE1️ Fallback-First Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1824,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Network Resilience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1827,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Multiple RPC endpoints\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1829,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic failover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1830,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Connection testing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1831,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1828,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1826,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ Error Elimination\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1835,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"missing revert data\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1837,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: '• No \"Internal JSON-RPC\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1838,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Reliable contract calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1839,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1836,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1834,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"✅ User Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1843,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Seamless operation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1845,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Clear error messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1846,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Automatic recovery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1847,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1844,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1842,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1825,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1823,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true),\n                activeTab === 'whitelist' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"\\uD83D\\uDC65 Whitelisted Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1862,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Manage clients who are approved to hold \",\n                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.name) || 'this',\n                                                    \" tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1863,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1861,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: whitelistedClients.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1869,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Whitelisted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1870,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1868,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                                children: \"➕ Add Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1872,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1867,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1860,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1859,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md\",\n                            children: whitelistLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1886,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"Loading whitelisted clients...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1887,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1885,\n                                columnNumber: 17\n                            }, this) : whitelistError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-600 text-xl\",\n                                                children: \"❌\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1893,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-red-800\",\n                                                        children: \"Error Loading Whitelist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1895,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-700\",\n                                                        children: whitelistError\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1896,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1894,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1892,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1891,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1890,\n                                columnNumber: 17\n                            }, this) : whitelistedClients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1903,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No Whitelisted Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1904,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"No clients have been whitelisted for this token yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1905,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/clients\".concat(tokenFromDb ? \"?token=\".concat(tokenFromDb.id) : ''),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium\",\n                                        children: \"➕ Add First Client\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                        lineNumber: 1908,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1902,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1920,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Wallet Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1923,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"KYC Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1926,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Approved Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1929,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                        lineNumber: 1932,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                lineNumber: 1919,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1918,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: whitelistedClients.map((client)=>{\n                                                var _client_tokenApproval;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: [\n                                                                            client.firstName,\n                                                                            \" \",\n                                                                            client.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1942,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: client.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1945,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1941,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1940,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 font-mono\",\n                                                                children: client.walletAddress\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1949,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1948,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(client.kycStatus === 'APPROVED' ? 'bg-green-100 text-green-800' : client.kycStatus === 'REJECTED' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                children: client.kycStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                lineNumber: 1954,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1953,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                            children: ((_client_tokenApproval = client.tokenApproval) === null || _client_tokenApproval === void 0 ? void 0 : _client_tokenApproval.approvedAt) ? new Date(client.tokenApproval.approvedAt).toLocaleDateString() : 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1964,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveFromWhitelist(client.id),\n                                                                    className: \"text-red-600 hover:text-red-900 mr-4\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1970,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/clients/\".concat(client.id),\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1976,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                            lineNumber: 1969,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                                    lineNumber: 1939,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                            lineNumber: 1937,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                    lineNumber: 1917,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                                lineNumber: 1916,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                            lineNumber: 1883,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                    lineNumber: 1857,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 652,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 651,\n        columnNumber: 5\n    }, this);\n}\n_s(ReliableTokensContent, \"mZycVj3G2vFi5jAx3YzfoFENy3g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ReliableTokensContent;\nfunction ReliableTokensPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 2001,\n                        columnNumber: 9\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading token management...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                        lineNumber: 2002,\n                        columnNumber: 9\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n                lineNumber: 2000,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 1999,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReliableTokensContent, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n            lineNumber: 2005,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\reliable-tokens\\\\page.tsx\",\n        lineNumber: 1999,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ReliableTokensPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ReliableTokensContent\");\n$RefreshReg$(_c1, \"ReliableTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reliable-tokens/page.tsx\n"));

/***/ })

});